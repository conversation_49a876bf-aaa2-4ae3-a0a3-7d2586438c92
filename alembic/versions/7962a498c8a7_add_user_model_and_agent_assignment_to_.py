"""Add User model and agent assignment to Conversation

Revision ID: 7962a498c8a7
Revises: 8e48c03e38bc
Create Date: 2025-06-10 21:14:10.738274

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '7962a498c8a7'
down_revision = '8e48c03e38bc'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###

    # Create users table with enum
    op.create_table('users',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('full_name', sa.String(length=255), nullable=True),
    sa.Column('email', sa.String(length=255), nullable=False),
    sa.Column('hashed_password', sa.String(length=255), nullable=False),
    sa.Column('role', sa.Enum('admin', 'agent', name='userrole'), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=True)
    op.create_index(op.f('ix_users_full_name'), 'users', ['full_name'], unique=False)
    op.create_index(op.f('ix_users_id'), 'users', ['id'], unique=False)

    # Add assigned_agent_id column
    op.add_column('conversations', sa.Column('assigned_agent_id', sa.Integer(), nullable=True))

    # Update existing conversations to have 'new' status before changing column type
    op.execute("UPDATE conversations SET status = 'new' WHERE status = 'active'")
    op.execute("UPDATE conversations SET status = 'closed' WHERE status = 'archived'")

    # Create the conversationstatus enum and alter the column
    op.execute("CREATE TYPE conversationstatus AS ENUM ('new', 'open', 'closed')")
    op.execute("ALTER TABLE conversations ALTER COLUMN status TYPE conversationstatus USING status::conversationstatus")

    # Create foreign key
    op.create_foreign_key(None, 'conversations', 'users', ['assigned_agent_id'], ['id'])

    # Drop title column
    op.drop_column('conversations', 'title')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###

    # Add title column back
    op.add_column('conversations', sa.Column('title', sa.VARCHAR(length=255), autoincrement=False, nullable=True))

    # Drop foreign key
    op.drop_constraint(None, 'conversations', type_='foreignkey')

    # Convert status back to varchar
    op.alter_column('conversations', 'status',
               existing_type=sa.Enum('new', 'open', 'closed', name='conversationstatus'),
               type_=sa.VARCHAR(length=50),
               nullable=True)

    # Drop assigned_agent_id column
    op.drop_column('conversations', 'assigned_agent_id')

    # Drop users table and indexes
    op.drop_index(op.f('ix_users_id'), table_name='users')
    op.drop_index(op.f('ix_users_full_name'), table_name='users')
    op.drop_index(op.f('ix_users_email'), table_name='users')
    op.drop_table('users')

    # Drop enum types
    sa.Enum(name='conversationstatus').drop(op.get_bind())
    sa.Enum(name='userrole').drop(op.get_bind())
    # ### end Alembic commands ###
