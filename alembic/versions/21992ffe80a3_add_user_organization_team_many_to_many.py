"""add_user_organization_team_many_to_many

Revision ID: 21992ffe80a3
Revises: 668b16ae8553
Create Date: 2025-07-22 15:41:17.314779

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '21992ffe80a3'
down_revision = '668b16ae8553'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('user_organization_association',
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('organization_id', sa.UUID(), nullable=False),
    sa.ForeignKeyConstraint(['organization_id'], ['organizations.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('user_id', 'organization_id')
    )
    op.create_table('user_team_association',
    sa.Column('user_id', sa.UUI<PERSON>(), nullable=False),
    sa.Column('team_id', sa.UUID(), nullable=False),
    sa.ForeignKeyConstraint(['team_id'], ['teams.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('user_id', 'team_id')
    )
    op.drop_index(op.f('ix_users_organization_id'), table_name='users')
    op.drop_index(op.f('ix_users_team_id'), table_name='users')
    op.drop_constraint(op.f('users_team_id_fkey'), 'users', type_='foreignkey')
    op.drop_constraint(op.f('users_organization_id_fkey'), 'users', type_='foreignkey')
    op.drop_column('users', 'organization_id')
    op.drop_column('users', 'team_id')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('users', sa.Column('team_id', sa.UUID(), autoincrement=False, nullable=True))
    op.add_column('users', sa.Column('organization_id', sa.UUID(), autoincrement=False, nullable=True))
    op.create_foreign_key(op.f('users_organization_id_fkey'), 'users', 'organizations', ['organization_id'], ['id'])
    op.create_foreign_key(op.f('users_team_id_fkey'), 'users', 'teams', ['team_id'], ['id'])
    op.create_index(op.f('ix_users_team_id'), 'users', ['team_id'], unique=False)
    op.create_index(op.f('ix_users_organization_id'), 'users', ['organization_id'], unique=False)
    op.drop_table('user_team_association')
    op.drop_table('user_organization_association')
    # ### end Alembic commands ###
