"""fix_user_organization_id_column

Revision ID: 668b16ae8553
Revises: 462c56826416
Create Date: 2025-07-22 15:33:44.330729

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '668b16ae8553'
down_revision = '462c56826416'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('user_team_association')
    op.drop_table('user_organization_association')
    op.add_column('users', sa.Column('organization_id', sa.UUID(), nullable=True))
    op.add_column('users', sa.Column('team_id', sa.UUID(), nullable=True))
    op.add_column('users', sa.Column('role_id', sa.UUID(), nullable=True))
    op.create_index(op.f('ix_users_organization_id'), 'users', ['organization_id'], unique=False)
    op.create_index(op.f('ix_users_role_id'), 'users', ['role_id'], unique=False)
    op.create_index(op.f('ix_users_team_id'), 'users', ['team_id'], unique=False)
    op.create_foreign_key(None, 'users', 'organizations', ['organization_id'], ['id'])
    op.create_foreign_key(None, 'users', 'teams', ['team_id'], ['id'])
    op.create_foreign_key(None, 'users', 'roles', ['role_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'users', type_='foreignkey')
    op.drop_constraint(None, 'users', type_='foreignkey')
    op.drop_constraint(None, 'users', type_='foreignkey')
    op.drop_index(op.f('ix_users_team_id'), table_name='users')
    op.drop_index(op.f('ix_users_role_id'), table_name='users')
    op.drop_index(op.f('ix_users_organization_id'), table_name='users')
    op.drop_column('users', 'role_id')
    op.drop_column('users', 'team_id')
    op.drop_column('users', 'organization_id')
    op.create_table('user_organization_association',
    sa.Column('user_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('organization_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['organization_id'], ['organizations.id'], name=op.f('user_organization_association_organization_id_fkey')),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], name=op.f('user_organization_association_user_id_fkey')),
    sa.PrimaryKeyConstraint('user_id', 'organization_id', name=op.f('user_organization_association_pkey'))
    )
    op.create_table('user_team_association',
    sa.Column('user_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('team_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['team_id'], ['teams.id'], name=op.f('user_team_association_team_id_fkey')),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], name=op.f('user_team_association_user_id_fkey')),
    sa.PrimaryKeyConstraint('user_id', 'team_id', name=op.f('user_team_association_pkey'))
    )
    # ### end Alembic commands ###
