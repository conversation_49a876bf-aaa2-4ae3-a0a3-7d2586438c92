"""Add deleted field to messages table

Revision ID: 0ab0653408e2
Revises: 1cb751c10ce7
Create Date: 2025-06-12 12:01:59.773680

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '0ab0653408e2'
down_revision = '1cb751c10ce7'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # Add column with default value first, then set NOT NULL
    op.add_column('messages', sa.Column('deleted', sa.<PERSON>(), nullable=True, server_default='false'))
    # Update existing rows to have deleted=false
    op.execute("UPDATE messages SET deleted = false WHERE deleted IS NULL")
    # Now make it NOT NULL
    op.alter_column('messages', 'deleted', nullable=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('messages', 'deleted')
    # ### end Alembic commands ###
