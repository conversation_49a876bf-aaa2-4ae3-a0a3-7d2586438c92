"""Add canned responses (macros) feature

Revision ID: 63c8b9057bca
Revises: f9b81bee6206
Create Date: 2025-06-20 16:21:48.226773

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '63c8b9057bca'
down_revision = 'f9b81bee6206'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('canned_responses',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('title', sa.String(length=255), nullable=False),
    sa.Column('content', sa.Text(), nullable=False),
    sa.Column('shortcut', sa.String(length=50), nullable=True),
    sa.Column('category', sa.String(length=100), nullable=True),
    sa.Column('organization_id', sa.Integer(), nullable=False),
    sa.Column('created_by', sa.Integer(), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('is_public', sa.Boolean(), nullable=False),
    sa.Column('usage_count', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('last_used_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
    sa.ForeignKeyConstraint(['organization_id'], ['organizations.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_canned_responses_category'), 'canned_responses', ['category'], unique=False)
    op.create_index(op.f('ix_canned_responses_created_by'), 'canned_responses', ['created_by'], unique=False)
    op.create_index(op.f('ix_canned_responses_id'), 'canned_responses', ['id'], unique=False)
    op.create_index(op.f('ix_canned_responses_is_active'), 'canned_responses', ['is_active'], unique=False)
    op.create_index(op.f('ix_canned_responses_organization_id'), 'canned_responses', ['organization_id'], unique=False)
    op.create_index(op.f('ix_canned_responses_shortcut'), 'canned_responses', ['shortcut'], unique=False)
    op.create_index(op.f('ix_canned_responses_title'), 'canned_responses', ['title'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_canned_responses_title'), table_name='canned_responses')
    op.drop_index(op.f('ix_canned_responses_shortcut'), table_name='canned_responses')
    op.drop_index(op.f('ix_canned_responses_organization_id'), table_name='canned_responses')
    op.drop_index(op.f('ix_canned_responses_is_active'), table_name='canned_responses')
    op.drop_index(op.f('ix_canned_responses_id'), table_name='canned_responses')
    op.drop_index(op.f('ix_canned_responses_created_by'), table_name='canned_responses')
    op.drop_index(op.f('ix_canned_responses_category'), table_name='canned_responses')
    op.drop_table('canned_responses')
    # ### end Alembic commands ###
