"""Add required database indexes for performance

Revision ID: ec0e81b73d17
Revises: 9e47457303a5
Create Date: 2025-06-13 14:32:00.711095

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'ec0e81b73d17'
down_revision = '9e47457303a5'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index(op.f('ix_conversations_assigned_team_id'), 'conversations', ['assigned_team_id'], unique=False)
    op.create_index(op.f('ix_conversations_status'), 'conversations', ['status'], unique=False)
    op.create_index(op.f('ix_customers_is_deleted'), 'customers', ['is_deleted'], unique=False)
    op.create_index(op.f('ix_messages_asset_id'), 'messages', ['asset_id'], unique=False)
    op.create_index(op.f('ix_messages_conversation_id'), 'messages', ['conversation_id'], unique=False)
    op.create_index(op.f('ix_messages_customer_id'), 'messages', ['customer_id'], unique=False)
    op.create_index(op.f('ix_messages_deleted'), 'messages', ['deleted'], unique=False)
    op.create_index(op.f('ix_messages_user_id'), 'messages', ['user_id'], unique=False)
    op.create_index(op.f('ix_organizations_is_deleted'), 'organizations', ['is_deleted'], unique=False)
    op.create_index(op.f('ix_teams_is_deleted'), 'teams', ['is_deleted'], unique=False)
    op.create_index(op.f('ix_teams_organization_id'), 'teams', ['organization_id'], unique=False)
    op.create_index(op.f('ix_users_is_deleted'), 'users', ['is_deleted'], unique=False)
    op.create_index(op.f('ix_users_organization_id'), 'users', ['organization_id'], unique=False)
    op.create_index(op.f('ix_users_team_id'), 'users', ['team_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_users_team_id'), table_name='users')
    op.drop_index(op.f('ix_users_organization_id'), table_name='users')
    op.drop_index(op.f('ix_users_is_deleted'), table_name='users')
    op.drop_index(op.f('ix_teams_organization_id'), table_name='teams')
    op.drop_index(op.f('ix_teams_is_deleted'), table_name='teams')
    op.drop_index(op.f('ix_organizations_is_deleted'), table_name='organizations')
    op.drop_index(op.f('ix_messages_user_id'), table_name='messages')
    op.drop_index(op.f('ix_messages_deleted'), table_name='messages')
    op.drop_index(op.f('ix_messages_customer_id'), table_name='messages')
    op.drop_index(op.f('ix_messages_conversation_id'), table_name='messages')
    op.drop_index(op.f('ix_messages_asset_id'), table_name='messages')
    op.drop_index(op.f('ix_customers_is_deleted'), table_name='customers')
    op.drop_index(op.f('ix_conversations_status'), table_name='conversations')
    op.drop_index(op.f('ix_conversations_assigned_team_id'), table_name='conversations')
    # ### end Alembic commands ###
