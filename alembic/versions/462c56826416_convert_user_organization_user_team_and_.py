"""Convert user-organization, user-team, and user-role to many-to-many relationships

Revision ID: 462c56826416
Revises: 07d1d2a64925
Create Date: 2025-07-22 14:56:49.574852

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '462c56826416'
down_revision = '07d1d2a64925'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('user_organization_association',
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('organization_id', sa.UUID(), nullable=False),
    sa.ForeignKeyConstraint(['organization_id'], ['organizations.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('user_id', 'organization_id')
    )
    op.create_table('user_team_association',
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('team_id', sa.UUID(), nullable=False),
    sa.ForeignKeyConstraint(['team_id'], ['teams.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('user_id', 'team_id')
    )
    op.drop_index(op.f('ix_users_organization_id'), table_name='users')
    op.drop_index(op.f('ix_users_role_id'), table_name='users')
    op.drop_index(op.f('ix_users_team_id'), table_name='users')
    op.drop_constraint(op.f('users_team_id_fkey'), 'users', type_='foreignkey')
    op.drop_constraint(op.f('users_company_id_fkey'), 'users', type_='foreignkey')
    op.drop_constraint(op.f('users_role_id_fkey'), 'users', type_='foreignkey')
    op.drop_column('users', 'organization_id')
    op.drop_column('users', 'role_id')
    op.drop_column('users', 'team_id')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('users', sa.Column('team_id', sa.UUID(), autoincrement=False, nullable=True))
    op.add_column('users', sa.Column('role_id', sa.UUID(), autoincrement=False, nullable=True))
    op.add_column('users', sa.Column('organization_id', sa.UUID(), autoincrement=False, nullable=True))
    op.create_foreign_key(op.f('users_role_id_fkey'), 'users', 'roles', ['role_id'], ['id'])
    op.create_foreign_key(op.f('users_company_id_fkey'), 'users', 'organizations', ['organization_id'], ['id'])
    op.create_foreign_key(op.f('users_team_id_fkey'), 'users', 'teams', ['team_id'], ['id'])
    op.create_index(op.f('ix_users_team_id'), 'users', ['team_id'], unique=False)
    op.create_index(op.f('ix_users_role_id'), 'users', ['role_id'], unique=False)
    op.create_index(op.f('ix_users_organization_id'), 'users', ['organization_id'], unique=False)
    op.drop_table('user_team_association')
    op.drop_table('user_organization_association')
    # ### end Alembic commands ###
