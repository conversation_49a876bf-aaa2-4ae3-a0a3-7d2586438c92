"""Add default_team_id to Organization

Revision ID: 641a446df1da
Revises: 921e3625655c
Create Date: 2025-06-11 16:18:58.555839

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '641a446df1da'
down_revision = '921e3625655c'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('organizations', sa.Column('default_team_id', sa.Integer(), nullable=True))
    op.create_foreign_key(None, 'organizations', 'teams', ['default_team_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'organizations', type_='foreignkey')
    op.drop_column('organizations', 'default_team_id')
    # ### end Alembic commands ###
