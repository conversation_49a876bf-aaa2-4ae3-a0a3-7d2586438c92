"""Add soft delete fields to user, team, org, customer models

Revision ID: 9e47457303a5
Revises: 0ab0653408e2
Create Date: 2025-06-13 13:14:24.420368

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '9e47457303a5'
down_revision = '0ab0653408e2'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### CORRECTED MIGRATION LOGIC ###

    # --- Add soft delete to 'users' table ---
    op.add_column('users', sa.Column('is_deleted', sa.<PERSON>olean(), server_default='false', nullable=True))
    op.add_column('users', sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True))
    op.execute('UPDATE users SET is_deleted = false WHERE is_deleted IS NULL')
    op.alter_column('users', 'is_deleted', nullable=False)

    # --- Add soft delete to 'teams' table ---
    op.add_column('teams', sa.Column('is_deleted', sa.<PERSON>(), server_default='false', nullable=True))
    op.add_column('teams', sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True))
    op.execute('UPDATE teams SET is_deleted = false WHERE is_deleted IS NULL')
    op.alter_column('teams', 'is_deleted', nullable=False)

    # --- Add soft delete to 'organizations' table ---
    op.add_column('organizations', sa.Column('is_deleted', sa.Boolean(), server_default='false', nullable=True))
    op.add_column('organizations', sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True))
    op.execute('UPDATE organizations SET is_deleted = false WHERE is_deleted IS NULL')
    op.alter_column('organizations', 'is_deleted', nullable=False)

    # --- Add soft delete to 'customers' table ---
    op.add_column('customers', sa.Column('is_deleted', sa.Boolean(), server_default='false', nullable=True))
    op.add_column('customers', sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True))
    op.execute('UPDATE customers SET is_deleted = false WHERE is_deleted IS NULL')
    op.alter_column('customers', 'is_deleted', nullable=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### CORRECTED DOWNGRADE LOGIC ###
    op.drop_column('customers', 'deleted_at')
    op.drop_column('customers', 'is_deleted')
    op.drop_column('organizations', 'deleted_at')
    op.drop_column('organizations', 'is_deleted')
    op.drop_column('teams', 'deleted_at')
    op.drop_column('teams', 'is_deleted')
    op.drop_column('users', 'deleted_at')
    op.drop_column('users', 'is_deleted')
    # ### end Alembic commands ###
