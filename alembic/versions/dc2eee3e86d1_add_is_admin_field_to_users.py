"""Add is_admin field to users

Revision ID: dc2eee3e86d1
Revises: d540f27c1dae
Create Date: 2025-06-19 19:59:17.906025

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'dc2eee3e86d1'
down_revision = 'd540f27c1dae'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index(op.f('ix_conversations_assigned_team_id'), 'conversations', ['assigned_team_id'], unique=False)
    op.create_index(op.f('ix_conversations_status'), 'conversations', ['status'], unique=False)
    op.alter_column('customers', 'organization_id',
               existing_type=sa.INTEGER(),
               nullable=False)
    op.create_index(op.f('ix_customers_is_deleted'), 'customers', ['is_deleted'], unique=False)
    op.create_index(op.f('ix_customers_organization_id'), 'customers', ['organization_id'], unique=False)
    op.create_index(op.f('ix_messages_asset_id'), 'messages', ['asset_id'], unique=False)
    op.create_index(op.f('ix_messages_conversation_id'), 'messages', ['conversation_id'], unique=False)
    op.create_index(op.f('ix_messages_customer_id'), 'messages', ['customer_id'], unique=False)
    op.create_index(op.f('ix_messages_deleted'), 'messages', ['deleted'], unique=False)
    op.create_index(op.f('ix_messages_user_id'), 'messages', ['user_id'], unique=False)
    op.create_index(op.f('ix_organizations_is_deleted'), 'organizations', ['is_deleted'], unique=False)
    op.create_index(op.f('ix_teams_is_deleted'), 'teams', ['is_deleted'], unique=False)
    op.create_index(op.f('ix_teams_organization_id'), 'teams', ['organization_id'], unique=False)
    # Add is_admin column with default value first
    op.add_column('users', sa.Column('is_admin', sa.Boolean(), nullable=True, default=False))

    # Update existing users: set is_admin=True for users with role='admin', False for others
    op.execute("UPDATE users SET is_admin = CASE WHEN role = 'admin' THEN true ELSE false END")

    # Now make the column NOT NULL
    op.alter_column('users', 'is_admin', nullable=False)
    op.create_index(op.f('ix_users_is_admin'), 'users', ['is_admin'], unique=False)
    op.create_index(op.f('ix_users_is_deleted'), 'users', ['is_deleted'], unique=False)
    op.create_index(op.f('ix_users_organization_id'), 'users', ['organization_id'], unique=False)
    op.create_index(op.f('ix_users_team_id'), 'users', ['team_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_users_team_id'), table_name='users')
    op.drop_index(op.f('ix_users_organization_id'), table_name='users')
    op.drop_index(op.f('ix_users_is_deleted'), table_name='users')
    op.drop_index(op.f('ix_users_is_admin'), table_name='users')
    op.drop_column('users', 'is_admin')
    op.drop_index(op.f('ix_teams_organization_id'), table_name='teams')
    op.drop_index(op.f('ix_teams_is_deleted'), table_name='teams')
    op.drop_index(op.f('ix_organizations_is_deleted'), table_name='organizations')
    op.drop_index(op.f('ix_messages_user_id'), table_name='messages')
    op.drop_index(op.f('ix_messages_deleted'), table_name='messages')
    op.drop_index(op.f('ix_messages_customer_id'), table_name='messages')
    op.drop_index(op.f('ix_messages_conversation_id'), table_name='messages')
    op.drop_index(op.f('ix_messages_asset_id'), table_name='messages')
    op.drop_index(op.f('ix_customers_organization_id'), table_name='customers')
    op.drop_index(op.f('ix_customers_is_deleted'), table_name='customers')
    op.alter_column('customers', 'organization_id',
               existing_type=sa.INTEGER(),
               nullable=True)
    op.drop_index(op.f('ix_conversations_status'), table_name='conversations')
    op.drop_index(op.f('ix_conversations_assigned_team_id'), table_name='conversations')
    # ### end Alembic commands ###
