"""Add dynamic role management with many-to-many relationship

Revision ID: f9b81bee6206
Revises: dc2eee3e86d1
Create Date: 2025-06-20 15:52:46.401061

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'f9b81bee6206'
down_revision = 'dc2eee3e86d1'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('roles',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=50), nullable=False),
    sa.Column('description', sa.String(length=255), nullable=True),
    sa.Column('is_active', sa.<PERSON>(), nullable=False),
    sa.Column('is_system_role', sa.<PERSON>an(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_roles_id'), 'roles', ['id'], unique=False)
    op.create_index(op.f('ix_roles_name'), 'roles', ['name'], unique=True)
    op.create_table('user_role_association',
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('role_id', sa.Integer(), nullable=False),
    sa.Column('assigned_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('assigned_by', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['assigned_by'], ['users.id'], ),
    sa.ForeignKeyConstraint(['role_id'], ['roles.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('user_id', 'role_id')
    )

    # ### NEW: Insert default system roles ###
    roles_table = sa.table('roles',
        sa.column('name', sa.String),
        sa.column('description', sa.String),
        sa.column('is_active', sa.Boolean),
        sa.column('is_system_role', sa.Boolean)
    )

    op.bulk_insert(roles_table, [
        {
            'name': 'Admin',
            'description': 'Full system access and administration',
            'is_active': True,
            'is_system_role': True
        },
        {
            'name': 'Manager',
            'description': 'Team and user management capabilities',
            'is_active': True,
            'is_system_role': True
        },
        {
            'name': 'Agent',
            'description': 'Standard customer support agent',
            'is_active': True,
            'is_system_role': True
        },
        {
            'name': 'Supervisor',
            'description': 'Supervises agents and handles escalations',
            'is_active': True,
            'is_system_role': True
        },
        {
            'name': 'HR',
            'description': 'Human Resources management',
            'is_active': True,
            'is_system_role': True
        },
        {
            'name': 'Viewer',
            'description': 'Read-only access to system data',
            'is_active': True,
            'is_system_role': True
        }
    ])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('user_role_association')
    op.drop_index(op.f('ix_roles_name'), table_name='roles')
    op.drop_index(op.f('ix_roles_id'), table_name='roles')
    op.drop_table('roles')
    # ### end Alembic commands ###
