"""Restore role and is_admin columns for backward compatibility

Revision ID: 62901b880fa5
Revises: e633220c2ad0
Create Date: 2025-06-21 10:08:42.459385

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '62901b880fa5'
down_revision = 'e633220c2ad0'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###

    # First, add columns with default values (nullable)
    op.add_column('users', sa.Column('role', sa.Enum('agent', 'admin', name='userrole'), nullable=True))
    op.add_column('users', sa.Column('is_admin', sa.<PERSON>(), nullable=True))

    # Update all existing users to have default values
    op.execute("UPDATE users SET role = 'agent' WHERE role IS NULL")
    op.execute("UPDATE users SET is_admin = false WHERE is_admin IS NULL")

    # Now make the columns NOT NULL
    op.alter_column('users', 'role', nullable=False)
    op.alter_column('users', 'is_admin', nullable=False)

    # Create index
    op.create_index(op.f('ix_users_is_admin'), 'users', ['is_admin'], unique=False)

    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_users_is_admin'), table_name='users')
    op.drop_column('users', 'is_admin')
    op.drop_column('users', 'role')
    # ### end Alembic commands ###
