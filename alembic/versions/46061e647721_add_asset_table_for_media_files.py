"""Add Asset table for media files

Revision ID: 46061e647721
Revises: 641a446df1da
Create Date: 2025-06-12 10:58:04.474156

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '46061e647721'
down_revision = '641a446df1da'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('assets',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('filename', sa.String(), nullable=False),
    sa.Column('original_filename', sa.String(), nullable=False),
    sa.Column('file_type', sa.String(), nullable=False),
    sa.Column('mime_type', sa.String(), nullable=False),
    sa.Column('file_size', sa.BigInteger(), nullable=False),
    sa.Column('s3_bucket', sa.String(), nullable=False),
    sa.Column('s3_key', sa.String(), nullable=False),
    sa.Column('s3_url', sa.Text(), nullable=False),
    sa.Column('width', sa.Integer(), nullable=True),
    sa.Column('height', sa.Integer(), nullable=True),
    sa.Column('duration', sa.Integer(), nullable=True),
    sa.Column('thumbnail_s3_key', sa.String(), nullable=True),
    sa.Column('thumbnail_s3_url', sa.Text(), nullable=True),
    sa.Column('is_processed', sa.Boolean(), nullable=True),
    sa.Column('file_metadata', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_assets_id'), 'assets', ['id'], unique=False)
    op.add_column('messages', sa.Column('asset_id', sa.Integer(), nullable=True))
    op.alter_column('messages', 'content',
               existing_type=sa.TEXT(),
               nullable=True)
    op.create_foreign_key(None, 'messages', 'assets', ['asset_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'messages', type_='foreignkey')
    op.alter_column('messages', 'content',
               existing_type=sa.TEXT(),
               nullable=False)
    op.drop_column('messages', 'asset_id')
    op.drop_index(op.f('ix_assets_id'), table_name='assets')
    op.drop_table('assets')
    # ### end Alembic commands ###
