"""Add organization_id to customer model

Revision ID: d540f27c1dae
Revises: ec0e81b73d17
Create Date: 2025-06-16 15:35:57.645962

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'd540f27c1dae'
down_revision = 'ec0e81b73d17'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### CORRECTED MIGRATION LOGIC ###

    # Step 1: Add the organization_id column, but allow it to be NULL for now.
    op.add_column('customers', sa.Column('organization_id', sa.Integer(), nullable=True))
    
    # Step 2: Set a default value for all existing customers.
    # We will assign all existing customers to the default organization (ID=1).
    # This is a safe assumption for our development setup.
    op.execute('UPDATE customers SET organization_id = 1 WHERE organization_id IS NULL')

    # Step 3: Now that all rows have a value, alter the column to be NOT NULL.
    op.alter_column('customers', 'organization_id', nullable=False)

    # Step 4: Create the index and foreign key constraint.
    op.create_index(op.f('ix_customers_organization_id'), 'customers', ['organization_id'], unique=False)
    op.create_foreign_key(
        'fk_customers_organization_id_organizations', # A descriptive constraint name
        'customers', 'organizations',
        ['organization_id'], ['id']
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### CORRECTED DOWNGRADE LOGIC ###
    op.drop_constraint('fk_customers_organization_id_organizations', 'customers', type_='foreignkey')
    op.drop_index(op.f('ix_customers_organization_id'), table_name='customers')
    op.drop_column('customers', 'organization_id')
    # ### end Alembic commands ###