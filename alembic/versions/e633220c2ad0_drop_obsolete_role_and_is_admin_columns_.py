"""Drop obsolete role and is_admin columns from user table

Revision ID: e633220c2ad0
Revises: 63c8b9057bca
Create Date: 2025-06-20 21:38:42.763948

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'e633220c2ad0'
down_revision = '63c8b9057bca'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_users_is_admin'), table_name='users')
    op.drop_column('users', 'is_admin')
    op.drop_column('users', 'role')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('users', sa.Column('role', postgresql.ENUM('admin', 'agent', name='userrole'), autoincrement=False, nullable=False))
    op.add_column('users', sa.Column('is_admin', sa.BOOLEAN(), autoincrement=False, nullable=False))
    op.create_index(op.f('ix_users_is_admin'), 'users', ['is_admin'], unique=False)
    # ### end Alembic commands ###
