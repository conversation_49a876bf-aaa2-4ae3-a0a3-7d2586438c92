"""Major refactor: Add Organization, Customer, Team models and update relationships

Revision ID: 921e3625655c
Revises: 7962a498c8a7
Create Date: 2025-06-11 12:43:53.136912

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '921e3625655c'
down_revision = '7962a498c8a7'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('customers',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('customer_id', sa.String(length=100), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=True),
    sa.Column('email', sa.String(length=255), nullable=True),
    sa.Column('phone', sa.String(length=50), nullable=True),
    sa.Column('ip_address', sa.String(length=45), nullable=True),
    sa.Column('location', sa.String(length=255), nullable=True),
    sa.Column('user_agent', sa.Text(), nullable=True),
    sa.Column('customer_metadata', sa.Text(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_customers_customer_id'), 'customers', ['customer_id'], unique=True)
    op.create_index(op.f('ix_customers_email'), 'customers', ['email'], unique=False)
    op.create_index(op.f('ix_customers_id'), 'customers', ['id'], unique=False)
    op.create_table('organizations',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('website', sa.String(length=255), nullable=True),
    sa.Column('email', sa.String(length=255), nullable=True),
    sa.Column('phone', sa.String(length=50), nullable=True),
    sa.Column('address', sa.Text(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_organizations_id'), 'organizations', ['id'], unique=False)
    op.create_index(op.f('ix_organizations_name'), 'organizations', ['name'], unique=False)
    op.create_table('teams',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('organization_id', sa.Integer(), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['organization_id'], ['organizations.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_teams_id'), 'teams', ['id'], unique=False)
    op.create_index(op.f('ix_teams_name'), 'teams', ['name'], unique=False)

    # Create a default organization first
    op.execute("INSERT INTO organizations (name, description, is_active) VALUES ('Default Organization', 'Default organization for migration', true)")

    # Create default customers for existing conversations
    op.execute("""
        INSERT INTO customers (customer_id, name, is_active)
        SELECT DISTINCT user_id, 'Migrated Customer - ' || user_id, true
        FROM conversations
        WHERE user_id IS NOT NULL
    """)

    # Add columns as nullable first
    op.add_column('conversations', sa.Column('customer_id', sa.Integer(), nullable=True))
    op.add_column('conversations', sa.Column('organization_id', sa.Integer(), nullable=True))
    op.add_column('conversations', sa.Column('assigned_team_id', sa.Integer(), nullable=True))

    # Update existing conversations with default values
    op.execute("""
        UPDATE conversations
        SET customer_id = customers.id, organization_id = 1
        FROM customers
        WHERE conversations.user_id = customers.customer_id
    """)

    # Now make the columns NOT NULL
    op.alter_column('conversations', 'customer_id', nullable=False)
    op.alter_column('conversations', 'organization_id', nullable=False)
    op.alter_column('conversations', 'status',
               existing_type=postgresql.ENUM('new', 'open', 'closed', name='conversationstatus'),
               nullable=False)
    op.drop_index(op.f('ix_conversations_user_id'), table_name='conversations')
    op.create_index(op.f('ix_conversations_customer_id'), 'conversations', ['customer_id'], unique=False)
    op.create_index(op.f('ix_conversations_organization_id'), 'conversations', ['organization_id'], unique=False)
    op.drop_constraint(op.f('conversations_assigned_agent_id_fkey'), 'conversations', type_='foreignkey')
    op.create_foreign_key(None, 'conversations', 'customers', ['customer_id'], ['id'])
    op.create_foreign_key(None, 'conversations', 'organizations', ['organization_id'], ['id'])
    op.create_foreign_key(None, 'conversations', 'teams', ['assigned_team_id'], ['id'])
    op.drop_column('conversations', 'user_id')
    op.drop_column('conversations', 'assigned_agent_id')
    op.add_column('messages', sa.Column('customer_id', sa.Integer(), nullable=True))
    op.add_column('messages', sa.Column('user_id', sa.Integer(), nullable=True))
    op.add_column('messages', sa.Column('ip_address', sa.String(length=45), nullable=True))
    op.add_column('messages', sa.Column('location', sa.String(length=255), nullable=True))
    op.create_foreign_key(None, 'messages', 'customers', ['customer_id'], ['id'])
    op.create_foreign_key(None, 'messages', 'users', ['user_id'], ['id'])
    op.drop_column('messages', 'message_metadata')
    op.add_column('users', sa.Column('organization_id', sa.Integer(), nullable=True))
    op.add_column('users', sa.Column('team_id', sa.Integer(), nullable=True))
    op.add_column('users', sa.Column('is_active', sa.Boolean(), nullable=True))

    # Set default value for is_active
    op.execute("UPDATE users SET is_active = true WHERE is_active IS NULL")

    # Make is_active NOT NULL
    op.alter_column('users', 'is_active', nullable=False)
    op.create_foreign_key(None, 'users', 'teams', ['team_id'], ['id'])
    op.create_foreign_key(None, 'users', 'organizations', ['organization_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'users', type_='foreignkey')
    op.drop_constraint(None, 'users', type_='foreignkey')
    op.drop_column('users', 'is_active')
    op.drop_column('users', 'team_id')
    op.drop_column('users', 'organization_id')
    op.add_column('messages', sa.Column('message_metadata', sa.TEXT(), autoincrement=False, nullable=True))
    op.drop_constraint(None, 'messages', type_='foreignkey')
    op.drop_constraint(None, 'messages', type_='foreignkey')
    op.drop_column('messages', 'location')
    op.drop_column('messages', 'ip_address')
    op.drop_column('messages', 'user_id')
    op.drop_column('messages', 'customer_id')
    op.add_column('conversations', sa.Column('assigned_agent_id', sa.INTEGER(), autoincrement=False, nullable=True))
    op.add_column('conversations', sa.Column('user_id', sa.VARCHAR(length=100), autoincrement=False, nullable=False))
    op.drop_constraint(None, 'conversations', type_='foreignkey')
    op.drop_constraint(None, 'conversations', type_='foreignkey')
    op.drop_constraint(None, 'conversations', type_='foreignkey')
    op.create_foreign_key(op.f('conversations_assigned_agent_id_fkey'), 'conversations', 'users', ['assigned_agent_id'], ['id'])
    op.drop_index(op.f('ix_conversations_organization_id'), table_name='conversations')
    op.drop_index(op.f('ix_conversations_customer_id'), table_name='conversations')
    op.create_index(op.f('ix_conversations_user_id'), 'conversations', ['user_id'], unique=False)
    op.alter_column('conversations', 'status',
               existing_type=postgresql.ENUM('new', 'open', 'closed', name='conversationstatus'),
               nullable=True)
    op.drop_column('conversations', 'assigned_team_id')
    op.drop_column('conversations', 'organization_id')
    op.drop_column('conversations', 'customer_id')
    op.drop_index(op.f('ix_teams_name'), table_name='teams')
    op.drop_index(op.f('ix_teams_id'), table_name='teams')
    op.drop_table('teams')
    op.drop_index(op.f('ix_organizations_name'), table_name='organizations')
    op.drop_index(op.f('ix_organizations_id'), table_name='organizations')
    op.drop_table('organizations')
    op.drop_index(op.f('ix_customers_id'), table_name='customers')
    op.drop_index(op.f('ix_customers_email'), table_name='customers')
    op.drop_index(op.f('ix_customers_customer_id'), table_name='customers')
    op.drop_table('customers')
    # ### end Alembic commands ###
