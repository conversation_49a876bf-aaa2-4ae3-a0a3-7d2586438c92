<template>
  <div id="app" class="app-container">
    <!-- Dark Sidebar -->
    <aside class="sidebar" v-if="isAuthenticated" :class="{ 'sidebar-collapsed': sidebarCollapsed }">
      <div class="sidebar-header">
        <div class="sidebar-brand">
          <i class="fas fa-robot"></i>
          <span v-if="!sidebarCollapsed">Yupcha AI</span>
        </div>
        <button @click="toggleSidebar" class="sidebar-toggle">
          <i :class="sidebarCollapsed ? 'fas fa-chevron-right' : 'fas fa-chevron-left'"></i>
        </button>
      </div>

      <nav class="sidebar-nav">
        <router-link to="/dashboard" class="nav-item">
          <i class="fas fa-tachometer-alt"></i>
          <span v-if="!sidebarCollapsed">Dashboard</span>
        </router-link>
        <router-link to="/conversations" class="nav-item">
          <i class="fas fa-comments"></i>
          <span v-if="!sidebarCollapsed">Conversations</span>
        </router-link>
        <router-link to="/chat" class="nav-item">
          <i class="fas fa-comment-dots"></i>
          <span v-if="!sidebarCollapsed">Live Chat</span>
        </router-link>
        <router-link to="/customers" class="nav-item">
          <i class="fas fa-users"></i>
          <span v-if="!sidebarCollapsed">Customers</span>
        </router-link>

        <div v-if="user?.is_admin" class="nav-section">
          <div class="nav-section-title" v-if="!sidebarCollapsed">
            <i class="fas fa-cog"></i>
            <span>Administration</span>
          </div>
          <router-link to="/teams" class="nav-item">
            <i class="fas fa-user-friends"></i>
            <span v-if="!sidebarCollapsed">Teams</span>
          </router-link>
          <router-link to="/organizations" class="nav-item">
            <i class="fas fa-building"></i>
            <span v-if="!sidebarCollapsed">Organizations</span>
          </router-link>
          <router-link to="/users" class="nav-item">
            <i class="fas fa-user-cog"></i>
            <span v-if="!sidebarCollapsed">Users</span>
          </router-link>
          <router-link to="/permissions" class="nav-item">
            <i class="fas fa-shield-alt"></i>
            <span v-if="!sidebarCollapsed">Permissions</span>
          </router-link>
        </div>
      </nav>

      <div class="sidebar-footer">
        <div class="user-profile">
          <div class="user-avatar">
            <i class="fas fa-user"></i>
          </div>
          <div class="user-details" v-if="!sidebarCollapsed">
            <span class="user-name">{{ user?.full_name }}</span>
            <span class="user-role">{{ user?.role }}</span>
          </div>
        </div>
        <button @click="logout" class="logout-btn" :title="sidebarCollapsed ? 'Logout' : ''">
          <i class="fas fa-sign-out-alt"></i>
          <span v-if="!sidebarCollapsed">Logout</span>
        </button>
      </div>
    </aside>

    <!-- Main Content -->
    <main class="main-content" :class="{
      'no-sidebar': !isAuthenticated,
      'sidebar-collapsed': sidebarCollapsed && isAuthenticated
    }">
      <router-view></router-view>
    </main>

    <!-- Loading Overlay -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-spinner">
        <i class="fas fa-spinner fa-spin"></i>
        <p>Loading...</p>
      </div>
    </div>

    <!-- Toast Notifications -->
    <div class="toast-container">
      <div v-for="toast in toasts" :key="toast.id"
           :class="['toast', `toast-${toast.type}`]"
           @click="removeToast(toast.id)">
        <i :class="getToastIcon(toast.type)"></i>
        <span>{{ toast.message }}</span>
        <button class="toast-close">&times;</button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, getCurrentInstance } from 'vue'
import { useRouter } from 'vue-router'
import axios from 'axios'

export default {
  name: 'App',
  setup() {
    const router = useRouter()

    // Global state
    const globalState = reactive({
      user: null,
      isAuthenticated: false,
      loading: false,
      toasts: []
    })

    // Sidebar state
    const sidebarCollapsed = ref(false)

    const isAuthenticated = computed(() => globalState.isAuthenticated)
    const user = computed(() => globalState.user)
    const loading = computed(() => globalState.loading)
    const toasts = computed(() => globalState.toasts)

    // Toast system
    let toastId = 0
    const showToast = (message, type = 'info') => {
      const toast = {
        id: ++toastId,
        message,
        type
      }
      globalState.toasts.push(toast)

      setTimeout(() => {
        removeToast(toast.id)
      }, 5000)
    }

    const removeToast = (id) => {
      const index = globalState.toasts.findIndex(t => t.id === id)
      if (index > -1) {
        globalState.toasts.splice(index, 1)
      }
    }

    const getToastIcon = (type) => {
      const icons = {
        success: 'fas fa-check-circle',
        error: 'fas fa-exclamation-circle',
        warning: 'fas fa-exclamation-triangle',
        info: 'fas fa-info-circle'
      }
      return icons[type] || icons.info
    }

    // Sidebar methods
    const toggleSidebar = () => {
      sidebarCollapsed.value = !sidebarCollapsed.value
    }

    // Auth methods
    const logout = async () => {
      try {
        globalState.loading = true
        await axios.post('/api/auth/logout')
        globalState.user = null
        globalState.isAuthenticated = false
        showToast('Logged out successfully', 'success')
        router.push('/login')
      } catch (error) {
        showToast('Logout failed', 'error')
      } finally {
        globalState.loading = false
      }
    }

    // Check authentication on app load
    onMounted(async () => {
      try {
        const response = await axios.get('/api/auth/me')
        globalState.user = response.data
        globalState.isAuthenticated = true
        if (router.currentRoute.value.path === '/login') {
          router.push('/dashboard')
        }
      } catch (error) {
        // User not authenticated
      }
    })

    // Provide global state to all components
    const app = getCurrentInstance()
    app.appContext.config.globalProperties.$globalState = globalState
    app.appContext.config.globalProperties.$showToast = showToast

    return {
      isAuthenticated,
      user,
      loading,
      toasts,
      sidebarCollapsed,
      toggleSidebar,
      logout,
      getToastIcon,
      removeToast
    }
  }
}
</script>
