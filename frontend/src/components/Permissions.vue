<template>
  <div class="permissions-container">
    <div class="header">
      <h1>
        <i class="fas fa-shield-alt"></i>
        Permission Management
      </h1>
      <p class="subtitle">Casbin-based Attribute Access Control (ABAC) System</p>
    </div>

    <!-- Permission Check Tool -->
    <div class="card">
      <div class="card-header">
        <h2><i class="fas fa-check-circle"></i> Check Permission</h2>
      </div>
      <div class="card-body">
        <div class="form-row">
          <div class="form-group">
            <label for="resource">Resource</label>
            <input 
              type="text" 
              id="resource" 
              v-model="permissionCheck.resource" 
              placeholder="e.g., users, organization:1, conversation:123"
            >
          </div>
          <div class="form-group">
            <label for="action">Action</label>
            <select id="action" v-model="permissionCheck.action">
              <option value="">Select action...</option>
              <option v-for="action in availableActions" :key="action" :value="action">
                {{ action }}
              </option>
            </select>
          </div>
          <div class="form-group">
            <button @click="checkPermission" :disabled="!permissionCheck.resource || !permissionCheck.action">
              <i class="fas fa-search"></i>
              Check Permission
            </button>
          </div>
        </div>
        
        <div v-if="permissionResult" class="permission-result">
          <div :class="['result-badge', permissionResult.allowed ? 'allowed' : 'denied']">
            <i :class="permissionResult.allowed ? 'fas fa-check' : 'fas fa-times'"></i>
            {{ permissionResult.allowed ? 'ALLOWED' : 'DENIED' }}
          </div>
          <div class="result-details">
            <p><strong>User:</strong> {{ permissionResult.user_email }}</p>
            <p><strong>Roles:</strong> {{ permissionResult.user_roles.join(', ') }}</p>
            <p><strong>Resource:</strong> {{ permissionResult.resource }}</p>
            <p><strong>Action:</strong> {{ permissionResult.action }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- My Permissions -->
    <div class="card">
      <div class="card-header">
        <h2><i class="fas fa-user-shield"></i> My Permissions</h2>
        <button @click="loadMyPermissions" class="btn-refresh">
          <i class="fas fa-sync-alt"></i>
          Refresh
        </button>
      </div>
      <div class="card-body">
        <div v-if="myPermissions" class="permissions-display">
          <div class="user-info">
            <h3>{{ myPermissions.user_email }}</h3>
            <div class="roles">
              <span v-for="role in myPermissions.roles" :key="role" class="role-badge">
                {{ role }}
              </span>
            </div>
          </div>
          
          <div class="permissions-list">
            <h4>Direct Permissions:</h4>
            <div v-if="myPermissions.permissions.length === 0" class="no-permissions">
              No direct permissions. Access is granted through roles.
            </div>
            <div v-else>
              <div v-for="(permission, index) in myPermissions.permissions" :key="index" class="permission-item">
                <span class="permission-subject">{{ permission[0] }}</span>
                <span class="permission-object">{{ permission[1] }}</span>
                <span class="permission-action">{{ permission[2] }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Permission Matrix (Admin Only) -->
    <div v-if="isAdmin" class="card">
      <div class="card-header">
        <h2><i class="fas fa-table"></i> Permission Matrix</h2>
        <button @click="loadPermissionMatrix" class="btn-refresh">
          <i class="fas fa-sync-alt"></i>
          Load Matrix
        </button>
      </div>
      <div class="card-body">
        <div v-if="permissionMatrix" class="matrix-display">
          <div class="matrix-info">
            <p><strong>Total Rules:</strong> {{ permissionMatrix.total_rules }}</p>
            <p><strong>Roles:</strong> {{ permissionMatrix.roles }}</p>
          </div>
          
          <div class="matrix-table">
            <div v-for="(resources, role) in permissionMatrix.permission_matrix" :key="role" class="role-section">
              <h4 class="role-title">{{ role }}</h4>
              <div class="resources">
                <div v-for="(actions, resource) in resources" :key="resource" class="resource-item">
                  <span class="resource-name">{{ resource }}</span>
                  <div class="actions">
                    <span v-for="action in actions" :key="action" class="action-badge">
                      {{ action }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Available Resources and Actions -->
    <div class="card">
      <div class="card-header">
        <h2><i class="fas fa-list"></i> System Resources & Actions</h2>
      </div>
      <div class="card-body">
        <div class="resources-actions">
          <div class="column">
            <h4>Available Resources:</h4>
            <div class="items-list">
              <span v-for="resource in availableResources" :key="resource" class="item-badge resource">
                {{ resource }}
              </span>
            </div>
          </div>
          <div class="column">
            <h4>Available Actions:</h4>
            <div class="items-list">
              <span v-for="action in availableActions" :key="action" class="item-badge action">
                {{ action }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted, computed } from 'vue'
import axios from 'axios'

export default {
  name: 'Permissions',
  setup() {
    const loading = ref(false)
    const myPermissions = ref(null)
    const permissionMatrix = ref(null)
    const availableResources = ref([])
    const availableActions = ref([])
    const permissionResult = ref(null)
    
    const permissionCheck = reactive({
      resource: '',
      action: ''
    })

    const currentUser = ref(null)
    
    const isAdmin = computed(() => {
      return currentUser.value?.is_admin || false
    })

    const loadCurrentUser = async () => {
      try {
        const response = await axios.get('/api/auth/me')
        currentUser.value = response.data
      } catch (error) {
        console.error('Failed to load current user:', error)
      }
    }

    const loadMyPermissions = async () => {
      try {
        const response = await axios.get('/api/permissions/me')
        myPermissions.value = response.data
      } catch (error) {
        console.error('Failed to load permissions:', error)
      }
    }

    const loadPermissionMatrix = async () => {
      try {
        const response = await axios.get('/api/permissions/matrix')
        permissionMatrix.value = response.data
      } catch (error) {
        console.error('Failed to load permission matrix:', error)
      }
    }

    const loadAvailableResources = async () => {
      try {
        const response = await axios.get('/api/permissions/resources')
        availableResources.value = response.data
      } catch (error) {
        console.error('Failed to load resources:', error)
      }
    }

    const loadAvailableActions = async () => {
      try {
        const response = await axios.get('/api/permissions/actions')
        availableActions.value = response.data
      } catch (error) {
        console.error('Failed to load actions:', error)
      }
    }

    const checkPermission = async () => {
      try {
        const response = await axios.post('/api/permissions/check', permissionCheck)
        permissionResult.value = response.data
      } catch (error) {
        console.error('Failed to check permission:', error)
      }
    }

    onMounted(async () => {
      await loadCurrentUser()
      await loadMyPermissions()
      await loadAvailableResources()
      await loadAvailableActions()
    })

    return {
      loading,
      myPermissions,
      permissionMatrix,
      availableResources,
      availableActions,
      permissionResult,
      permissionCheck,
      isAdmin,
      loadMyPermissions,
      loadPermissionMatrix,
      checkPermission
    }
  }
}
</script>

<style scoped>
.permissions-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.header {
  text-align: center;
  margin-bottom: 30px;
}

.header h1 {
  color: var(--primary-color);
  margin-bottom: 10px;
}

.subtitle {
  color: var(--text-muted);
  font-size: 1.1rem;
}

.card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 20px;
  overflow: hidden;
}

.card-header {
  background: var(--primary-color);
  color: white;
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h2 {
  margin: 0;
  font-size: 1.2rem;
}

.btn-refresh {
  background: rgba(255,255,255,0.2);
  border: 1px solid rgba(255,255,255,0.3);
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
}

.card-body {
  padding: 20px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr auto;
  gap: 15px;
  align-items: end;
}

.permission-result {
  margin-top: 20px;
  padding: 15px;
  border-radius: 6px;
  background: var(--bg-light);
}

.result-badge {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: bold;
  margin-bottom: 10px;
}

.result-badge.allowed {
  background: var(--success-color);
  color: white;
}

.result-badge.denied {
  background: var(--danger-color);
  color: white;
}

.role-badge {
  background: var(--primary-color);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  margin-right: 5px;
}

.permission-item {
  display: grid;
  grid-template-columns: 1fr 2fr 1fr;
  gap: 10px;
  padding: 8px;
  background: var(--bg-light);
  margin-bottom: 5px;
  border-radius: 4px;
}

.matrix-table {
  max-height: 400px;
  overflow-y: auto;
}

.role-section {
  margin-bottom: 20px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  overflow: hidden;
}

.role-title {
  background: var(--primary-color);
  color: white;
  margin: 0;
  padding: 10px 15px;
}

.resource-item {
  padding: 10px 15px;
  border-bottom: 1px solid var(--border-color);
}

.resource-item:last-child {
  border-bottom: none;
}

.resource-name {
  font-weight: bold;
  display: block;
  margin-bottom: 5px;
}

.action-badge {
  background: var(--info-color);
  color: white;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 0.7rem;
  margin-right: 5px;
}

.resources-actions {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
}

.items-list {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.item-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
}

.item-badge.resource {
  background: var(--warning-color);
  color: white;
}

.item-badge.action {
  background: var(--info-color);
  color: white;
}

.no-permissions {
  color: var(--text-muted);
  font-style: italic;
  padding: 20px;
  text-align: center;
}
</style>
