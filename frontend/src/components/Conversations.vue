<template>
  <div class="conversations">
    <div class="page-header">
      <div class="header-content">
        <h1><i class="fas fa-comments"></i> Conversations</h1>
        <!-- NEW: Show a subtitle if filtered by a customer -->
        <p v-if="filteredCustomerId">Showing conversations for Customer #{{ filteredCustomerId }}</p>
        <p v-else>Manage all customer conversations</p>
      </div>
      <div class="header-actions">
        <button @click="refreshConversations" class="btn btn-secondary">
          <i class="fas fa-sync-alt" :class="{ 'fa-spin': loading }"></i>
          Refresh
        </button>
        <button @click="showCreateModal = true" class="btn btn-primary">
          <i class="fas fa-plus"></i>
          New Conversation
        </button>
      </div>
    </div>

    <!-- Conversations List -->
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">
          Conversations ({{ conversations.length }})
        </h3>
      </div>

      <div v-if="loading" class="loading-state">
        <i class="fas fa-spinner fa-spin"></i>
        <p>Loading conversations...</p>
      </div>

      <div v-else-if="conversations.length === 0" class="empty-state">
        <i class="fas fa-inbox"></i>
        <p>No conversations found</p>
        <button @click="showCreateModal = true" class="btn btn-primary">
          <i class="fas fa-plus"></i>
          Create First Conversation
        </button>
      </div>

      <div v-else class="conversations-list">
        <div 
          v-for="conversation in conversations" 
          :key="conversation.id"
          class="conversation-item"
          @click="openConversation(conversation.id)"
        >
          <div class="conversation-avatar">
            <i class="fas fa-user"></i>
          </div>
          
          <div class="conversation-content">
            <div class="conversation-header">
              <h4>{{ conversation.customer?.name || 'Unknown Customer' }}</h4>
              <span class="conversation-time">
                {{ formatTime(conversation.updated_at || conversation.created_at) }}
              </span>
            </div>
            
            <div class="conversation-details">
              <p class="customer-email">{{ conversation.customer?.email || 'No email' }}</p>
              <div class="conversation-meta">
                <span :class="['badge', `badge-${getStatusColor(conversation.status)}`]">
                  {{ conversation.status }}
                </span>
                <span v-if="conversation.team" class="team-badge">
                  <i class="fas fa-users"></i>
                  {{ conversation.team.name }}
                </span>
                <span v-else class="team-badge unassigned">
                  <i class="fas fa-exclamation-triangle"></i>
                  Unassigned
                </span>
              </div>
            </div>
          </div>
          
          <div class="conversation-actions">
            <button 
              @click.stop="openConversation(conversation.id)"
              class="btn btn-sm btn-primary"
            >
              <i class="fas fa-eye"></i>
              View
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, computed, getCurrentInstance } from 'vue'
import { useRouter, useRoute } from 'vue-router' // Import useRoute
import axios from 'axios'

export default {
  name: 'Conversations',
  setup() {
    const router = useRouter();
    const route = useRoute(); // NEW: Get the current route information
    const app = getCurrentInstance();
    const showToast = app.appContext.config.globalProperties.$showToast;
    
    const loading = ref(false);
    const showCreateModal = ref(false);
    const conversations = ref([]);

    // NEW: A computed property to check if we are filtering
    const filteredCustomerId = computed(() => route.query.customerId);

    const loadConversations = async () => {
      loading.value = true;
      try {
        let response;
        // MODIFIED: Check if we need to fetch all conversations or just for one customer
        if (filteredCustomerId.value) {
          response = await axios.get(`/api/conversations/customer/${filteredCustomerId.value}`);
        } else {
          // This call requires admin, so it might fail for agents. That's okay.
          // In a real app, you'd fetch based on team or unassigned.
          response = await axios.get('/api/conversations/');
        }
        conversations.value = response.data;
      } catch (error) {
        showToast(error.response?.data?.detail || 'Failed to load conversations', 'error');
        conversations.value = []; // Clear on error
      } finally {
        loading.value = false;
      }
    };
    
    const refreshConversations = () => {
      loadConversations();
    };

    const openConversation = (conversationId) => {
      // This will navigate to the Chat.vue component for single conversation
      router.push({ name: 'SingleChat', params: { id: conversationId } });
    };

    const formatTime = (timestamp) => {
      const date = new Date(timestamp);
      const now = new Date();
      const diff = now - date;

      if (diff < 60000) return 'Just now';
      if (diff < 3600000) return `${Math.floor(diff / 60000)}m ago`;
      if (diff < 86400000) return `${Math.floor(diff / 3600000)}h ago`;
      return `${Math.floor(diff / 86400000)}d ago`;
    };

    const getStatusColor = (status) => {
      const colors = {
        'new': 'info',
        'active': 'success',
        'pending': 'warning',
        'resolved': 'secondary',
        'closed': 'secondary'
      };
      return colors[status] || 'secondary';
    };

    onMounted(loadConversations);
    
    return {
      loading,
      showCreateModal,
      conversations,
      filteredCustomerId,
      refreshConversations,
      openConversation,
      formatTime,
      getStatusColor
    }
  }
}
</script>

<style scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30px;
  gap: 20px;
}

.header-content h1 {
  color: var(--dark-color);
  margin-bottom: 5px;
}

.header-content p {
  color: var(--secondary-color);
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 10px;
  flex-shrink: 0;
}

.conversations-list {
  max-height: 600px;
  overflow-y: auto;
}

.conversation-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  border-bottom: 1px solid var(--border-color);
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.conversation-item:hover {
  background-color: var(--light-color);
}

.conversation-item:last-child {
  border-bottom: none;
}

.conversation-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: var(--info-color);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
}

.conversation-content {
  flex: 1;
}

.conversation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}

.conversation-header h4 {
  margin: 0;
  font-size: 1rem;
  color: var(--dark-color);
}

.conversation-time {
  font-size: 0.8rem;
  color: var(--secondary-color);
}

.conversation-details {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.customer-email {
  margin: 0;
  font-size: 0.9rem;
  color: var(--secondary-color);
}

.conversation-meta {
  display: flex;
  align-items: center;
  gap: 10px;
}

.conversation-actions {
  display: flex;
  gap: 5px;
}

.team-badge {
  display: inline-flex;
  align-items: center;
  gap: 5px;
  background: var(--light-color);
  color: var(--secondary-color);
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
}

.team-badge.unassigned {
  background: var(--warning-color);
  color: var(--dark-color);
}

.badge {
  display: inline-block;
  padding: 4px 8px;
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: 4px;
  text-transform: uppercase;
}

.badge-success {
  background: var(--success-color);
  color: white;
}

.badge-warning {
  background: var(--warning-color);
  color: var(--dark-color);
}

.badge-info {
  background: var(--info-color);
  color: white;
}

.badge-secondary {
  background: var(--secondary-color);
  color: white;
}

.loading-state,
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: var(--secondary-color);
}

.loading-state i,
.empty-state i {
  font-size: 2rem;
  margin-bottom: 10px;
  display: block;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }
  
  .header-actions {
    justify-content: flex-end;
  }
  
  .conversation-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .conversation-header {
    width: 100%;
  }
  
  .conversation-actions {
    width: 100%;
    justify-content: flex-end;
  }
}
</style>
