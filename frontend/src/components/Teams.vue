<template>
  <div class="teams">
    <div class="page-header">
      <div class="header-content">
        <h1><i class="fas fa-user-friends"></i> Teams</h1>
        <p>Manage support teams and agent assignments</p>
      </div>
      <div class="header-actions">
        <button @click="refreshTeams" class="btn btn-secondary">
          <i class="fas fa-sync-alt" :class="{ 'fa-spin': loading }"></i>
          Refresh
        </button>
        <button @click="openCreateModal" class="btn btn-primary">
          <i class="fas fa-plus"></i>
          Create Team
        </button>
      </div>
    </div>

    <div v-if="loading" class="loading-state">
      <i class="fas fa-spinner fa-spin"></i>
      <p>Loading teams...</p>
    </div>

    <div v-else-if="teams.length === 0" class="empty-state">
      <i class="fas fa-users-slash"></i>
      <p>No teams found</p>
    </div>

    <div v-else class="teams-grid">
      <div v-for="team in teams" :key="team.id" class="team-card">
        <div class="team-header">
          <div class="team-info">
            <h3>{{ team.name }}</h3>
            <p>{{ team.description }}</p>
          </div>
          <div class="team-actions">
            <button @click="editTeam(team)" class="btn btn-sm btn-secondary">
              <i class="fas fa-edit"></i>
            </button>
            <button @click="deleteTeam(team)" class="btn btn-sm btn-danger">
              <i class="fas fa-trash"></i>
            </button>
          </div>
        </div>

        <div class="team-stats">
          <div class="stat-item">
            <i class="fas fa-users"></i>
            <span>0 Members</span>
          </div>
          <div class="stat-item">
            <i class="fas fa-comments"></i>
            <span>0 Conversations</span>
          </div>
          <div class="stat-item">
            <i class="fas fa-building"></i>
            <span>{{ team.organization?.name || 'No Organization' }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Create/Edit Team Modal -->
    <div v-if="showModal" class="modal-overlay" @click.self="closeModal">
      <div class="modal-content">
        <form @submit.prevent="saveTeam" class="form-section">
          <h3>
            <i :class="isEditing ? 'fas fa-edit' : 'fas fa-plus'"></i>
            {{ isEditing ? 'Edit Team' : 'Create New Team' }}
          </h3>

          <div class="form-group">
            <label for="teamName">Team Name *</label>
            <input
              type="text"
              id="teamName"
              v-model="teamForm.name"
              placeholder="e.g., Customer Support"
              required
            >
          </div>

          <div class="form-group">
            <label for="teamDescription">Description</label>
            <textarea
              id="teamDescription"
              v-model="teamForm.description"
              rows="3"
              placeholder="Brief description of the team's role"
            ></textarea>
          </div>

          <div class="form-group">
            <label for="teamOrganization">Organization *</label>
            <select id="teamOrganization" v-model.number="teamForm.organization_id" required>
              <option value="">Select an organization</option>
              <option v-for="org in organizations" :key="org.id" :value="org.id">
                {{ org.name }}
              </option>
            </select>
          </div>

          <div class="modal-actions">
            <button type="button" class="btn btn-secondary" @click="closeModal">Cancel</button>
            <button type="submit" class="btn btn-primary" :disabled="isSubmitting">
              <i v-if="isSubmitting" class="fas fa-spinner fa-spin"></i>
              <i v-else :class="isEditing ? 'fas fa-save' : 'fas fa-plus'"></i>
              {{ isSubmitting ? 'Saving...' : (isEditing ? 'Save Changes' : 'Create Team') }}
            </button>
          </div>

          <div v-if="modalError" class="error-message">{{ modalError }}</div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue'
import axios from 'axios'

export default {
  name: 'Teams',
  setup() {
    const app = getCurrentInstance()
    const showToast = app.appContext.config.globalProperties.$showToast

    const loading = ref(false)
    const teams = ref([])
    const organizations = ref([])

    const showModal = ref(false)
    const isEditing = ref(false)
    const isSubmitting = ref(false)
    const modalError = ref('')

    const teamForm = reactive({
      id: null,
      name: '',
      description: '',
      organization_id: null
    })

    const loadTeams = async () => {
      loading.value = true
      try {
        const response = await axios.get('/api/teams/')
        teams.value = response.data
      } catch (error) {
        console.error('Error loading teams:', error)
        showToast('Failed to load teams', 'error')
      } finally {
        loading.value = false
      }
    }

    const loadOrganizations = async () => {
      try {
        const response = await axios.get('/api/organizations/')
        organizations.value = response.data
      } catch (error) {
        console.error('Error loading organizations:', error)
        showToast('Failed to load organizations', 'error')
      }
    }

    const refreshTeams = () => {
      loadTeams()
    }

    const resetForm = () => {
      teamForm.id = null
      teamForm.name = ''
      teamForm.description = ''
      teamForm.organization_id = null
    }

    const openCreateModal = async () => {
      resetForm()
      isEditing.value = false
      modalError.value = ''
      await loadOrganizations()
      showModal.value = true
    }

    const editTeam = async (team) => {
      resetForm()
      teamForm.id = team.id
      teamForm.name = team.name
      teamForm.description = team.description || ''
      teamForm.organization_id = team.organization_id
      isEditing.value = true
      modalError.value = ''
      await loadOrganizations()
      showModal.value = true
    }

    const closeModal = () => {
      showModal.value = false
      resetForm()
    }

    const saveTeam = async () => {
      isSubmitting.value = true
      modalError.value = ''

      try {
        const teamData = {
          name: teamForm.name,
          description: teamForm.description || null,
          organization_id: teamForm.organization_id
        }

        if (isEditing.value) {
          await axios.put(`/api/teams/${teamForm.id}`, teamData)
          showToast('Team updated successfully', 'success')
        } else {
          await axios.post('/api/teams/', teamData)
          showToast('Team created successfully', 'success')
        }

        closeModal()
        await loadTeams()

      } catch (error) {
        console.error('Error saving team:', error)
        modalError.value = error.response?.data?.detail || 'Failed to save team'
      } finally {
        isSubmitting.value = false
      }
    }

    const deleteTeam = async (team) => {
      if (!confirm(`Are you sure you want to delete team "${team.name}"?`)) {
        return
      }

      try {
        await axios.delete(`/api/teams/${team.id}`)
        showToast('Team deleted successfully', 'success')
        await loadTeams()
      } catch (error) {
        console.error('Error deleting team:', error)
        showToast('Failed to delete team', 'error')
      }
    }

    onMounted(() => {
      loadTeams()
    })

    return {
      loading,
      teams,
      organizations,
      showModal,
      isEditing,
      isSubmitting,
      modalError,
      teamForm,
      refreshTeams,
      openCreateModal,
      editTeam,
      closeModal,
      saveTeam,
      deleteTeam
    }
  }
}
</script>

<style scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30px;
  gap: 20px;
}

.header-content h1 {
  color: var(--dark-color);
  margin-bottom: 5px;
}

.header-content p {
  color: var(--secondary-color);
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 10px;
  flex-shrink: 0;
}

.teams-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 20px;
}

.team-card {
  background: var(--bg-card);
  border: 1px solid var(--border-light);
  border-radius: 8px;
  box-shadow: var(--shadow);
  padding: 20px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.team-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-color);
}

.team-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.team-info h3 {
  margin: 0 0 5px 0;
  color: var(--text-primary);
}

.team-info p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.team-actions {
  display: flex;
  gap: 5px;
}

.team-stats {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 0.9rem;
  color: var(--secondary-color);
}

.loading-state,
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: var(--secondary-color);
}

.loading-state i,
.empty-state i {
  font-size: 2rem;
  margin-bottom: 10px;
  display: block;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }
  
  .header-actions {
    justify-content: flex-end;
  }
  
  .teams-grid {
    grid-template-columns: 1fr;
  }
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  padding: 30px;
  border-radius: 8px;
  width: 100%;
  max-width: 500px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  max-height: 90vh;
  overflow-y: auto;
}

.form-section h3 {
  margin-bottom: 20px;
  color: var(--dark-color);
  display: flex;
  align-items: center;
  gap: 10px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--dark-color);
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  font-size: 0.9rem;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.error-message {
  background: #f8d7da;
  color: #721c24;
  padding: 12px;
  border-radius: 6px;
  margin-top: 15px;
  font-size: 0.9rem;
}
</style>
