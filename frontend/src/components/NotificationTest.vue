<template>
  <div class="notification-test">
    <div class="card">
      <div class="card-header">
        <h2 class="card-title">🔔 Real-time Notifications Test</h2>
      </div>
      <div class="card-body">
        <!-- Connection Status -->
        <div class="connection-status" :class="connectionStatus">
          <div class="status-indicator"></div>
          <span>{{ connectionStatusText }}</span>
        </div>

        <!-- Controls -->
        <div class="controls">
          <button @click="connectNotifications" :disabled="isConnected" class="btn btn-primary">
            Connect to Notifications
          </button>
          <button @click="disconnectNotifications" :disabled="!isConnected" class="btn btn-secondary">
            Disconnect
          </button>
          <button @click="sendTestNotification" :disabled="!isConnected" class="btn btn-success">
            Send Test Notification
          </button>
        </div>

        <!-- Notifications Display -->
        <div class="notifications-display">
          <h3>Recent Notifications:</h3>
          <div v-if="notifications.length === 0" class="empty-state">
            <i class="fas fa-bell-slash"></i>
            <p>No notifications received yet</p>
          </div>
          <div v-else class="notification-list">
            <div 
              v-for="notification in notifications" 
              :key="notification.id"
              class="notification-item"
              :class="notification.priority"
            >
              <div class="notification-icon">
                <i :class="getNotificationIcon(notification.type)"></i>
              </div>
              <div class="notification-content">
                <h4>{{ notification.title }}</h4>
                <p>{{ notification.message }}</p>
                <small>{{ formatTime(notification.timestamp) }}</small>
              </div>
              <div class="notification-type">
                {{ notification.type }}
              </div>
            </div>
          </div>
        </div>

        <!-- Debug Info -->
        <div class="debug-info">
          <h4>Debug Information:</h4>
          <pre>{{ debugInfo }}</pre>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useAuthStore } from '../stores/auth'

export default {
  name: 'NotificationTest',
  setup() {
    const authStore = useAuthStore()
    
    // Reactive data
    const isConnected = ref(false)
    const notifications = ref([])
    const debugInfo = ref({})
    const websocket = ref(null)
    
    // Computed properties
    const connectionStatus = computed(() => {
      return isConnected.value ? 'connected' : 'disconnected'
    })
    
    const connectionStatusText = computed(() => {
      return isConnected.value ? 'Connected to notifications' : 'Disconnected'
    })
    
    // Methods
    const connectNotifications = async () => {
      try {
        if (!authStore.token) {
          alert('Please login first')
          return
        }
        
        const wsUrl = `ws://localhost:8000/api/notifications/ws/notifications?token=${authStore.token}`
        debugInfo.value.wsUrl = wsUrl
        
        websocket.value = new WebSocket(wsUrl)
        
        websocket.value.onopen = () => {
          isConnected.value = true
          debugInfo.value.status = 'Connected'
          console.log('✅ Connected to notifications WebSocket')
        }
        
        websocket.value.onmessage = (event) => {
          try {
            const notification = JSON.parse(event.data)
            console.log('📨 Received notification:', notification)
            
            // Add to notifications list
            notifications.value.unshift({
              ...notification,
              timestamp: notification.timestamp || new Date().toISOString()
            })
            
            // Keep only last 10 notifications
            if (notifications.value.length > 10) {
              notifications.value = notifications.value.slice(0, 10)
            }
            
            debugInfo.value.lastMessage = notification
            
          } catch (error) {
            console.error('❌ Error parsing notification:', error)
            debugInfo.value.lastError = error.message
          }
        }
        
        websocket.value.onclose = () => {
          isConnected.value = false
          debugInfo.value.status = 'Disconnected'
          console.log('🔌 Disconnected from notifications WebSocket')
        }
        
        websocket.value.onerror = (error) => {
          console.error('❌ WebSocket error:', error)
          debugInfo.value.error = error
        }
        
      } catch (error) {
        console.error('❌ Failed to connect to notifications:', error)
        debugInfo.value.connectionError = error.message
      }
    }
    
    const disconnectNotifications = () => {
      if (websocket.value) {
        websocket.value.close()
        websocket.value = null
      }
    }
    
    const sendTestNotification = async () => {
      try {
        const response = await fetch('/api/notifications/test', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${authStore.token}`,
            'Content-Type': 'application/json'
          }
        })
        
        if (response.ok) {
          console.log('✅ Test notification sent')
          debugInfo.value.testSent = new Date().toISOString()
        } else {
          console.error('❌ Failed to send test notification')
          debugInfo.value.testError = await response.text()
        }
      } catch (error) {
        console.error('❌ Error sending test notification:', error)
        debugInfo.value.testError = error.message
      }
    }
    
    const getNotificationIcon = (type) => {
      const icons = {
        'new_message': 'fas fa-comment',
        'new_conversation': 'fas fa-comments',
        'system_alert': 'fas fa-exclamation-triangle',
        'agent_joined': 'fas fa-user-plus',
        'agent_left': 'fas fa-user-minus'
      }
      return icons[type] || 'fas fa-bell'
    }
    
    const formatTime = (timestamp) => {
      return new Date(timestamp).toLocaleTimeString()
    }
    
    // Lifecycle
    onMounted(() => {
      debugInfo.value.mounted = new Date().toISOString()
    })
    
    onUnmounted(() => {
      disconnectNotifications()
    })
    
    return {
      isConnected,
      notifications,
      debugInfo,
      connectionStatus,
      connectionStatusText,
      connectNotifications,
      disconnectNotifications,
      sendTestNotification,
      getNotificationIcon,
      formatTime
    }
  }
}
</script>

<style scoped>
.notification-test {
  padding: 20px;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 20px;
  padding: 10px;
  border-radius: 6px;
}

.connection-status.connected {
  background: rgba(39, 174, 96, 0.1);
  color: var(--success-color);
}

.connection-status.disconnected {
  background: rgba(231, 76, 60, 0.1);
  color: var(--danger-color);
}

.status-indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: currentColor;
}

.controls {
  display: flex;
  gap: 10px;
  margin-bottom: 30px;
  flex-wrap: wrap;
}

.notifications-display {
  margin-bottom: 30px;
}

.notifications-display h3 {
  color: var(--text-primary);
  margin-bottom: 15px;
}

.notification-list {
  max-height: 400px;
  overflow-y: auto;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  padding: 15px;
  margin-bottom: 10px;
  background: var(--bg-card);
  border: 1px solid var(--border-light);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.notification-item:hover {
  border-color: var(--primary-color);
  transform: translateY(-1px);
}

.notification-item.high {
  border-left: 4px solid var(--danger-color);
}

.notification-item.normal {
  border-left: 4px solid var(--primary-color);
}

.notification-item.low {
  border-left: 4px solid var(--secondary-color);
}

.notification-icon {
  color: var(--primary-color);
  font-size: 1.2rem;
  margin-top: 2px;
}

.notification-content {
  flex: 1;
}

.notification-content h4 {
  margin: 0 0 5px 0;
  color: var(--text-primary);
  font-size: 1rem;
}

.notification-content p {
  margin: 0 0 5px 0;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.notification-content small {
  color: var(--text-muted);
  font-size: 0.8rem;
}

.notification-type {
  background: var(--bg-tertiary);
  color: var(--text-primary);
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
}

.debug-info {
  background: var(--bg-secondary);
  border: 1px solid var(--border-light);
  border-radius: 6px;
  padding: 15px;
}

.debug-info h4 {
  margin: 0 0 10px 0;
  color: var(--text-primary);
}

.debug-info pre {
  background: var(--bg-primary);
  color: var(--text-secondary);
  padding: 10px;
  border-radius: 4px;
  font-size: 0.8rem;
  overflow-x: auto;
  margin: 0;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: var(--text-muted);
}

.empty-state i {
  font-size: 3rem;
  margin-bottom: 15px;
  display: block;
}
</style>
