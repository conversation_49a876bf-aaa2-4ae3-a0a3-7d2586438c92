<template>
  <div class="login-container">
    <div class="login-card">
      <div class="login-header">
        <div class="login-logo">
          <i class="fas fa-robot"></i>
          <h1>Yupcha AI</h1>
        </div>
        <p class="login-subtitle">Customer Service Bot Administration</p>
      </div>

      <!-- Form now calls the new, simplified handleLogin -->
      <form @submit.prevent="handleLogin" class="login-form">
        <div class="form-group">
          <label for="email" class="form-label">
            <i class="fas fa-envelope"></i>
            Email Address
          </label>
          <input 
            type="email" 
            id="email"
            v-model="form.email" 
            class="form-control"
            placeholder="Enter your email"
            required
            :disabled="loading"
          >
        </div>

        <div class="form-group">
          <label for="password" class="form-label">
            <i class="fas fa-lock"></i>
            Password
          </label>
          <div class="password-input">
            <input 
              :type="showPassword ? 'text' : 'password'"
              id="password"
              v-model="form.password" 
              class="form-control"
              placeholder="Enter your password"
              required
              :disabled="loading"
            >
            <button 
              type="button" 
              class="password-toggle"
              @click="showPassword = !showPassword"
              :disabled="loading"
            >
              <i :class="showPassword ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
            </button>
          </div>
        </div>

        <div class="form-group">
          <button 
            type="submit" 
            class="btn btn-primary btn-login"
            :disabled="loading || !form.email || !form.password"
          >
            <i v-if="loading" class="fas fa-spinner fa-spin"></i>
            <i v-else class="fas fa-sign-in-alt"></i>
            {{ loading ? 'Signing In...' : 'Sign In' }}
          </button>
        </div>

        <div v-if="error" class="error-message">
          <i class="fas fa-exclamation-circle"></i>
          {{ error }}
        </div>
      </form>

      <div class="login-footer">
        <div class="demo-credentials">
          <h4><i class="fas fa-info-circle"></i> Demo Credentials</h4>
          <div class="credential-item">
            <strong>Admin:</strong>
            <button @click="fillCredentials('admin')" class="btn btn-sm btn-secondary">
              <EMAIL> / adminpassword
            </button>
          </div>
          <div class="credential-item">
            <strong>Agent:</strong>
            <button @click="fillCredentials('agent')" class="btn btn-sm btn-secondary">
              <EMAIL> / agentpassword
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { reactive, ref } from 'vue'
import axios from 'axios'

export default {
  name: 'Login',
  setup() {
    // We no longer need useRouter or getCurrentInstance here for the login logic
    
    const form = reactive({
      email: '',
      password: ''
    })
    
    const loading = ref(false)
    const error = ref('')
    const showPassword = ref(false)
    
    const fillCredentials = (type) => {
      if (type === 'admin') {
        form.email = '<EMAIL>'
        form.password = 'adminpassword'
      } else if (type === 'agent') {
        form.email = '<EMAIL>'
        form.password = 'agentpassword'
      }
    }
    
    // --- MODIFIED: Simplified and Robust handleLogin function ---
    const handleLogin = async () => {
      loading.value = true
      error.value = ''
      
      try {
        // Step 1: Send the login request.
        // The backend will set an HttpOnly cookie if successful.
        const response = await axios.post('/api/auth/login', 
          `username=${encodeURIComponent(form.email)}&password=${encodeURIComponent(form.password)}`,
          {
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
          }
        )
        
        // Check if the backend responded with a success message.
        if (response.data.message !== 'Login successful') {
            throw new Error('Backend did not confirm successful login.');
        }

        // Step 2: Perform a full-page redirect to the dashboard.
        // This is the simplest and most reliable way to handle the session.
        // The browser will reload the entire application. When it does,
        // the main App.vue's session check will run with the new cookie.
        // This completely avoids any race conditions.
        window.location.href = '/dashboard';
        
      } catch (err) {
        console.error('Login error:', err)
        if (err.response?.status === 401) {
          error.value = 'Invalid email or password.'
        } else {
          error.value = 'An unexpected error occurred. Please check the network connection and try again.'
        }
      } finally {
        loading.value = false
      }
    }
    
    return {
      form,
      loading,
      error,
      showPassword,
      fillCredentials,
      handleLogin
    }
  }
}
</script>

<style scoped>
/* Your CSS is excellent and does not need any changes. */
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.login-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  padding: 40px;
  width: 100%;
  max-width: 450px;
  animation: slideUp 0.5s ease;
}

@keyframes slideUp {
  from { opacity: 0; transform: translateY(30px); }
  to { opacity: 1; transform: translateY(0); }
}

.login-header { text-align: center; margin-bottom: 30px; }
.login-logo { display: flex; align-items: center; justify-content: center; gap: 15px; margin-bottom: 10px; }
.login-logo i { font-size: 2.5rem; color: #667eea; }
.login-logo h1 { font-size: 2rem; color: #333; margin: 0; }
.login-subtitle { color: #6c757d; margin: 0; font-size: 0.9rem; }
.login-form { margin-bottom: 30px; }
.form-group { margin-bottom: 20px; }
.form-label { display: flex; align-items: center; gap: 8px; margin-bottom: 8px; font-weight: 500; color: #495057; }
.password-input { position: relative; }
.password-toggle { position: absolute; right: 12px; top: 50%; transform: translateY(-50%); background: none; border: none; color: #adb5bd; cursor: pointer; padding: 4px; }
.password-toggle:hover { color: #667eea; }
.btn-login { width: 100%; padding: 12px; font-size: 1rem; font-weight: 600; background-color: #667eea; }
.error-message { background: #f8d7da; color: #721c24; padding: 12px; border-radius: 6px; display: flex; align-items: center; gap: 8px; margin-top: 15px; font-size: 0.9rem; }
.login-footer { border-top: 1px solid #e9ecef; padding-top: 20px; }
.demo-credentials h4 { color: #6c757d; font-size: 0.9rem; margin-bottom: 15px; display: flex; align-items: center; gap: 8px; }
.credential-item { display: flex; align-items: center; justify-content: space-between; margin-bottom: 10px; font-size: 0.85rem; }
.credential-item strong { color: #495057; }
.credential-item .btn-secondary { background-color: #6c757d; font-size: 0.75rem; padding: 4px 8px; }
</style>