<template>
  <div class="chat-container">
    <div class="chat-header">
      <div class="chat-info">
        <div class="conversation-details">
          <h2>Conversation #{{ conversationId }}</h2>
          <p v-if="conversation">
            With Customer: <strong>{{ conversation.customer?.name || 'Unknown' }}</strong>
            <span v-if="conversation.customer?.email"> ({{ conversation.customer.email }})</span>
          </p>
        </div>
        <div class="connection-status" :class="{ connected: isConnected }">
          <div class="status-dot"></div>
          <span>{{ isConnected ? 'Connected' : 'Disconnected' }}</span>
        </div>
      </div>
    </div>

    <div class="chat-body">
      <div class="messages-container" ref="messagesContainer">
        <div v-if="messages.length === 0" class="empty-messages">
          <i class="fas fa-comments"></i>
          <h3>No messages yet</h3>
          <p>Start the conversation by sending a message below.</p>
        </div>

        <div v-for="message in messages" :key="message.id" class="message-wrapper">
          <div class="message" :class="getMessageClass(message.sender)">
            <div class="message-content">
              <div class="sender-info">{{ message.sender }}</div>
              <div class="content">{{ message.content }}</div>
              <div class="timestamp">{{ formatTime(message.created_at) }}</div>
            </div>
          </div>
        </div>
      </div>

      <div class="message-input">
        <form @submit.prevent="sendMessage" class="input-form">
          <div class="input-group">
            <input
              v-model="newMessage"
              type="text"
              class="form-control"
              placeholder="Type your message..."
              :disabled="!isConnected"
            >
            <button type="submit" class="btn btn-primary" :disabled="!isConnected || !newMessage.trim()">
              <i class="fas fa-paper-plane"></i> Send
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted, nextTick, computed } from 'vue'
import { useRoute } from 'vue-router'

export default {
  name: 'Chat',
  setup() {
    const route = useRoute()
    const conversationId = route.params.id

    const conversation = ref(null)
    const messages = ref([])
    const newMessage = ref('')
    const websocket = ref(null)
    const messagesContainer = ref(null)

    const isConnected = computed(() => websocket.value && websocket.value.readyState === WebSocket.OPEN)

    const scrollToBottom = () => {
      nextTick(() => {
        if (messagesContainer.value) {
          messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
        }
      })
    }

    const loadInitialData = async () => {
      try {
        const [convResponse, messagesResponse] = await Promise.all([
          fetch(`/api/conversations/${conversationId}`, { credentials: 'include' }),
          fetch(`/api/conversations/${conversationId}/messages`, { credentials: 'include' })
        ])

        if (convResponse.ok && messagesResponse.ok) {
          conversation.value = await convResponse.json()
          messages.value = await messagesResponse.json()
          console.log('✅ Loaded conversation and messages:', messages.value.length)
          scrollToBottom()
        } else {
          console.error('❌ Failed to load conversation data')
        }
      } catch (error) {
        console.error('❌ Failed to load chat data:', error)
      }
    }

    const connectWebSocket = () => {
      const wsUrl = `ws://localhost:8000/api/ws/chat/${conversationId}`
      console.log('🔌 Connecting to WebSocket:', wsUrl)

      websocket.value = new WebSocket(wsUrl)

      websocket.value.onopen = () => {
        console.log('✅ WebSocket connected')
      }

      websocket.value.onclose = () => {
        console.log('🔌 WebSocket disconnected')
      }

      websocket.value.onerror = (error) => {
        console.error('❌ WebSocket error:', error)
      }

      websocket.value.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data)
          console.log('📨 Received message:', data)

          if (data.type === 'message') {
            messages.value.push(data)
            scrollToBottom()
          }
        } catch (error) {
          console.error('❌ Error parsing WebSocket message:', error)
        }
      }
    }

    const sendMessage = () => {
      if (!newMessage.value.trim() || !isConnected.value) return

      const payload = {
        content: newMessage.value.trim(),
        sender: 'agent',
        message_type: 'text'
      }

      console.log('📤 Sending message:', payload)
      websocket.value.send(JSON.stringify(payload))
      newMessage.value = ''
    }

    const getMessageClass = (sender) => {
      if (sender === 'customer') return 'customer-message'
      if (sender === 'agent' || sender === 'admin') return 'agent-message'
      if (sender === 'bot') return 'bot-message'
      return 'system-message'
    }

    const formatTime = (timestamp) => {
      if (!timestamp) return ''
      return new Date(timestamp).toLocaleTimeString()
    }

    onMounted(async () => {
      await loadInitialData()
      connectWebSocket()
    })

    onUnmounted(() => {
      if (websocket.value) {
        websocket.value.close()
      }
    })

    return {
      conversationId,
      conversation,
      messages,
      newMessage,
      isConnected,
      messagesContainer,
      sendMessage,
      getMessageClass,
      formatTime
    }
  }
}
</script>

<style scoped>
.chat-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 100px);
  background: var(--bg-primary);
  border-radius: 8px;
  box-shadow: var(--shadow);
  overflow: hidden;
}

.chat-header {
  padding: 20px;
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-card);
}

.chat-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.conversation-details h2 {
  margin: 0 0 5px 0;
  font-size: 1.2rem;
  color: var(--text-primary);
}

.conversation-details p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--danger-color);
}

.connection-status.connected {
  color: var(--success-color);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: currentColor;
}

.chat-body {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background: var(--bg-secondary);
}

.empty-messages {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-muted);
  text-align: center;
}

.empty-messages i {
  font-size: 3rem;
  margin-bottom: 15px;
}

.message-wrapper {
  display: flex;
  flex-direction: column;
  margin-bottom: 15px;
}

.message {
  display: inline-block;
  max-width: 70%;
}

.customer-message {
  align-self: flex-start;
}

.agent-message, .admin-message {
  align-self: flex-end;
}

.bot-message {
  align-self: flex-start;
}

.system-message {
  align-self: center;
  font-style: italic;
}

.message-content {
  padding: 12px 16px;
  border-radius: 18px;
  word-wrap: break-word;
}

.customer-message .message-content {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.agent-message .message-content,
.admin-message .message-content {
  background: var(--primary-color);
  color: white;
}

.bot-message .message-content {
  background: var(--secondary-color);
  color: white;
}

.system-message .message-content {
  background: var(--bg-hover);
  color: var(--text-muted);
}

.sender-info {
  font-size: 0.8rem;
  font-weight: 600;
  margin-bottom: 4px;
  opacity: 0.8;
}

.content {
  margin-bottom: 4px;
}

.timestamp {
  font-size: 0.7rem;
  opacity: 0.7;
  margin-top: 4px;
}

.message-input {
  padding: 20px;
  border-top: 1px solid var(--border-color);
  background: var(--bg-card);
}

.input-form {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.input-group {
  display: flex;
  gap: 10px;
}

.input-group .form-control {
  flex: 1;
  padding: 12px 16px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background: var(--bg-input);
  color: var(--text-primary);
}

.input-group .form-control:focus {
  outline: none;
  border-color: var(--primary-color);
}

.input-group .form-control:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

@media (max-width: 768px) {
  .chat-header {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }

  .chat-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .message {
    max-width: 85%;
  }
}
</style>
