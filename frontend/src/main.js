import './assets/main.css'
import { createApp } from 'vue'
import { createRouter, createWebHistory } from 'vue-router'
import axios from 'axios'
import App from './App.vue'

// Import components
import LoginComponent from './components/Login.vue'
import DashboardComponent from './components/Dashboard.vue'
import ConversationsComponent from './components/Conversations.vue'
import CustomersComponent from './components/Customers.vue'
import TeamsComponent from './components/Teams.vue'
import OrganizationsComponent from './components/Organizations.vue'
import UsersComponent from './components/Users.vue'
import ChatComponent from './components/Chat.vue'

// API Configuration
const API_BASE_URL = 'http://localhost:8000'
axios.defaults.baseURL = API_BASE_URL
axios.defaults.withCredentials = true

// Router configuration
const routes = [
    { path: '/', redirect: '/login' },
    { path: '/login', component: LoginComponent, meta: { requiresGuest: true } },
    { path: '/dashboard', component: DashboardComponent, meta: { requiresAuth: true } },
    { path: '/conversations', name: 'Conversations', component: ConversationsComponent, meta: { requiresAuth: true } },
    { path: '/conversations/:id', name: 'SingleChat', component: ChatComponent, meta: { requiresAuth: true } },
    { path: '/chat', name: 'Chat', component: ChatComponent, meta: { requiresAuth: true } },
    { path: '/customers', component: CustomersComponent, meta: { requiresAuth: true } },
    { path: '/teams', component: TeamsComponent, meta: { requiresAuth: true, requiresAdmin: true } },
    { path: '/organizations', component: OrganizationsComponent, meta: { requiresAuth: true, requiresAdmin: true } },
    { path: '/users', component: UsersComponent, meta: { requiresAuth: true, requiresAdmin: true } }
]

const router = createRouter({
    history: createWebHistory(),
    routes
})

// Route guards
router.beforeEach(async (to, from, next) => {
    // Check if route requires authentication
    if (to.meta.requiresAuth) {
        try {
            const response = await axios.get('/api/auth/me')
            const user = response.data

            // Debug logging for admin access
            console.log('User data:', user)
            console.log('Route requires admin:', to.meta.requiresAdmin)
            console.log('User is_admin:', user.is_admin)
            console.log('User roles:', user.roles)

            // Check if route requires admin privileges
            if (to.meta.requiresAdmin && !user.is_admin) {
                console.warn('Access denied: Admin privileges required')
                console.warn('User email:', user.email)
                console.warn('User is_admin flag:', user.is_admin)
                console.warn('User roles:', user.roles)
                next('/dashboard') // Redirect to dashboard if not admin
                return
            }

            console.log('Access granted to route:', to.path)
            next() // User is authenticated and has required privileges
        } catch (error) {
            console.warn('Authentication required')
            next('/login') // Redirect to login if not authenticated
        }
    } else if (to.meta.requiresGuest) {
        // Check if user is already authenticated (for login page)
        try {
            await axios.get('/api/auth/me')
            next('/dashboard') // Redirect to dashboard if already authenticated
        } catch (error) {
            next() // User is not authenticated, can access guest pages
        }
    } else {
        next() // No special requirements
    }
})

// Create Vue app
const app = createApp(App)

// Global properties
app.config.globalProperties.$api = axios
app.config.globalProperties.$API_BASE_URL = API_BASE_URL

// Use router
app.use(router)

// Mount the app
app.mount('#app')
