# Database Configuration
DATABASE_URL=postgresql+asyncpg://yupcha_user:yupcha_password@localhost:5432/yupcha_db

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# JWT Configuration
SECRET_KEY=your-super-secret-key-here-change-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# S3/MinIO Configuration
S3_ENDPOINT_URL=http://localhost:9000
S3_ACCESS_KEY_ID=minioadmin
S3_SECRET_ACCESS_KEY=minioadmin
S3_BUCKET_NAME=yupcha-assets
S3_REGION=us-east-1

# Email Configuration (FastAPI-Mail)
# For development - use Mailtrap or similar
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-email-password
MAIL_FROM=<EMAIL>
MAIL_PORT=587
MAIL_SERVER=smtp.gmail.com
MAIL_FROM_NAME="Yupcha Support"
MAIL_STARTTLS=true
MAIL_SSL_TLS=false
USE_CREDENTIALS=true
VALIDATE_CERTS=true

# Email Templates
MAIL_TEMPLATE_FOLDER=templates

# Application Settings
APP_NAME="Yupcha Customer Bot AI"
APP_VERSION="1.0.0"
DEBUG=true

# CORS Settings
ALLOWED_ORIGINS=["http://localhost:5173", "http://localhost:3000"]

# Notification Settings
ENABLE_EMAIL_NOTIFICATIONS=true
ADMIN_EMAIL=<EMAIL>
