# The format is: p, subject, object, action
# Role groupings: g, user, role

# --- User Role Assignments ---
g, <EMAIL>, Admin
g, <EMAIL>, Agent

# --- Admin Role ---
# Admins can do anything (*) on any resource (*)
p, Admin, *, *

# --- Manager Role ---
# Managers can view all users in their organization
p, Manager, organization:*/users, read
p, Manager, organization:*/users, edit
p, Manager, organization:*/users, create
# Managers can manage teams in their organization
p, Manager, organization:*/teams, *
# Managers can view organizations
p, Manager, organization:*, read
p, Manager, organization:*, edit
# Managers can manage canned responses
p, Manager, canned-responses, *

# --- Supervisor Role ---
# Supervisors can manage conversations and users in their team
p, Supervisor, team:*/conversations, *
p, Supervisor, team:*/users, read
p, Supervisor, team:*/users, edit
p, Supervisor, conversations, *
p, Supervisor, canned-responses, read
p, Supervisor, canned-responses, create

# --- HR Role ---
# HR can manage users but not system settings
p, HR, users, *
p, HR, organization:*/users, *
p, HR, teams, read
p, HR, canned-responses, read

# --- Agent Role ---
# Agents can read conversations they are assigned to
p, Agent, conversations, read
p, Agent, conversations, edit
# Agents can create messages in conversations
p, Agent, conversation:*/messages, create
p, Agent, conversation:*/messages, read
# Agents can view and use canned responses
p, Agent, canned-responses, read
p, Agent, canned-responses, use
# Agents can view customers
p, Agent, customers, read
# Agents can view their own profile
p, Agent, profile, read
p, Agent, profile, edit

# --- Sales Role ---
# Sales team members can access customer data and conversations
p, Sales, customers, *
p, Sales, conversations, read
p, Sales, conversations, create
p, Sales, canned-responses, read
p, Sales, leads, *

# --- Viewer Role ---
# Viewers have read-only access to most resources
p, Viewer, conversations, read
p, Viewer, customers, read
p, Viewer, users, read
p, Viewer, teams, read
p, Viewer, organizations, read
p, Viewer, canned-responses, read

# --- Test Role ---
# Test role for development/testing purposes
p, Test, test-resources, *
p, Test, conversations, read
