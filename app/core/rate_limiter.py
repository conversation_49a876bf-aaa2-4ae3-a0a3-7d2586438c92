# yupcha-customerbot-ai/app/core/rate_limiter.py

import logging
from fastapi import WebSocket, HTTPException, status
from app.core.config import settings
from app.core.websocket_manager import redis_client

logger = logging.getLogger(__name__)

class RateLimiter:
    """
    A simple rate limiter using Redis to prevent WebSocket message spam.
    Inspired by the OpenChat architecture article.
    """
    
    def __init__(
        self,
        identifier: str,
        limit: int = settings.RATE_LIMIT_MESSAGES,
        window: int = settings.RATE_LIMIT_WINDOW_SECONDS
    ):
        self.redis = redis_client
        self.key = f"rate-limit:{identifier}"
        self.limit = limit
        self.window = window

    async def check(self) -> bool:
        """
        Checks if the request is within the rate limit.
        Increments the counter and sets expiry on the first request in a window.
        
        Returns:
            bool: True if the request is allowed, False otherwise.
        """
        try:
            # Use a pipeline for atomic operations
            pipe = self.redis.pipeline()
            pipe.incr(self.key)
            pipe.ttl(self.key)
            count, ttl = await pipe.execute()

            # If the key is new (ttl is -1), set its expiry
            if ttl == -1:
                await self.redis.expire(self.key, self.window)

            return int(count) <= self.limit
        
        except Exception as e:
            logger.error(f"❌ Rate limiting check failed: {e}")
            # Fail open in case of Redis error
            return True

async def rate_limit_dependency(websocket: WebSocket, identifier: str) -> None:
    """
    A FastAPI-style dependency for WebSocket endpoints to enforce rate limiting.
    """
    limiter = RateLimiter(identifier=identifier)
    if not await limiter.check():
        error_message = {
            "type": "error",
            "detail": f"Rate limit exceeded. Try again in {limiter.window} seconds."
        }
        await websocket.send_json(error_message)
        # We can't use WebSocketException here easily, so we close the connection
        # with a custom code to indicate a policy violation.
        await websocket.close(code=status.WS_1008_POLICY_VIOLATION, reason="Rate limit exceeded")
        # Raising an exception here will ensure the endpoint logic doesn't run.
        raise HTTPException(status_code=status.HTTP_429_TOO_MANY_REQUESTS)