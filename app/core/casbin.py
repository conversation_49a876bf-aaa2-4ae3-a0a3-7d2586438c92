# yupcha-customerbot-ai/app/core/casbin.py

import casbin
from pathlib import Path
from typing import List
import logging

logger = logging.getLogger(__name__)

# Define paths to the Casbin model and policy files
# This makes the paths relative to this file's location
casbin_model_path = Path(__file__).parent.parent / "casbin/model.conf"
casbin_policy_path = Path(__file__).parent.parent / "casbin/policy.csv"

# Create a global enforcer instance
# In a production app, you might use the SQLAlchemy adapter to store policies in the DB
enforcer = None

def initialize_enforcer():
    """Initialize the Casbin enforcer with model and policy files."""
    global enforcer
    try:
        enforcer = casbin.Enforcer(str(casbin_model_path), str(casbin_policy_path))
        logger.info("Casbin enforcer initialized successfully")
        return enforcer
    except Exception as e:
        logger.error(f"Failed to initialize Casbin enforcer: {e}")
        raise

def get_enforcer() -> casbin.Enforcer:
    """Dependency to get the Casbin enforcer."""
    global enforcer
    if enforcer is None:
        enforcer = initialize_enforcer()
    return enforcer

def add_user_roles(user_email: str, roles: List[str]):
    """Add role assignments for a user using grouping policy."""
    enforcer = get_enforcer()

    # Remove existing role groupings for the user first
    enforcer.delete_roles_for_user(user_email)

    # Add new role groupings
    for role in roles:
        enforcer.add_grouping_policy(user_email, role)
        logger.debug(f"Added role '{role}' for user '{user_email}'")

def remove_user_roles(user_email: str, roles: List[str] = None):
    """Remove role assignments for a user."""
    enforcer = get_enforcer()

    if roles is None:
        # Remove all roles for the user
        enforcer.delete_roles_for_user(user_email)
        logger.debug(f"Removed all roles for user '{user_email}'")
    else:
        # Remove specific roles
        for role in roles:
            enforcer.remove_grouping_policy(user_email, role)
            logger.debug(f"Removed role '{role}' for user '{user_email}'")

def check_permission(user_email: str, resource: str, action: str) -> bool:
    """Check if a user has permission to perform an action on a resource."""
    enforcer = get_enforcer()

    # Check permission directly using the enforcer
    # The enforcer will automatically check roles through the g() function in the model
    result = enforcer.enforce(user_email, resource, action)

    if result:
        logger.debug(f"Permission granted: {user_email} can {action} on {resource}")
    else:
        logger.debug(f"Permission denied: {user_email} cannot {action} on {resource}")

    return result

def get_user_permissions(user_email: str) -> List[List[str]]:
    """Get all permissions for a user."""
    enforcer = get_enforcer()
    return enforcer.get_permissions_for_user(user_email)

def get_user_roles(user_email: str) -> List[str]:
    """Get all roles for a user."""
    enforcer = get_enforcer()
    return enforcer.get_roles_for_user(user_email)

def reload_policy():
    """Reload the policy from the file."""
    enforcer = get_enforcer()
    enforcer.load_policy()
    logger.info("Casbin policy reloaded")

# Initialize the enforcer when the module is imported
try:
    initialize_enforcer()
except Exception as e:
    logger.warning(f"Could not initialize Casbin enforcer on import: {e}")
