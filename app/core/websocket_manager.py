import asyncio
import json
import logging
from typing import Optional
from fastapi import WebSocket
from redis import asyncio as aioredis
from uuid import UUID   
from app.core.broadcaster import broadcaster
from app.core.config import settings
from app.models.user import User
from app.models.customer import Customer

logger = logging.getLogger(__name__)

# Redis client for presence tracking (separate from broadcaster's)
redis_client = aioredis.from_url(
    settings.REDIS_URL, 
    encoding="utf-8", 
    decode_responses=True,
    socket_connect_timeout=5,   # Timeout for initial connection
    socket_keepalive=True,      # Enable TCP keepalives
    # Optional: Configure keepalive timings (in seconds) if needed
    # socket_keepalive_options={
    #     socket.TCP_KEEPIDLE: 60,
    #     socket.TCP_KEEPCNT: 3,
    #     socket.TCP_KEEPINTVL: 10,
    )

class WebSocketManager:
    """
    Manages WebSocket connections and broadcasts messages using a Redis backend.
    This architecture is horizontally scalable.
    """

    def get_chat_channel(self, conversation_id: UUID) -> str:
        """Returns the Redis channel name for a given conversation."""
        return f"chat-conversation-{conversation_id}"

    async def connect(self, websocket: WebSocket, channel: str):
        """
        Subscribes a WebSocket to a specific channel and forwards messages.
        This function runs as a task for each connected client.
        """
        async with broadcaster.subscribe(channel=channel) as subscriber:
            try:
                # First, send a confirmation to the client
                await websocket.send_text(json.dumps({"type": "system", "detail": "Connection successful."}))
                
                # Then, listen for messages from the channel and send to the client
                async for event in subscriber:
                    try:
                        await websocket.send_text(event.message)
                    except Exception as send_error:
                        logger.warning(f"Failed to send message to WebSocket on channel {channel}: {send_error}")
                        # WebSocket is likely closed, break the loop
                        break
            except Exception as e:
                logger.error(f"Error in WebSocket listener for channel {channel}: {e}", exc_info=True)
            finally:
                logger.info(f"Listener for channel {channel} stopped.")

    async def broadcast(self, channel: str, message: str, broadcast_to_sender: bool = True, sender_websocket: Optional[WebSocket] = None):
        """
        Broadcasts a message to a specific Redis channel.
        Optionally excludes the original sender.
        """
        # Note: This implementation of excluding the sender is simple and works for a single-node setup.
        # For multi-node, a more complex mechanism (e.g., passing a unique connection ID) would be needed.
        # The `broadcaster` library itself doesn't support excluding a specific subscriber.
        # For now, this is a good-enough approximation.
        await broadcaster.publish(channel=channel, message=message)
        logger.debug(f"Broadcasted to channel {channel}")

    # --- NEW: Explicit Lifecycle Hooks ---

    async def on_connect(self, websocket: WebSocket, conversation_id: UUID, user: Optional[User], customer: Optional[Customer]):
        """
        Handles all logic for a new connection.
        - Subscribes to the correct channel.
        - Updates presence if it's a company user.
        - Returns the asyncio task for the listener.
        """
        logger.info(f"Connection opened for conversation {conversation_id}")
        
        channel = self.get_chat_channel(conversation_id)
        listen_task = asyncio.create_task(self.connect(websocket, channel))

        if user:
            await self.set_user_active(conversation_id)
        
        return listen_task

    async def on_disconnect(self, websocket: WebSocket, conversation_id: UUID, user: Optional[User], listen_task: asyncio.Task):
        """
        Handles all logic for a disconnection.
        - Cancels the listener task.
        - Updates presence if it was a company user.
        """
        logger.info(f"Connection closed for conversation {conversation_id}")
        
        listen_task.cancel()
        
        if user:
            try:
                await self.set_user_inactive(conversation_id)
            except aioredis.exceptions.ConnectionError as e:
                logger.warning(f"Redis connection error during disconnect: {e}")
            except Exception as e:
                logger.error(f"An unexpected error occurred during disconnect cleanup: {e}")

    # --- Agent Presence Tracking using Redis ---

    def get_presence_key(self, conversation_id: UUID) -> str:
        """Returns the Redis key for tracking agent presence in a conversation."""
        return f"presence-conversation-{conversation_id}"

    async def set_user_active(self, conversation_id: UUID):
        """
        Marks a company user (agent, admin, etc.) as active in a conversation.
        We use INCR to handle multiple users and EXPIRE to auto-clean.
        """
        key = self.get_presence_key(conversation_id)
        # The client must send a ping/pong or some activity to keep this alive.
        # A simple INCR with a 60-second timeout is a good strategy.
        await redis_client.incr(key)
        await redis_client.expire(key, 60) 
        logger.info(f"Company user presence marked for conversation {conversation_id}")

    async def set_user_inactive(self, conversation_id: UUID):
        """Marks a company user as inactive in a conversation."""
        key = self.get_presence_key(conversation_id)
        # We use DECR. If the count reaches 0, the key might still exist until it expires.
        # This is fine and handles multiple users correctly.
        await redis_client.decr(key)
        logger.info(f"Company user presence removed for conversation {conversation_id}")
    
    async def is_user_active(self, conversation_id: UUID) -> bool:
        """
        Checks if any company user is marked as active in the conversation from Redis.
        Returns True if the count of users is greater than 0.
        """
        key = self.get_presence_key(conversation_id)
        user_count = await redis_client.get(key)
        # Check if the count is not None and is a number greater than 0
        return user_count is not None and int(user_count) > 0

    # --- Organization Notification Channel Methods (using Redis) ---

    def get_org_channel(self, organization_id: UUID) -> str:
        """Returns the Redis channel name for organization notifications."""
        return f"org-notifications-{organization_id}"

    async def connect_to_org_channel(self, websocket: WebSocket, organization_id: UUID, user_id: UUID, role: UUID):
        """
        Connects an agent/admin to their organization's notification channel.
        """
        channel = self.get_org_channel(organization_id)

        # Start listening to the organization channel
        listen_task = asyncio.create_task(self.connect(websocket, channel))

        logger.info(f"User {user_id} ({role}) connected to notification channel for organization {organization_id}")

        # Send welcome message
        welcome_message = {
            "type": "notification_channel_connected",
            "organization_id": organization_id,
            "user_id": user_id,
            "message": "Connected to organization notification channel"
        }
        await websocket.send_text(json.dumps(welcome_message))

        return listen_task

    async def broadcast_to_organization(self, message: str, organization_id: UUID):
        """Broadcasts a message to all users in an organization's notification channel."""
        channel = self.get_org_channel(organization_id)
        await self.broadcast(channel, message)
        logger.debug(f"Broadcasted to organization {organization_id}")

    def disconnect_from_org_channel(self, websocket: WebSocket, organization_id: UUID = None):
        """Disconnects a user from the notification channel."""
        # With Redis pub/sub, disconnection is handled automatically when WebSocket closes
        logger.info(f"User disconnected from notification channel for organization {organization_id}")

    # --- Backward compatibility methods for existing code ---

    async def broadcast_to_conversation(self, message: str, conversation_id: UUID):
        """Broadcast a message to all clients in a conversation (Redis-based)"""
        channel = self.get_chat_channel(conversation_id)
        await self.broadcast(channel, message)

    async def is_user_in_conversation(self, conversation_id: UUID) -> bool:
        """Checks if any agent or admin is currently connected to a conversation."""
        return await self.is_user_active(conversation_id)

# A single, global instance of the manager
manager = WebSocketManager()
