from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()

# Import all the models here so that Base.metadata.create_all() can find them
from app.models.role import Role  # NEW: Import Role model
from app.models.user import User
from app.models.organization import Organization
from app.models.team import Team
from app.models.customer import Customer
from app.models.chat import Conversation, Message
from app.models.asset import Asset
