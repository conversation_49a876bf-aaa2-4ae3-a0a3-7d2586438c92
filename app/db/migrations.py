import logging
from sqlalchemy import inspect, text
from sqlalchemy.exc import SQLAlchemyError
from app.db.session import engine
from app.db.base import Base # Import Base

# Import all models from the central __init__.py to ensure they are registered
from app.models import *

logger = logging.getLogger(__name__)

class DatabaseManager:
    """A manager for direct SQLAlchemy schema operations."""
    def __init__(self):
        self.engine = engine
        self.metadata = Base.metadata
        self.inspector = inspect(self.engine)

    def table_exists(self, table_name: str) -> bool:
        """Check if a table exists in the database."""
        return self.inspector.has_table(table_name)

    def get_existing_tables(self) -> list[str]:
        """Get a list of all table names in the public schema."""
        return self.inspector.get_table_names()

    def create_all_tables(self, recreate: bool = False):
        """Create all tables defined in models. Drops all first if recreate is True."""
        try:
            if recreate:
                logger.warning("Dropping all existing tables...")
                self.metadata.drop_all(bind=self.engine)
                logger.info("All tables dropped.")
            
            logger.info("Creating all defined tables...")
            self.metadata.create_all(bind=self.engine)
            
            tables = self.get_existing_tables()
            logger.info(f"✅ Successfully created/verified {len(tables)} tables.")
            return True
        except SQLAlchemyError as e:
            logger.error(f"❌ Error during table creation: {e}", exc_info=True)
            return False

    def get_database_info(self) -> dict:
        """Get summary information about the database schema."""
        try:
            expected_tables = set(self.metadata.tables.keys())
            existing_tables = set(self.get_existing_tables())
            
            info = {
                "database_url": str(self.engine.url).split(' @')[-1], # Hide credentials
                "total_tables": len(existing_tables),
                "expected_tables": sorted(list(expected_tables)),
                "existing_tables": sorted(list(existing_tables)),
                "missing_tables": sorted(list(expected_tables - existing_tables)),
                "extra_tables": sorted(list(existing_tables - expected_tables)),
            }
            return info
        except Exception as e:
            logger.error(f"❌ Error getting database info: {e}", exc_info=True)
            return None

    def execute_sql(self, sql_command: str):
        """Execute a raw SQL command."""
        try:
            with self.engine.connect() as connection:
                with connection.begin():  # Ensures transactional execution
                    connection.execute(text(sql_command))
                logger.info(f"✅ Successfully executed SQL: {sql_command[:100]}...")
        except SQLAlchemyError as e:
            logger.error(f"❌ SQL execution failed: {e}", exc_info=True)
            raise