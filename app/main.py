from contextlib import asynccontextmanager
from app.core.scheduler import setup_scheduler, scheduler
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from scalar_fastapi import get_scalar_api_reference
from app.api.api import api_router
from app.core.config import settings
from app.db.session import AsyncSessionLocal
from app.crud import crud_user, crud_organization
from app.schemas.user import UserCreate
from app.schemas.organization import OrganizationCreate
from app.models.user import UserRole
from app.core.s3 import s3_manager

from fastapi_cache import FastAPICache
from fastapi_cache.backends.redis import RedisBackend
from fastapi.staticfiles import StaticFiles
from redis import asyncio as aioredis
from app.core.broadcaster import broadcaster
from app.services.notification_service import notification_service

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan events"""
    print("🚀 Starting Yupcha Customer Bot AI...")
    
    await broadcaster.connect()

    try:
        await s3_manager.ensure_bucket_exists()
        print("✅ S3/MinIO bucket initialized successfully")
    except Exception as e:
        print(f"⚠️  S3/MinIO bucket initialization failed: {e}")

    try:
        redis = aioredis.from_url(settings.REDIS_URL, encoding="utf-8", decode_responses=True)
        FastAPICache.init(RedisBackend(redis), prefix="fastapi-cache")
        print("✅ Redis cache initialized successfully")
    except Exception as e:
        print(f"⚠️ Redis connection failed: {e}")
        print("   Caching will be disabled.")

    # Initialize notification service
    try:
        await notification_service.startup()
        print("✅ Notification service initialized successfully")
    except Exception as e:
        print(f"⚠️ Notification service initialization failed: {e}")
        
    #* Create default users
    #* await create_default_users()
    setup_scheduler()
    
    yield

    await broadcaster.disconnect()

    # Shutdown notification service
    try:
        await notification_service.shutdown()
        print("✅ Notification service shutdown complete")
    except Exception as e:
        print(f"⚠️ Error shutting down notification service: {e}")

    # Shutdown
    scheduler.shutdown()
    FastAPICache.clear()
    print("🛑 Shutting down Yupcha Customer Searvice app...")

app = FastAPI(
    title=settings.APP_NAME,
    description="Advanced customer support system with real-time chat, file sharing, and notifications",
    debug=settings.DEBUG,
    lifespan=lifespan
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS, 
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.include_router(api_router, prefix="/api")

app.mount("/test-ui", StaticFiles(directory="tests", html=True), name="test-ui")

async def create_default_users():
    """Create default admin and agent users on startup"""
    async with AsyncSessionLocal() as db:
        try:
            # Get the default organization (created in migration)
            default_org = await crud_organization.get_organization(db=db, organization_id=1)

            # Check if admin exists and update password if needed
            admin_user = await crud_user.get_user_by_email(db=db, email="<EMAIL>")
            if not admin_user:
                admin = UserCreate(
                    email="<EMAIL>",
                    password="adminpassword",
                    full_name="Admin User",
                    role=UserRole.agent,  # All users are agents
                    is_admin=True,  # But this one has admin privileges
                    organization_id=default_org.id if default_org else None
                )
                await crud_user.create_user(db, user=admin)
                print("✅ Default admin user created: <EMAIL> / adminpassword")
            else:
                # Update password if it's not hashed
                from app.core.security import get_password_hash
                if not admin_user.hashed_password.startswith('$2b$'):
                    admin_user.hashed_password = get_password_hash('adminpassword')
                    await db.commit()
                    print("✅ Admin password updated to use proper hashing")

            # Check if agent exists and update password if needed
            agent_user = await crud_user.get_user_by_email(db=db, email="<EMAIL>")
            if not agent_user:
                agent = UserCreate(
                    email="<EMAIL>",
                    password="agentpassword",
                    full_name="Agent One",
                    role=UserRole.agent,  # Regular agent
                    is_admin=False,  # No admin privileges
                    organization_id=default_org.id if default_org else None
                )
                await crud_user.create_user(db, user=agent)
                print("✅ Default agent user created: <EMAIL> / agentpassword")
            else:
                # Update password if it's not hashed
                from app.core.security import get_password_hash
                if not agent_user.hashed_password.startswith('$2b$'):
                    agent_user.hashed_password = get_password_hash('agentpassword')
                    await db.commit()
                    print("✅ Agent password updated to use proper hashing")

        except Exception as e:
            print(f"❌ Error creating default users: {e}")

@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "message": "Welcome to Yupcha Customer Bot AI",
        "docs": "/docs",
        "redoc": "/redoc",
        "openapi": "/openapi.json",
        "endpoints": {
            "users": "/api/users/",
            "conversations": "/api/conversations/",
            "messages": "/api/messages/",
            "media": "/api/media/",
            "websocket_chat": "/api/ws/chat/{conversation_id}",
            "websocket_notifications": "/api/ws/agent-notifications",
            "health": "/health",
            "scalar_docs": "/scalar"
        }
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "app_name": settings.APP_NAME,
        "database": "connected"
    }

@app.get("/api")
async def api_info():
    """API information endpoint"""
    return {
        "api_version": "v1",
        "app_name": settings.APP_NAME,
        "description": "FastAPI-based customer chatbot with WebSocket support",
        "endpoints": {
            "authentication": {
                "login": "POST /api/auth/login",
                "logout": "POST /api/auth/logout",
                "me": "GET /api/auth/me"
            },
            "users": {
                "create": "POST /api/users/",
                "list_all": "GET /api/users/",
                "list_agents": "GET /api/users/agents",
                "list_admins": "GET /api/users/admins",
                "get": "GET /api/users/{id}"
            },
            "organizations": {
                "list": "GET /api/organizations/",
                "create": "POST /api/organizations/",
                "get": "GET /api/organizations/{id}"
            },
            "customers": {
                "list": "GET /api/customers/",
                "create": "POST /api/customers/",
                "get": "GET /api/customers/{id}"
            },
            "teams": {
                "list": "GET /api/teams/",
                "create": "POST /api/teams/",
                "get": "GET /api/teams/{id}"
            },
            "conversations": {
                "list": "GET /api/conversations/",
                "create": "POST /api/conversations/",
                "get": "GET /api/conversations/{id}",
                "messages": "GET /api/conversations/{id}/messages",
                "assign_team": "POST /api/conversations/{id}/assign/{team_id}",
                "unassigned": "GET /api/conversations/unassigned",
                "by_team": "GET /api/conversations/team/{team_id}"
            },
            "messages": {
                "get": "GET /api/messages/{id}",
                "delete": "DELETE /api/messages/{id}",
                "restore": "PATCH /api/messages/{id}/restore"
            },
            "media": {
                "upload": "POST /api/media/upload",
                "upload_public": "POST /api/media/upload/public",
                "list_assets": "GET /api/media/assets",
                "get_asset": "GET /api/media/assets/{id}",
                "delete_asset": "DELETE /api/media/assets/{id}"
            },
            "websocket": {
                "chat": "WS /api/ws/chat/{conversation_id}",
                "agent_notifications": "WS /api/ws/agent-notifications"
            }
        },
        "documentation": {
            "swagger": "/docs",
            "redoc": "/redoc",
            "openapi_schema": "/openapi.json"
        }
    }

@app.get("/scalar", include_in_schema=False)
async def scalar_html():
    """Scalar API documentation"""
    return get_scalar_api_reference(
        openapi_url=app.openapi_url,
        title=app.title,
    )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host=settings.HOST, port=settings.PORT)
