from datetime import datetime, timezone, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, delete, or_, Select
from typing import List, Optional
from uuid import UUID
from app.models.customer import Customer
from app.schemas.customer import CustomerCreate, CustomerUpdate

def get_customers_query(organization_ids: List[UUID] = None, organization_id: UUID = None, search: Optional[str] = None) -> Select:
    """
    Returns a SQLAlchemy SELECT statement for fetching customers with optional search.
    Supports both single organization_id (for backward compatibility) and multiple organization_ids.
    """
    # Handle backward compatibility
    if organization_id is not None and organization_ids is None:
        organization_ids = [organization_id]

    if not organization_ids:
        raise ValueError("Either organization_id or organization_ids must be provided")

    stmt = select(Customer).where(Customer.organization_id.in_(organization_ids), Customer.is_deleted == False)
    if search:
        search_pattern = f"%{search}%".lower()
        stmt = stmt.where(or_(Customer.name.ilike(search_pattern), Customer.email.ilike(search_pattern)))
    return stmt.order_by(Customer.name)

async def create_customer(db: AsyncSession, customer: CustomerCreate) -> Customer:
    """Create a new customer"""
    db_customer = Customer(**customer.model_dump())
    db.add(db_customer)
    await db.commit()
    await db.refresh(db_customer)
    return db_customer

async def get_customer(db: AsyncSession, customer_id: UUID, include_deleted: bool = False) -> Optional[Customer]:
    """Get a customer by ID, optionally including soft-deleted ones."""
    stmt = select(Customer).where(Customer.id == customer_id)
    #* MODIFIED: Conditionally filter out deleted records
    if not include_deleted:
        stmt = stmt.where(Customer.is_deleted == False)
    result = await db.execute(stmt)
    return result.scalar_one_or_none()

async def get_customers(db: AsyncSession, skip: int = 0, limit: int = 100) -> List[Customer]:
    """Get all non-deleted customers with pagination."""
    #* MODIFIED: Always filter out deleted records
    stmt = select(Customer).where(Customer.is_deleted == False).offset(skip).limit(limit)
    result = await db.execute(stmt)
    return result.scalars().all()

async def get_or_create_customer(db: AsyncSession, customer_id: UUID, name: Optional[str] = None, email: Optional[str] = None, organization_id: Optional[UUID] = None) -> Customer:
    """
    Retrieves a customer by their unique customer_id. If not found, creates a new one.
    This is essential for the public-facing chat widget.
    """
    customer = await get_customer_by_customer_id(db, customer_id=customer_id)
    if not customer:
        # If no organization_id provided, get the first available organization
        if not organization_id:
            from app.crud import crud_organization
            orgs = await crud_organization.get_organizations(db, limit=1)
            if orgs:
                organization_id = orgs[0].id
            else:
                raise ValueError("No organizations found. Please create an organization first.")

        customer_in = CustomerCreate(
            customer_id=customer_id,
            name=name,
            email=email,
            organization_id=organization_id
        )
        customer = await create_customer(db, customer=customer_in)
    return customer

async def get_customer_by_customer_id(db: AsyncSession, customer_id: UUID) -> Optional[Customer]:
    """Get a customer by external customer_id"""
    result = await db.execute(select(Customer).filter(Customer.customer_id == customer_id))
    return result.scalar_one_or_none()

async def get_customer_by_email(db: AsyncSession, email: str) -> Optional[Customer]:
    """Get a customer by email"""
    result = await db.execute(select(Customer).filter(Customer.email == email))
    return result.scalar_one_or_none()

async def update_customer(db: AsyncSession, customer_id: UUID, customer_update: CustomerUpdate) -> Optional[Customer]:
    """Update a customer"""
    customer = await get_customer(db, customer_id)
    if customer:
        update_data = customer_update.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(customer, field, value)
        await db.commit()
        await db.refresh(customer)
    return customer

async def delete_customer(db: AsyncSession, customer_id: UUID) -> bool:
    """Delete a customer"""
    customer = await get_customer(db, customer_id)
    if customer:
        await db.delete(customer)
        await db.commit()
        return True
    return False

async def soft_delete_customer(db: AsyncSession, customer_id: UUID) -> Optional[Customer]:
    """Soft delete a customer by setting is_deleted to True."""
    customer = await get_customer(db, customer_id=customer_id)
    if customer:
        customer.is_deleted = True
        customer.deleted_at = datetime.utcnow()
        await db.commit()
        await db.refresh(customer)
    return customer

async def restore_customer(db: AsyncSession, customer_id: UUID) -> Optional[Customer]:
    """Restore a soft-deleted customer."""
    customer = await get_customer(db, customer_id=customer_id, include_deleted=True)
    if customer and customer.is_deleted:
        customer.is_deleted = False
        customer.deleted_at = None
        await db.commit()
        await db.refresh(customer)
    return customer

async def purge_deleted_customers(db: AsyncSession) -> int:
    """Permanently delete customers soft-deleted more than 90 days ago."""
    purge_date = datetime.now(timezone.utc) - timedelta(days=90)
    stmt = delete(Customer).where(Customer.is_deleted == True, Customer.deleted_at <= purge_date)
    result = await db.execute(stmt)
    await db.commit()
    return result.rowcount