from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import selectinload
from sqlalchemy import func, and_, or_
from typing import List, Optional

from app.models.role import Role, user_role_association
from app.models.user import User
from app.schemas.role import RoleCreate, RoleUpdate


class CRUDRole:
    async def create_role(self, db: AsyncSession, role: RoleCreate) -> Role:
        """Create a new role"""
        db_role = Role(
            name=role.name,
            description=role.description,
            is_system_role=role.is_system_role or False
        )
        db.add(db_role)
        await db.commit()
        await db.refresh(db_role)
        return db_role

    async def get_role(self, db: AsyncSession, role_id: int) -> Optional[Role]:
        """Get role by ID"""
        result = await db.execute(
            select(Role).where(Role.id == role_id)
        )
        return result.scalar_one_or_none()

    async def get_role_by_name(self, db: AsyncSession, name: str) -> Optional[Role]:
        """Get role by name"""
        result = await db.execute(
            select(Role).where(Role.name == name)
        )
        return result.scalar_one_or_none()

    async def get_roles(
        self, 
        db: AsyncSession, 
        skip: int = 0, 
        limit: int = 100,
        active_only: bool = True,
        include_system: bool = True
    ) -> List[Role]:
        """Get all roles with filtering"""
        query = select(Role)
        
        if active_only:
            query = query.where(Role.is_active == True)
        
        if not include_system:
            query = query.where(Role.is_system_role == False)
        
        query = query.offset(skip).limit(limit).order_by(Role.name)
        
        result = await db.execute(query)
        return result.scalars().all()

    async def update_role(self, db: AsyncSession, role_id: int, role_update: RoleUpdate) -> Optional[Role]:
        """Update a role"""
        result = await db.execute(
            select(Role).where(Role.id == role_id)
        )
        db_role = result.scalar_one_or_none()
        
        if not db_role:
            return None
        
        update_data = role_update.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_role, field, value)
        
        await db.commit()
        await db.refresh(db_role)
        return db_role

    async def delete_role(self, db: AsyncSession, role_id: int) -> bool:
        """Delete a role (only if not system role and no users assigned)"""
        result = await db.execute(
            select(Role).where(Role.id == role_id)
        )
        db_role = result.scalar_one_or_none()
        
        if not db_role:
            return False
        
        # Prevent deletion of system roles
        if db_role.is_system_role:
            return False
        
        # Check if any users have this role
        user_count_result = await db.execute(
            select(func.count(user_role_association.c.user_id))
            .where(user_role_association.c.role_id == role_id)
        )
        user_count = user_count_result.scalar()
        
        if user_count > 0:
            return False
        
        await db.delete(db_role)
        await db.commit()
        return True

    async def assign_role_to_user(self, db: AsyncSession, user_id: int, role_id: int) -> bool:
        """Assign a role to a user"""
        # Check if user exists
        user_result = await db.execute(
            select(User).where(User.id == user_id)
        )
        user = user_result.scalar_one_or_none()
        if not user:
            return False
        
        # Check if role exists
        role_result = await db.execute(
            select(Role).where(Role.id == role_id)
        )
        role = role_result.scalar_one_or_none()
        if not role:
            return False
        
        # Check if assignment already exists
        existing_result = await db.execute(
            select(user_role_association)
            .where(
                and_(
                    user_role_association.c.user_id == user_id,
                    user_role_association.c.role_id == role_id
                )
            )
        )
        if existing_result.first():
            return True  # Already assigned
        
        # Create assignment
        await db.execute(
            user_role_association.insert().values(
                user_id=user_id,
                role_id=role_id
            )
        )
        await db.commit()
        return True

    async def remove_role_from_user(self, db: AsyncSession, user_id: int, role_id: int) -> bool:
        """Remove a role from a user"""
        result = await db.execute(
            user_role_association.delete().where(
                and_(
                    user_role_association.c.user_id == user_id,
                    user_role_association.c.role_id == role_id
                )
            )
        )
        await db.commit()
        return result.rowcount > 0

    async def assign_roles_to_user(self, db: AsyncSession, user_id: int, role_ids: List[int], replace_existing: bool = False) -> bool:
        """Assign multiple roles to a user"""
        # Check if user exists
        user_result = await db.execute(
            select(User).where(User.id == user_id)
        )
        user = user_result.scalar_one_or_none()
        if not user:
            return False
        
        # If replacing existing roles, remove all current roles
        if replace_existing:
            await db.execute(
                user_role_association.delete().where(
                    user_role_association.c.user_id == user_id
                )
            )
        
        # Add new roles
        for role_id in role_ids:
            await self.assign_role_to_user(db, user_id, role_id)
        
        return True

    async def get_user_roles(self, db: AsyncSession, user_id: int) -> List[Role]:
        """Get all roles for a user"""
        result = await db.execute(
            select(Role)
            .join(user_role_association)
            .where(user_role_association.c.user_id == user_id)
            .where(Role.is_active == True)
            .order_by(Role.name)
        )
        return result.scalars().all()

    async def get_users_with_role(self, db: AsyncSession, role_id: int) -> List[User]:
        """Get all users with a specific role"""
        result = await db.execute(
            select(User)
            .join(user_role_association)
            .where(user_role_association.c.role_id == role_id)
            .where(User.is_active == True)
            .where(User.is_deleted == False)
            .order_by(User.full_name)
        )
        return result.scalars().all()

    async def get_role_stats(self, db: AsyncSession) -> dict:
        """Get role statistics"""
        # Total roles
        total_roles_result = await db.execute(
            select(func.count(Role.id))
        )
        total_roles = total_roles_result.scalar()
        
        # Active roles
        active_roles_result = await db.execute(
            select(func.count(Role.id)).where(Role.is_active == True)
        )
        active_roles = active_roles_result.scalar()
        
        # System roles
        system_roles_result = await db.execute(
            select(func.count(Role.id)).where(Role.is_system_role == True)
        )
        system_roles = system_roles_result.scalar()
        
        # Users with roles
        users_with_roles_result = await db.execute(
            select(func.count(func.distinct(user_role_association.c.user_id)))
        )
        users_with_roles = users_with_roles_result.scalar()
        
        # Total active users
        total_users_result = await db.execute(
            select(func.count(User.id))
            .where(User.is_active == True)
            .where(User.is_deleted == False)
        )
        total_users = total_users_result.scalar()
        
        return {
            "total_roles": total_roles,
            "active_roles": active_roles,
            "system_roles": system_roles,
            "custom_roles": total_roles - system_roles,
            "users_with_roles": users_with_roles,
            "users_without_roles": total_users - users_with_roles
        }

    async def create_default_roles(self, db: AsyncSession) -> List[Role]:
        """Create default system roles"""
        default_roles = [
            {"name": "Admin", "description": "Full system access and administration", "is_system_role": True},
            {"name": "Agent", "description": "Standard customer support agent", "is_system_role": True},
        ]
        
        created_roles = []
        for role_data in default_roles:
            # Check if role already exists
            existing_role = await self.get_role_by_name(db, role_data["name"])
            if not existing_role:
                role_create = RoleCreate(**role_data)
                created_role = await self.create_role(db, role_create)
                created_roles.append(created_role)
        
        return created_roles


# Create instance
crud_role = CRUDRole()
