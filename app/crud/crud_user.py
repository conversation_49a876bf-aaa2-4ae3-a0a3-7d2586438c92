from datetime import datetime, timezone, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, or_, Select
from sqlalchemy.orm import selectinload
from typing import List, Optional
from fastapi import BackgroundTasks
import logging

from app.crud import crud_asset
from app.core.s3 import s3_manager

logger = logging.getLogger(__name__)

from app.models.user import User
from app.models.role import Role
from app.schemas.user import UserCreate, UserUpdate
from app.core.security import get_password_hash

def get_all_users_query(search: Optional[str] = None, role_name: Optional[str] = None) -> Select:
    """
    Returns a SQLAlchemy SELECT statement for fetching users,
    which can be used by the pagination library.
    """
    stmt = select(User).options(selectinload(User.role), selectinload(User.profile_image)).where(User.is_deleted == False)
    
    if search:
        search_pattern = f"%{search}%".lower()
        stmt = stmt.where(or_(User.full_name.ilike(search_pattern), User.email.ilike(search_pattern)))
    if role_name:
        stmt = stmt.join(User.role).where(Role.name == role_name)
        
    return stmt.order_by(User.full_name)

async def create_user(db: AsyncSession, user_in: UserCreate) -> User:
    """
    Create a new user with single role assignment.
    If no role_id provided, user will default to Agent role.
    """
    hashed_password = get_password_hash(user_in.password)

    user_data = user_in.model_dump()
    user_data.pop('password', None)  # Remove password, not a User model field

    # Rename organization_id to company_id for the SQLAlchemy model
    if 'organization_id' in user_data:
        user_data['company_id'] = user_data.pop('organization_id')

    # If no role specified, find Agent role and assign it as default
    if not user_data.get('role_id'):
        company_id = user_data.get('company_id', 1)  # Default to company 1
        agent_role = await db.execute(
            select(Role).where(Role.name == "Agent", Role.company_id == company_id)
        )
        agent_role = agent_role.scalar_one_or_none()
        if agent_role:
            user_data['role_id'] = agent_role.id
        else:
            # Create Agent role if it doesn't exist
            new_agent_role = Role(
                name="Agent",
                description="Customer support agent (default role)",
                company_id=company_id,
                is_system_role=True
            )
            db.add(new_agent_role)
            await db.flush()  # Get the ID without committing
            user_data['role_id'] = new_agent_role.id

    db_user = User(**user_data, hashed_password=hashed_password)

    db.add(db_user)
    await db.commit()
    await db.refresh(db_user)

    return db_user

async def get_user(db: AsyncSession, id: int, include_deleted: bool = False) -> Optional[User]:
    """Get a single user by ID with role loaded, optionally including soft-deleted ones."""
    stmt = select(User).options(selectinload(User.role), selectinload(User.profile_image)).where(User.id == id)
    if not include_deleted:
        stmt = stmt.where(User.is_deleted == False)
    result = await db.execute(stmt)
    return result.scalar_one_or_none()

async def get_user_by_email(db: AsyncSession, email: str) -> Optional[User]:
    """Get a single user by email"""
    result = await db.execute(select(User).filter(User.email == email))
    return result.scalar_one_or_none()

async def update_user(
    db: AsyncSession, 
    user_id: int, 
    user_update: UserUpdate, 
    current_user: User, 
    background_tasks: BackgroundTasks
) -> Optional[User]:
    """Update a user, with special handling for the profile image."""
    user = await get_user(db, id=user_id)
    if not user:
        return None

    update_data = user_update.model_dump(exclude_unset=True)

    if "profile_image_id" in update_data:
        old_profile_image_id = user.profile_image_id
        new_profile_image_id = update_data["profile_image_id"]

        # If setting a new profile image
        if new_profile_image_id is not None:
            new_asset = await crud_asset.get_asset(db, asset_id=new_profile_image_id)
            if not new_asset:
                raise ValueError("New profile image asset not found.")
            # Simple ownership check for now. You could make this a Casbin check later.
            if new_asset.owner_id != current_user.id and not current_user.has_role("Admin"):
                 raise PermissionError("You do not have permission to use this asset.")

        # If there's an old image and it's being replaced or removed, delete it.
        if old_profile_image_id and old_profile_image_id != new_profile_image_id:
            old_asset = await crud_asset.get_asset(db, asset_id=old_profile_image_id)
            if old_asset:
                logger.info(f"Scheduling deletion of old profile image asset: {old_asset.s3_key}")
                # Use background tasks to delete from S3 and the DB
                background_tasks.add_task(s3_manager.delete_file, old_asset.s3_key)
                background_tasks.add_task(crud_asset.delete_asset, db, old_profile_image_id)

    # Apply all other updates
    for field, value in update_data.items():
        setattr(user, field, value)

    db.add(user)
    await db.commit()
    await db.refresh(user)
    return user

# --- NEW: Soft Delete and Restore ---
async def soft_delete_user(db: AsyncSession, user_id: int) -> Optional[User]:
    """Soft delete a user by setting is_deleted to True."""
    user = await get_user(db, id=user_id)
    if user:
        user.is_deleted = True
        user.deleted_at = datetime.now(timezone.utc)
        await db.commit()
        await db.refresh(user)
    return user

async def restore_user(db: AsyncSession, user_id: int) -> Optional[User]:
    """Restore a soft-deleted user."""
    user = await get_user(db, user_id=user_id, include_deleted=True)
    if user and user.is_deleted:
        user.is_deleted = False
        user.deleted_at = None
        await db.commit()
        await db.refresh(user)
    return user

# --- NEW: Scheduled Purge Function ---
async def purge_deleted_users(db: AsyncSession) -> int:
    """Permanently delete users soft-deleted more than 90 days ago."""
    purge_date = datetime.now(timezone.utc) - timedelta(days=90)
    
    stmt = select(User).where(User.is_deleted == True, User.deleted_at <= purge_date)
    users_to_purge = (await db.execute(stmt)).scalars().all()

    if not users_to_purge:
        return 0

    for user in users_to_purge:
        await db.delete(user)
    
    await db.commit()
    return len(users_to_purge)

async def get_user_with_roles(db: AsyncSession, user_id: int, include_deleted: bool = False) -> Optional[User]:
    """Get a single user by ID with roles and profile image loaded, optionally including soft-deleted ones."""
    stmt = select(User).options(selectinload(User.roles), selectinload(User.profile_image)).where(User.id == user_id)
    if not include_deleted:
        stmt = stmt.where(User.is_deleted == False)
    result = await db.execute(stmt)
    return result.scalar_one_or_none()

async def get_user_by_email_with_roles(db: AsyncSession, email: str) -> Optional[User]:
    """Get a single user by email with roles loaded"""
    result = await db.execute(
        select(User)
        .options(selectinload(User.roles))
        .filter(User.email == email)
        .filter(User.is_deleted == False)
    )
    return result.scalar_one_or_none()

async def get_users_with_roles(db: AsyncSession, skip: int = 0, limit: int = 100) -> List[User]:
    """Get all users with their roles loaded"""
    result = await db.execute(
        select(User)
        .options(selectinload(User.roles))
        .where(User.is_deleted == False)
        .offset(skip)
        .limit(limit)
        .order_by(User.full_name)
    )
    return result.scalars().all()

async def get_users_by_role_name(db: AsyncSession, role_name: str, skip: int = 0, limit: int = 100) -> List[User]:
    """Get all users with a specific role name"""
    result = await db.execute(
        select(User)
        .options(selectinload(User.roles))
        .join(User.roles)
        .where(Role.name == role_name)
        .where(User.is_deleted == False)
        .where(User.is_active == True)
        .offset(skip)
        .limit(limit)
        .order_by(User.full_name)
    )
    return result.scalars().all()

async def get_admins(db: AsyncSession, skip: int = 0, limit: int = 100) -> List[User]:
    """Get all admin users"""
    result = await db.execute(
        select(User)
        .options(selectinload(User.roles))
        .join(User.roles)
        .where(Role.name == "Admin")
        .where(User.is_deleted == False)
        .where(User.is_active == True)
        .offset(skip)
        .limit(limit)
        .order_by(User.full_name)
    )
    return result.scalars().all()

async def get_agents(db: AsyncSession, skip: int = 0, limit: int = 100) -> List[User]:
    """Get all agent users"""
    result = await db.execute(
        select(User)
        .options(selectinload(User.roles))
        .join(User.roles)
        .where(Role.name == "Agent")
        .where(User.is_deleted == False)
        .where(User.is_active == True)
        .offset(skip)
        .limit(limit)
        .order_by(User.full_name)
    )
    return result.scalars().all()



async def get_users_by_organization(db: AsyncSession, *, organization_id: int, search: Optional[str] = None, role_name: Optional[str] = None) -> List[User]:
    stmt = select(User).options(selectinload(User.roles)).filter(User.organization_id == organization_id).filter(User.is_deleted == False)
    if search:
        search_pattern = f"%{search}%".lower()
        stmt = stmt.filter(or_(User.full_name.ilike(search_pattern), User.email.ilike(search_pattern)))
    if role_name:
        stmt = stmt.join(User.roles).filter(Role.name == role_name)
    stmt = stmt.order_by(User.full_name)
    result = await db.execute(stmt)
    return result.scalars().all()