from datetime import datetime, timezone, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from sqlalchemy.orm import selectinload
from typing import List, Optional

from app.models.user import User
from app.models.role import Role
from app.schemas.user import UserCreate, UserUpdate
from app.core.security import get_password_hash

async def create_user(db: AsyncSession, user: UserCreate) -> User:
    """Create a new user and assign roles based on the input."""
    hashed_password = get_password_hash(user.password)

    # Create user object without the 'role_names' field, which is not in the DB model
    user_data = user.model_dump(exclude={"role_names"})
    db_user = User(**user_data, hashed_password=hashed_password)

    db.add(db_user)
    await db.commit()
    await db.refresh(db_user)

    # --- Role Assignment Logic ---
    from app.crud.crud_role import get_role_by_name, assign_role_to_user

    # 1. ALL new users get the "Agent" role by default.
    agent_role = await get_role_by_name(db, name="Agent")
    if agent_role:
        await assign_role_to_user(db, user_id=db_user.id, role_id=agent_role.id)

    # 2. Assign any additional roles specified during creation.
    if user.role_names:
        for role_name in set(user.role_names):  # Use set to avoid duplicates
            if role_name != "Agent":  # Avoid re-assigning the default role
                role = await get_role_by_name(db, name=role_name)
                if role:
                    await assign_role_to_user(db, user_id=db_user.id, role_id=role.id)

    # Eagerly load the roles before returning
    await db.refresh(db_user, attribute_names=['roles'])
    return db_user

async def get_user(db: AsyncSession, user_id: int, include_deleted: bool = False) -> Optional[User]:
    """Get a single user by ID, optionally including soft-deleted ones."""
    stmt = select(User).where(User.id == user_id)
    if not include_deleted:
        stmt = stmt.where(User.is_deleted == False)
    result = await db.execute(stmt)
    return result.scalar_one_or_none()

async def get_user_by_email(db: AsyncSession, email: str) -> Optional[User]:
    """Get a single user by email"""
    result = await db.execute(select(User).filter(User.email == email))
    return result.scalar_one_or_none()

# Removed: get_users_by_role - use get_users_by_role_name instead

async def get_all_users(db: AsyncSession, skip: int = 0, limit: int = 100) -> List[User]:
    """Get all users"""
    result = await db.execute(select(User).offset(skip).limit(limit))
    return result.scalars().all()

async def update_user(db: AsyncSession, user_id: int, user_update: UserUpdate) -> Optional[User]:
    """Update a user with the provided data."""
    user = await get_user(db, user_id=user_id)
    if not user:
        return None

    # Update only the fields that are provided
    update_data = user_update.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(user, field, value)

    await db.commit()
    await db.refresh(user)
    return user

# --- NEW: Soft Delete and Restore ---
async def soft_delete_user(db: AsyncSession, user_id: int) -> Optional[User]:
    """Soft delete a user by setting is_deleted to True."""
    user = await get_user(db, user_id=user_id)
    if user:
        user.is_deleted = True
        user.deleted_at = datetime.utcnow()
        await db.commit()
        await db.refresh(user)
    return user

async def restore_user(db: AsyncSession, user_id: int) -> Optional[User]:
    """Restore a soft-deleted user."""
    user = await get_user(db, user_id=user_id, include_deleted=True)
    if user and user.is_deleted:
        user.is_deleted = False
        user.deleted_at = None
        await db.commit()
        await db.refresh(user)
    return user

# --- NEW: Scheduled Purge Function ---
async def purge_deleted_users(db: AsyncSession) -> int:
    """Permanently delete users soft-deleted more than 90 days ago."""
    purge_date = datetime.now(timezone.utc) - timedelta(days=90)
    
    stmt = select(User).where(User.is_deleted == True, User.deleted_at <= purge_date)
    users_to_purge = (await db.execute(stmt)).scalars().all()

    if not users_to_purge:
        return 0

    for user in users_to_purge:
        await db.delete(user)
    
    await db.commit()
    return len(users_to_purge)

# --- NEW: Role-related functions ---
async def get_user_with_roles(db: AsyncSession, user_id: int, include_deleted: bool = False) -> Optional[User]:
    """Get a single user by ID with roles loaded, optionally including soft-deleted ones."""
    stmt = select(User).options(selectinload(User.roles)).where(User.id == user_id)
    if not include_deleted:
        stmt = stmt.where(User.is_deleted == False)
    result = await db.execute(stmt)
    return result.scalar_one_or_none()

async def get_user_by_email_with_roles(db: AsyncSession, email: str) -> Optional[User]:
    """Get a single user by email with roles loaded"""
    result = await db.execute(
        select(User)
        .options(selectinload(User.roles))
        .filter(User.email == email)
        .filter(User.is_deleted == False)
    )
    return result.scalar_one_or_none()

async def get_users_with_roles(db: AsyncSession, skip: int = 0, limit: int = 100) -> List[User]:
    """Get all users with their roles loaded"""
    result = await db.execute(
        select(User)
        .options(selectinload(User.roles))
        .where(User.is_deleted == False)
        .offset(skip)
        .limit(limit)
        .order_by(User.full_name)
    )
    return result.scalars().all()

async def get_users_by_role_name(db: AsyncSession, role_name: str, skip: int = 0, limit: int = 100) -> List[User]:
    """Get all users with a specific role name"""
    result = await db.execute(
        select(User)
        .options(selectinload(User.roles))
        .join(User.roles)
        .where(Role.name == role_name)
        .where(User.is_deleted == False)
        .where(User.is_active == True)
        .offset(skip)
        .limit(limit)
        .order_by(User.full_name)
    )
    return result.scalars().all()

async def get_admins(db: AsyncSession, skip: int = 0, limit: int = 100) -> List[User]:
    """Get all admin users"""
    result = await db.execute(
        select(User)
        .options(selectinload(User.roles))
        .join(User.roles)
        .where(Role.name == "Admin")
        .where(User.is_deleted == False)
        .where(User.is_active == True)
        .offset(skip)
        .limit(limit)
        .order_by(User.full_name)
    )
    return result.scalars().all()

async def get_agents(db: AsyncSession, skip: int = 0, limit: int = 100) -> List[User]:
    """Get all agent users"""
    result = await db.execute(
        select(User)
        .options(selectinload(User.roles))
        .join(User.roles)
        .where(Role.name == "Agent")
        .where(User.is_deleted == False)
        .where(User.is_active == True)
        .offset(skip)
        .limit(limit)
        .order_by(User.full_name)
    )
    return result.scalars().all()

# Removed: create_user_with_roles - functionality moved to main create_user function