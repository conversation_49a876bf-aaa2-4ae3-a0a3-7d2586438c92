from datetime import datetime, timezone
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, Select
from sqlalchemy.orm import selectinload
from typing import List, Optional
from uuid import UUID
from app.models.chat import Conversation, Message, ConversationStatus
from app.models.organization import Organization
from app.models.label import Label
from datetime import datetime
from app.schemas.chat import ConversationCreate, MessageCreate
from fastapi import HTT<PERSON>Exception

def get_conversations_query(
    organization_id: Optional[UUID] = None,
    organization_ids: Optional[List[UUID]] = None,
    status: Optional[ConversationStatus] = None,
    team_id: Optional[UUID] = None,
    customer_id: Optional[UUID] = None,
    unassigned: Optional[bool] = False,
    include_archived: bool = False,  # NEW: Parameter to include archived conversations
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    label_ids: Optional[List[UUID]] = None,
    search_query: Optional[str] = None,
) -> Select:
    """
    Returns a SQLAlchemy SELECT statement for fetching conversations with various filters,
    ordered by most recently updated.

    Args:
        organization_id: Filter by organization ID
        status: Filter by conversation status
        team_id: Filter by assigned team ID
        customer_id: Filter by customer ID
        unassigned: Filter for unassigned conversations
        include_archived: Whether to include archived conversations (default: False)
    """
    stmt = (
        select(Conversation)
        .options(
            selectinload(Conversation.assigned_team),
            selectinload(Conversation.customer),
            selectinload(Conversation.labels),
            selectinload(Conversation.organization),
        )
    )

    # Handle backward compatibility and multiple organizations
    if organization_id is not None and organization_ids is None:
        organization_ids = [organization_id]

    if organization_ids:
        stmt = stmt.where(Conversation.organization_id.in_(organization_ids))

    if status:
        stmt = stmt.where(Conversation.status == status)

    # By default, do not include archived conversations unless explicitly requested
    if not include_archived:
        stmt = stmt.where(Conversation.status != ConversationStatus.archived)

    if team_id:
        stmt = stmt.where(Conversation.assigned_team_id == team_id)
    if customer_id:
        stmt = stmt.where(Conversation.customer_id == customer_id)
    if unassigned:
        stmt = stmt.where(Conversation.assigned_team_id.is_(None)).where(Conversation.status == ConversationStatus.new)

    if start_date:
        stmt = stmt.where(Conversation.created_at >= start_date)
    if end_date:
        stmt = stmt.where(Conversation.created_at <= end_date)

    if label_ids:
        stmt = stmt.join(Conversation.labels).where(Label.id.in_(label_ids))

    if search_query:
        stmt = stmt.join(Message).where(Message.content.ilike(f"%{search_query}%"))

    return stmt.order_by(Conversation.updated_at.desc().nulls_last(), Conversation.created_at.desc())



async def touch_conversation(db: AsyncSession, conversation_id: UUID):
    """Explicitly update the updated_at timestamp for a conversation."""
    stmt = (
        update(Conversation)
        .where(Conversation.id == conversation_id)
        .values(updated_at=datetime.now(timezone.utc))
    )
    await db.execute(stmt)
    
async def create_conversation(db: AsyncSession, conversation: ConversationCreate) -> Conversation:
    """Create a new conversation and auto-assign to the organization's default team."""

    # Get the organization to find its default team
    org = await db.get(Organization, conversation.organization_id)
    if not org:
        raise HTTPException(status_code=404, detail="Organization not found")

    db_conversation = Conversation(
        **conversation.model_dump(),
        assigned_team_id=org.default_team_id,  # Auto-assign
        status=ConversationStatus.open if org.default_team_id else ConversationStatus.new
    )
    db.add(db_conversation)
    await db.commit()
    await db.refresh(db_conversation)
    return db_conversation

async def get_conversation(db: AsyncSession, id: UUID) -> Optional[Conversation]:
    """Get a conversation by ID, with related data"""
    result = await db.execute(
        select(Conversation)
        .options(
            selectinload(Conversation.assigned_team),
            selectinload(Conversation.customer),
            selectinload(Conversation.organization)
        )
        .filter(Conversation.id == id) # UPDATED
    )
    return result.scalar_one_or_none()

async def get_conversations(db: AsyncSession, skip: int = 0, limit: int = 100) -> List[Conversation]:
    """Get all conversations with pagination, ordered by most recently updated first"""
    result = await db.execute(
        select(Conversation)
        .order_by(Conversation.updated_at.desc().nulls_last(), Conversation.created_at.desc())
        .offset(skip)
        .limit(limit)
    )
    return result.scalars().all()

async def get_conversations_by_user(db: AsyncSession, user_id: UUID, skip: int = 0, limit: int = 100) -> List[Conversation]:
    """Get conversations for a specific user"""
    result = await db.execute(
        select(Conversation).filter(Conversation.user_id == user_id).offset(skip).limit(limit)
    )
    return result.scalars().all()

async def update_conversation_status(db: AsyncSession, conversation_id: UUID, status: str) -> Optional[Conversation]:
    """Update conversation status"""
    result = await db.execute(select(Conversation).filter(Conversation.id == conversation_id))
    db_conversation = result.scalar_one_or_none()
    if db_conversation:
        db_conversation.status = status
        await db.commit()
        await db.refresh(db_conversation)
    return db_conversation

async def create_message(db: AsyncSession, message: MessageCreate) -> Message:
    """Create a new message and 'touch' the parent conversation to update its timestamp."""
    db_message = Message(**message.model_dump())
    db.add(db_message)
    
    # Touch the parent conversation to update its 'updated_at' field
    await touch_conversation(db, message.conversation_id)
    
    # Commit both the new message and the conversation update
    await db.commit()
    await db.refresh(db_message)
    return db_message


async def get_message(db: AsyncSession, message_id: UUID) -> Optional[Message]:
    """Get a message by ID"""
    result = await db.execute(select(Message).filter(Message.id == message_id))
    return result.scalar_one_or_none()

async def get_messages_by_conversation(db: AsyncSession, conversation_id: UUID, limit: Optional[int] = None) -> List[Message]:
    """Get messages for a conversation with optional limit"""
    query = select(Message).filter(Message.conversation_id == conversation_id).order_by(Message.created_at.desc())

    if limit:
        query = query.limit(limit)

    result = await db.execute(query)
    messages = result.scalars().all()

    # Return in chronological order (oldest first)
    return list(reversed(messages))

async def get_recent_messages(db: AsyncSession, conversation_id: UUID, limit: int = 10) -> List[Message]:
    """Get recent messages for a conversation"""
    result = await db.execute(
        select(Message)
        .filter(Message.conversation_id == conversation_id)
        .order_by(Message.created_at.desc())
        .limit(limit)
    )
    return result.scalars().all()

async def assign_team_to_conversation(db: AsyncSession, conversation_id: UUID, team_id: UUID) -> Optional[Conversation]:
    """Assigns a team to a conversation and updates its status"""
    conversation = await get_conversation(db, id=conversation_id) 
    if conversation:
        conversation.assigned_team_id = team_id
        conversation.status = ConversationStatus.open  # Change status from 'new' to 'open'
        await db.commit()
        await db.refresh(conversation)
    return conversation

async def get_conversations_by_team(db: AsyncSession, team_id: UUID, skip: int = 0, limit: int = 100) -> List[Conversation]:
    """Get conversations assigned to a specific team, ordered by most recently updated first"""
    result = await db.execute(
        select(Conversation)
        .options(selectinload(Conversation.assigned_team))
        .filter(Conversation.assigned_team_id == team_id)
        .order_by(Conversation.updated_at.desc().nulls_last(), Conversation.created_at.desc())
        .offset(skip).limit(limit)
    )
    return result.scalars().all()

async def get_unassigned_conversations(db: AsyncSession, skip: int = 0, limit: int = 100) -> List[Conversation]:
    """Get conversations that are not assigned to any team, ordered by most recently updated first"""
    result = await db.execute(
        select(Conversation)
        .filter(Conversation.assigned_team_id.is_(None))
        .filter(Conversation.status == ConversationStatus.new)
        .order_by(Conversation.updated_at.desc().nulls_last(), Conversation.created_at.desc())
        .offset(skip).limit(limit)
    )
    return result.scalars().all()

async def get_conversations_by_organization(db: AsyncSession, organization_id: UUID, skip: int = 0, limit: int = 100) -> List[Conversation]:
    """Get conversations for a specific organization"""
    result = await db.execute(
        select(Conversation)
        .filter(Conversation.organization_id == organization_id)
        .offset(skip).limit(limit)
    )
    return result.scalars().all()

async def get_conversations_by_customer(db: AsyncSession, customer_id: UUID, skip: int = 0, limit: int = 100) -> List[Conversation]:
    """Get conversations for a specific customer"""
    result = await db.execute(
        select(Conversation)
        .filter(Conversation.customer_id == customer_id)
        .offset(skip).limit(limit)
    )
    return result.scalars().all()

# Message-specific CRUD operations

async def get_message(db: AsyncSession, message_id: UUID) -> Optional[Message]:
    """Get a message by ID, including its conversation for context."""
    result = await db.execute(
        select(Message)
        .options(selectinload(Message.conversation))
        .where(Message.id == message_id)
    )
    return result.scalar_one_or_none()

async def soft_delete_message(db: AsyncSession, message_id: UUID) -> Optional[Message]:
    """Soft deletes a message by setting its 'deleted' flag to True."""
    message = await get_message(db, message_id=message_id)
    if message:
        message.deleted = True
        await db.commit()
        await db.refresh(message)
    return message

async def restore_message(db: AsyncSession, message_id: UUID) -> Optional[Message]:
    """Restores a soft-deleted message by setting its 'deleted' flag to False."""
    message = await get_message(db, message_id=message_id)
    if message:
        message.deleted = False
        await db.commit()
        await db.refresh(message)
    return message

# --- NEW: Functions for Archiving ---

async def archive_conversation(db: AsyncSession, conversation_id: UUID) -> Optional[Conversation]:
    """
    Updates a conversation's status to 'archived'.
    """
    conversation = await get_conversation(db, id=conversation_id)
    if conversation:
        conversation.status = ConversationStatus.archived
        await db.commit()
        await db.refresh(conversation)
    return conversation

async def unarchive_conversation(db: AsyncSession, conversation_id: UUID) -> Optional[Conversation]:
    """
    Updates an archived conversation's status back to 'closed'.
    """
    conversation = await get_conversation(db, id=conversation_id)
    if conversation and conversation.status == ConversationStatus.archived:
        # We assume an un-archived conversation should go back to the 'closed' state.
        conversation.status = ConversationStatus.closed
        await db.commit()
        await db.refresh(conversation)
    return conversation

async def get_messages_with_deleted(db: AsyncSession, conversation_id: UUID, include_deleted: bool = False) -> List[Message]:
    """Get all messages for a conversation, optionally including deleted ones."""
    query = select(Message).filter(Message.conversation_id == conversation_id)

    if not include_deleted:
        query = query.filter(Message.deleted == False)

    query = query.order_by(Message.created_at)
    result = await db.execute(query)
    return result.scalars().all()

async def get_conversations_by_customer(db: AsyncSession, customer_id: UUID) -> List[Conversation]:
    """Get all conversations for a specific customer."""
    stmt = select(Conversation).where(Conversation.customer_id == customer_id).order_by(Conversation.created_at.desc())
    result = await db.execute(stmt)
    return result.scalars().all()

# --- NEW: Function for Bot Toggle ---

async def toggle_bot_activity(db: AsyncSession, conversation_id: UUID, active_state: bool) -> Optional[Conversation]:
    """
    Updates a conversation's is_bot_active flag.
    """
    conversation = await get_conversation(db, id=conversation_id)
    if conversation:
        conversation.is_bot_active = active_state
        await db.commit()
        await db.refresh(conversation)
    return conversation

async def mark_messages_as_read(db: AsyncSession, conversation_id: UUID, user_id: UUID) -> None:
    """
    Marks all unread messages in a conversation as read by a specific user.
    """
    stmt = (
        update(Message)
        .where(
            Message.conversation_id == conversation_id,
            Message.read_at.is_(None)
        )
        .values(
            read_at=datetime.now(timezone.utc),
            read_by_user_id=user_id
        )
    )
    await db.execute(stmt)
    await db.commit()
