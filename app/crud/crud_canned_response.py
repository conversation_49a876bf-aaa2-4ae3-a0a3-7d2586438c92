from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import selectinload
from sqlalchemy import func, and_, or_, desc, Select
from typing import List, Optional, Dict

from app.models.canned_response import CannedResponse
from app.models.user import User
from app.schemas.canned_response import CannedResponseCreate, CannedResponseUpdate, CannedResponseSearch

def get_canned_responses_query(
    organization_id: int,
    user_id: int,
    search: Optional[CannedResponseSearch] = None,
    active_only: bool = True
) -> Select:
    """
    Returns a SQLAlchemy SELECT statement for fetching canned responses with filtering.
    """
    query = select(CannedResponse).options(selectinload(CannedResponse.creator))
    
    # Base filters
    query = query.where(CannedResponse.organization_id == organization_id)
    
    if active_only:
        query = query.where(CannedResponse.is_active == True)
    
    # Show public responses + user's own private responses
    query = query.where(
        or_(
            CannedResponse.is_public == True,
            CannedResponse.created_by == user_id
        )
    )
    
    # Apply search filters
    if search:
        if search.query:
            search_term = f"%{search.query}%"
            query = query.where(
                or_(
                    CannedResponse.title.ilike(search_term),
                    CannedResponse.content.ilike(search_term)
                )
            )
        
        if search.category:
            query = query.where(CannedResponse.category == search.category)
        
        if search.created_by:
            query = query.where(CannedResponse.created_by == search.created_by)
        
        if search.is_public is not None:
            query = query.where(CannedResponse.is_public == search.is_public)
        
        if search.shortcut:
            query = query.where(CannedResponse.shortcut.ilike(f"%{search.shortcut}%"))
    
    # Order by usage count (most used first), then by title
    return query.order_by(desc(CannedResponse.usage_count), CannedResponse.title)


class CRUDCannedResponse:
    async def create_canned_response(
        self, 
        db: AsyncSession, 
        response: CannedResponseCreate, 
        created_by: int,
        organization_id: int
    ) -> CannedResponse:
        """Create a new canned response"""
        db_response = CannedResponse(
            title=response.title,
            content=response.content,
            shortcut=response.shortcut,
            category=response.category,
            is_public=response.is_public,
            organization_id=organization_id,
            created_by=created_by
        )
        db.add(db_response)
        await db.commit()
        await db.refresh(db_response)
        return db_response

    async def get_canned_response(self, db: AsyncSession, response_id: int) -> Optional[CannedResponse]:
        """Get canned response by ID"""
        result = await db.execute(
            select(CannedResponse)
            .options(selectinload(CannedResponse.creator))
            .where(CannedResponse.id == response_id)
        )
        return result.scalar_one_or_none()

    async def get_canned_responses(
        self, 
        db: AsyncSession, 
        organization_id: int,
        user_id: int,
        skip: int = 0, 
        limit: int = 100,
        search: Optional[CannedResponseSearch] = None,
        active_only: bool = True
    ) -> List[CannedResponse]:
        """Get canned responses for an organization with filtering"""
        query = select(CannedResponse).options(selectinload(CannedResponse.creator))
        
        # Base filters
        query = query.where(CannedResponse.organization_id == organization_id)
        
        if active_only:
            query = query.where(CannedResponse.is_active == True)
        
        # Show public responses + user's own private responses
        query = query.where(
            or_(
                CannedResponse.is_public == True,
                CannedResponse.created_by == user_id
            )
        )
        
        # Apply search filters
        if search:
            if search.query:
                search_term = f"%{search.query}%"
                query = query.where(
                    or_(
                        CannedResponse.title.ilike(search_term),
                        CannedResponse.content.ilike(search_term)
                    )
                )
            
            if search.category:
                query = query.where(CannedResponse.category == search.category)
            
            if search.created_by:
                query = query.where(CannedResponse.created_by == search.created_by)
            
            if search.is_public is not None:
                query = query.where(CannedResponse.is_public == search.is_public)
            
            if search.shortcut:
                query = query.where(CannedResponse.shortcut.ilike(f"%{search.shortcut}%"))
        
        # Order by usage count (most used first), then by title
        query = query.order_by(desc(CannedResponse.usage_count), CannedResponse.title)
        query = query.offset(skip).limit(limit)
        
        result = await db.execute(query)
        return result.scalars().all()

    async def get_canned_response_by_shortcut(
        self, 
        db: AsyncSession, 
        shortcut: str, 
        organization_id: int,
        user_id: int
    ) -> Optional[CannedResponse]:
        """Get canned response by shortcut"""
        result = await db.execute(
            select(CannedResponse)
            .where(CannedResponse.shortcut == shortcut)
            .where(CannedResponse.organization_id == organization_id)
            .where(CannedResponse.is_active == True)
            .where(
                or_(
                    CannedResponse.is_public == True,
                    CannedResponse.created_by == user_id
                )
            )
        )
        return result.scalar_one_or_none()

    async def update_canned_response(
        self, 
        db: AsyncSession, 
        response_id: int, 
        response_update: CannedResponseUpdate,
        user_id: int
    ) -> Optional[CannedResponse]:
        """Update a canned response (only creator or admin can update)"""
        result = await db.execute(
            select(CannedResponse).where(CannedResponse.id == response_id)
        )
        db_response = result.scalar_one_or_none()
        
        if not db_response:
            return None
        
        # Check if user can update (creator only for now)
        if db_response.created_by != user_id:
            return None
        
        update_data = response_update.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_response, field, value)
        
        await db.commit()
        await db.refresh(db_response)
        return db_response

    async def delete_canned_response(self, db: AsyncSession, response_id: int, user_id: int) -> bool:
        """Delete a canned response (only creator or admin can delete)"""
        result = await db.execute(
            select(CannedResponse).where(CannedResponse.id == response_id)
        )
        db_response = result.scalar_one_or_none()
        
        if not db_response:
            return False
        
        # Check if user can delete (creator only for now)
        if db_response.created_by != user_id:
            return False
        
        await db.delete(db_response)
        await db.commit()
        return True

    async def use_canned_response(self, db: AsyncSession, response_id: int) -> Optional[CannedResponse]:
        """Mark a canned response as used (increment usage count)"""
        result = await db.execute(
            select(CannedResponse).where(CannedResponse.id == response_id)
        )
        db_response = result.scalar_one_or_none()
        
        if not db_response:
            return None
        
        db_response.usage_count += 1
        db_response.last_used_at = func.now()
        
        await db.commit()
        await db.refresh(db_response)
        return db_response

    async def get_categories(self, db: AsyncSession, organization_id: int, user_id: int) -> List[str]:
        """Get all categories for an organization"""
        result = await db.execute(
            select(CannedResponse.category)
            .where(CannedResponse.organization_id == organization_id)
            .where(CannedResponse.category.isnot(None))
            .where(CannedResponse.is_active == True)
            .where(
                or_(
                    CannedResponse.is_public == True,
                    CannedResponse.created_by == user_id
                )
            )
            .distinct()
            .order_by(CannedResponse.category)
        )
        return [row[0] for row in result.fetchall()]

    async def get_responses_by_category(
        self, 
        db: AsyncSession, 
        category: str, 
        organization_id: int,
        user_id: int
    ) -> List[CannedResponse]:
        """Get all responses in a specific category"""
        result = await db.execute(
            select(CannedResponse)
            .options(selectinload(CannedResponse.creator))
            .where(CannedResponse.category == category)
            .where(CannedResponse.organization_id == organization_id)
            .where(CannedResponse.is_active == True)
            .where(
                or_(
                    CannedResponse.is_public == True,
                    CannedResponse.created_by == user_id
                )
            )
            .order_by(desc(CannedResponse.usage_count), CannedResponse.title)
        )
        return result.scalars().all()

    async def get_popular_responses(
        self, 
        db: AsyncSession, 
        organization_id: int,
        user_id: int,
        limit: int = 10
    ) -> List[CannedResponse]:
        """Get most popular (most used) canned responses"""
        result = await db.execute(
            select(CannedResponse)
            .options(selectinload(CannedResponse.creator))
            .where(CannedResponse.organization_id == organization_id)
            .where(CannedResponse.is_active == True)
            .where(
                or_(
                    CannedResponse.is_public == True,
                    CannedResponse.created_by == user_id
                )
            )
            .where(CannedResponse.usage_count > 0)
            .order_by(desc(CannedResponse.usage_count))
            .limit(limit)
        )
        return result.scalars().all()

    async def get_user_responses(
        self, 
        db: AsyncSession, 
        user_id: int, 
        organization_id: int,
        skip: int = 0,
        limit: int = 100
    ) -> List[CannedResponse]:
        """Get all responses created by a specific user"""
        result = await db.execute(
            select(CannedResponse)
            .options(selectinload(CannedResponse.creator))
            .where(CannedResponse.created_by == user_id)
            .where(CannedResponse.organization_id == organization_id)
            .where(CannedResponse.is_active == True)
            .order_by(desc(CannedResponse.created_at))
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()

    async def get_stats(self, db: AsyncSession, organization_id: int, user_id: int) -> Dict:
        """Get canned response statistics for an organization"""
        # Total responses
        total_result = await db.execute(
            select(func.count(CannedResponse.id))
            .where(CannedResponse.organization_id == organization_id)
            .where(
                or_(
                    CannedResponse.is_public == True,
                    CannedResponse.created_by == user_id
                )
            )
        )
        total_responses = total_result.scalar()
        
        # Public responses
        public_result = await db.execute(
            select(func.count(CannedResponse.id))
            .where(CannedResponse.organization_id == organization_id)
            .where(CannedResponse.is_public == True)
        )
        public_responses = public_result.scalar()
        
        # Active responses
        active_result = await db.execute(
            select(func.count(CannedResponse.id))
            .where(CannedResponse.organization_id == organization_id)
            .where(CannedResponse.is_active == True)
            .where(
                or_(
                    CannedResponse.is_public == True,
                    CannedResponse.created_by == user_id
                )
            )
        )
        active_responses = active_result.scalar()
        
        # Total usage
        usage_result = await db.execute(
            select(func.sum(CannedResponse.usage_count))
            .where(CannedResponse.organization_id == organization_id)
            .where(
                or_(
                    CannedResponse.is_public == True,
                    CannedResponse.created_by == user_id
                )
            )
        )
        total_usage = usage_result.scalar() or 0
        
        # Most used response
        most_used_result = await db.execute(
            select(CannedResponse)
            .where(CannedResponse.organization_id == organization_id)
            .where(CannedResponse.usage_count > 0)
            .where(
                or_(
                    CannedResponse.is_public == True,
                    CannedResponse.created_by == user_id
                )
            )
            .order_by(desc(CannedResponse.usage_count))
            .limit(1)
        )
        most_used_response = most_used_result.scalar_one_or_none()
        
        # Categories
        categories = await self.get_categories(db, organization_id, user_id)
        
        return {
            "total_responses": total_responses,
            "public_responses": public_responses,
            "private_responses": total_responses - public_responses,
            "active_responses": active_responses,
            "inactive_responses": total_responses - active_responses,
            "total_usage": total_usage,
            "most_used_response": most_used_response,
            "categories": categories
        }


# Create instance
crud_canned_response = CRUDCannedResponse()
