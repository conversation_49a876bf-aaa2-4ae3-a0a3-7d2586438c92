from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from typing import List, Optional

from app.models.asset import Asset
from app.schemas.asset import AssetCreate, AssetUpdate

class CRUDAsset:
    async def create_asset(self, db: AsyncSession, asset: AssetCreate) -> Asset:
        """Create a new asset"""
        db_asset = Asset(
            filename=asset.filename,
            original_filename=asset.original_filename,
            file_type=asset.file_type,
            mime_type=asset.mime_type,
            file_size=asset.file_size,
            s3_bucket=asset.s3_bucket,
            s3_key=asset.s3_key,
            s3_url=asset.s3_url,
            width=asset.width,
            height=asset.height,
            duration=asset.duration,
            thumbnail_s3_key=asset.thumbnail_s3_key,
            thumbnail_s3_url=asset.thumbnail_s3_url,
            is_processed=asset.is_processed,
            file_metadata=asset.file_metadata
        )
        db.add(db_asset)
        await db.commit()
        await db.refresh(db_asset)
        return db_asset

    async def get_asset(self, db: AsyncSession, asset_id: int) -> Optional[Asset]:
        """Get asset by ID"""
        result = await db.execute(select(Asset).where(Asset.id == asset_id))
        return result.scalar_one_or_none()

    async def get_asset_by_s3_key(self, db: AsyncSession, s3_key: str) -> Optional[Asset]:
        """Get asset by S3 key"""
        result = await db.execute(select(Asset).where(Asset.s3_key == s3_key))
        return result.scalar_one_or_none()

    async def get_assets(self, db: AsyncSession, skip: int = 0, limit: int = 100) -> List[Asset]:
        """Get all assets with pagination"""
        result = await db.execute(
            select(Asset)
            .offset(skip)
            .limit(limit)
            .order_by(Asset.created_at.desc())
        )
        return result.scalars().all()

    async def get_assets_by_type(self, db: AsyncSession, file_type: str, skip: int = 0, limit: int = 100) -> List[Asset]:
        """Get assets by file type"""
        result = await db.execute(
            select(Asset)
            .where(Asset.file_type == file_type)
            .offset(skip)
            .limit(limit)
            .order_by(Asset.created_at.desc())
        )
        return result.scalars().all()

    async def update_asset(self, db: AsyncSession, asset_id: int, asset_update: AssetUpdate) -> Optional[Asset]:
        """Update asset"""
        result = await db.execute(select(Asset).where(Asset.id == asset_id))
        db_asset = result.scalar_one_or_none()

        if not db_asset:
            return None

        update_data = asset_update.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_asset, field, value)

        await db.commit()
        await db.refresh(db_asset)
        return db_asset

    async def delete_asset(self, db: AsyncSession, asset_id: int) -> bool:
        """Delete asset"""
        result = await db.execute(select(Asset).where(Asset.id == asset_id))
        db_asset = result.scalar_one_or_none()

        if not db_asset:
            return False

        await db.delete(db_asset)
        await db.commit()
        return True

# Create instance
crud_asset = CRUDAsset()
