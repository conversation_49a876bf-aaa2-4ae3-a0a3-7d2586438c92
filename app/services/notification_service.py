"""
Real-time notification service using Redis pub/sub
"""

import json
import uuid
import asyncio
from datetime import datetime
from typing import List, Optional, Dict, Any, Set
from broadcaster import Broadcast
from fastapi import WebSocket
import logging

from app.core.config import settings
from app.schemas.notification import (
    NotificationCreate, 
    NotificationResponse, 
    NotificationType,
    NotificationPriority
)

logger = logging.getLogger(__name__)

class NotificationService:
    """Service for handling real-time notifications"""
    
    def __init__(self):
        # Initialize Redis broadcaster
        self.broadcast = Broadcast(settings.REDIS_URL)
        
        # Store active WebSocket connections
        # Format: {user_id: {connection_id: websocket}}
        self.connections: Dict[int, Dict[str, WebSocket]] = {}
        
        # Store user organization/team mappings for filtering
        # Format: {user_id: {"organization_id": int, "team_id": int}}
        self.user_contexts: Dict[int, Dict[str, Any]] = {}
        
        # Channel names
        self.GLOBAL_CHANNEL = "notifications:global"
        self.ORG_CHANNEL_PREFIX = "notifications:org:"
        self.TEAM_CHANNEL_PREFIX = "notifications:team:"
        self.USER_CHANNEL_PREFIX = "notifications:user:"
        
    async def startup(self):
        """Initialize the notification service"""
        try:
            await self.broadcast.connect()
            logger.info("✅ Notification service started successfully")
        except Exception as e:
            logger.error(f"❌ Failed to start notification service: {e}")
            raise
    
    async def shutdown(self):
        """Cleanup the notification service"""
        try:
            await self.broadcast.disconnect()
            logger.info("🛑 Notification service stopped")
        except Exception as e:
            logger.error(f"❌ Error stopping notification service: {e}")
    
    async def add_connection(
        self, 
        user_id: int, 
        websocket: WebSocket,
        organization_id: int,
        team_id: Optional[int] = None
    ) -> str:
        """Add a WebSocket connection for a user"""
        connection_id = str(uuid.uuid4())
        
        # Store connection
        if user_id not in self.connections:
            self.connections[user_id] = {}
        self.connections[user_id][connection_id] = websocket
        
        # Store user context for filtering
        self.user_contexts[user_id] = {
            "organization_id": organization_id,
            "team_id": team_id
        }
        
        logger.info(f"👤 User {user_id} connected (connection: {connection_id})")
        return connection_id
    
    async def remove_connection(self, user_id: int, connection_id: str):
        """Remove a WebSocket connection"""
        if user_id in self.connections and connection_id in self.connections[user_id]:
            del self.connections[user_id][connection_id]
            
            # Remove user if no more connections
            if not self.connections[user_id]:
                del self.connections[user_id]
                if user_id in self.user_contexts:
                    del self.user_contexts[user_id]
            
            logger.info(f"👤 User {user_id} disconnected (connection: {connection_id})")
    
    async def publish_notification(self, notification: NotificationCreate):
        """Publish a notification to appropriate channels"""
        try:
            # Create notification response
            notification_data = NotificationResponse(
                id=str(uuid.uuid4()),
                timestamp=datetime.utcnow(),
                **notification.model_dump()
            )
            
            # Convert to JSON
            message = notification_data.model_dump_json()
            
            # Publish to different channels based on targeting
            channels = self._get_channels_for_notification(notification)
            
            for channel in channels:
                await self.broadcast.publish(channel, message)
                logger.info(f"📢 Published notification to channel: {channel}")
            
            # Also send directly to connected users
            await self._send_to_connected_users(notification_data)
            
        except Exception as e:
            logger.error(f"❌ Error publishing notification: {e}")
    
    def _get_channels_for_notification(self, notification: NotificationCreate) -> List[str]:
        """Get the appropriate channels for a notification"""
        channels = []
        
        # Global notifications
        if notification.type in [NotificationType.SYSTEM_ALERT]:
            channels.append(self.GLOBAL_CHANNEL)
        
        # Organization-specific notifications
        if notification.organization_id:
            channels.append(f"{self.ORG_CHANNEL_PREFIX}{notification.organization_id}")
        
        # Team-specific notifications
        if notification.team_id:
            channels.append(f"{self.TEAM_CHANNEL_PREFIX}{notification.team_id}")
        
        # User-specific notifications
        if notification.user_id:
            channels.append(f"{self.USER_CHANNEL_PREFIX}{notification.user_id}")
        
        return channels
    
    async def _send_to_connected_users(self, notification: NotificationResponse):
        """Send notification directly to connected WebSocket users"""
        message = notification.model_dump_json()
        
        # Get users who should receive this notification
        target_users = self._get_target_users(notification)
        
        for user_id in target_users:
            if user_id in self.connections:
                # Send to all connections for this user
                for connection_id, websocket in list(self.connections[user_id].items()):
                    try:
                        await websocket.send_text(message)
                        logger.debug(f"📱 Sent notification to user {user_id}")
                    except Exception as e:
                        logger.warning(f"⚠️ Failed to send to user {user_id}: {e}")
                        # Remove broken connection
                        await self.remove_connection(user_id, connection_id)
    
    def _get_target_users(self, notification: NotificationResponse) -> Set[int]:
        """Get the set of user IDs who should receive this notification"""
        target_users = set()
        
        # Specific user targeting
        if notification.user_id:
            target_users.add(notification.user_id)
            return target_users
        
        # Filter by organization and team
        for user_id, context in self.user_contexts.items():
            should_receive = False
            
            # Check organization match
            if notification.organization_id:
                if context.get("organization_id") == notification.organization_id:
                    should_receive = True
            
            # Check team match (more specific)
            if notification.team_id:
                if context.get("team_id") == notification.team_id:
                    should_receive = True
                elif notification.organization_id and context.get("organization_id") == notification.organization_id:
                    # Include if same org but no specific team
                    should_receive = True
            
            # Global notifications
            if notification.type in [NotificationType.SYSTEM_ALERT]:
                should_receive = True
            
            if should_receive:
                target_users.add(user_id)
        
        return target_users
    
    async def get_user_notifications(
        self, 
        user_id: int, 
        organization_id: int,
        limit: int = 50
    ) -> List[NotificationResponse]:
        """Get recent notifications for a user (placeholder for future database storage)"""
        # For now, return empty list
        # In the future, you could store notifications in database
        return []
    
    async def mark_notification_read(self, notification_id: str, user_id: int):
        """Mark a notification as read (placeholder for future database storage)"""
        # For now, just log
        logger.info(f"📖 Notification {notification_id} marked as read by user {user_id}")

# Global notification service instance
notification_service = NotificationService()
