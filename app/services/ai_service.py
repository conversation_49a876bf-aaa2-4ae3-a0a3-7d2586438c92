import datetime
import httpx
import logging
from typing import List, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings
from app.crud import crud_chat

logger = logging.getLogger(__name__)

class AIService:
    def __init__(self):
        # Create a persistent async HTTP client for performance
        self.client = httpx.AsyncClient(
            base_url=settings.LLM_SERVICE_URL,
            timeout=30.0 
        )

    async def get_suggestions(self, conversation_id: int, db: AsyncSession) -> List[str]:
        """
        Gets reply suggestions for a conversation from the LLM microservice
        by sending a structured payload.
        """
        try:
            conversation = await crud_chat.get_conversation(db, conversation_id)
            if not conversation:
                logger.warning(f"Conversation {conversation_id} not found for suggestions.")
                return ["Error: Conversation not found."]
            
            messages = await crud_chat.get_recent_messages(db, conversation_id, limit=10)
            customer = conversation.customer

            messages_payload = [
                {"sender": msg.sender, "content": msg.content} 
                for msg in messages if msg.content 
            ]
            
            last_agent_message = next((msg for msg in reversed(messages) if msg.sender == 'agent' and msg.user), None)
            agent_name = last_agent_message.user.full_name if last_agent_message and last_agent_message.user else "Agent"

            metadata_payload = {
                "conversation_id": conversation.id,
                "customer_name": customer.name or f"Customer #{customer.id}",
                "agent_name": agent_name,
                "start_time": conversation.created_at.isoformat(),
                "end_time": conversation.updated_at.isoformat() if conversation.updated_at else datetime.utcnow().isoformat()
            }
            
            config_payload = {
                "num_suggestions": 3,
                "tone": "professional_and_empathetic",
                "language": "en"
            }

            config_payload = {
                "summary_format": "bullet_points",
                "summary_length": "short"
            }

            final_payload = {
                "messages": messages_payload,
                "metadata": metadata_payload,
                "config": config_payload
            }

            #logger.info(f"Requesting suggestions for conversation {conversation_id} from LLM service.")
            #logger.info(f"LLM Payload: {final_payload}")
            
            response = await self.client.post(
                "/suggestions",
                json=final_payload
            )
            response.raise_for_status()
            
            return response.json().get("suggestions", [])

        except httpx.RequestError as e:
            logger.error(f"LLM service request failed for suggestions: {e}")
            return ["Error: Could not connect to the AI service."]
        except Exception as e:
            logger.error(f"Error getting AI suggestions: {e}")
            import traceback
            traceback.print_exc()
            return ["Error: An internal error occurred while getting suggestions."]
    async def get_summary(self, conversation_id: int, db: AsyncSession) -> str:
        """
        Gets a summary for a conversation from the LLM microservice.
        """
        try:
            conversation = await crud_chat.get_conversation(db, conversation_id)
            if not conversation:
                logger.warning(f"Conversation {conversation_id} not found for summary.")
                return "Error: Conversation not found."

            # now access the related customer and all messages
            # Note: The 'messages' relationship should be configured for lazy loading
            # or pre-loaded here if performance is an issue. SQLAlchemy's default is fine for now.
            messages = await crud_chat.get_messages_by_conversation(db, conversation_id)
            customer = conversation.customer

            messages_payload = [
                {"sender": msg.sender, "content": msg.content} 
                for msg in messages if msg.content # Ensure we don't send empty content
            ]
            
            last_agent_message = next((msg for msg in reversed(messages) if msg.sender == 'agent' and msg.user), None)
            agent_name = last_agent_message.user.full_name if last_agent_message and last_agent_message.user else "Agent"

            metadata_payload = {
                "conversation_id": conversation.id,
                "customer_name": customer.name or f"Customer #{customer.id}",
                "agent_name": agent_name,
                "start_time": conversation.created_at.isoformat(),
                "end_time": conversation.updated_at.isoformat() if conversation.updated_at else datetime.utcnow().isoformat()
            }
            
            config_payload = {
                "summary_format": "bullet_points",
                "summary_length": "short"
            }

            final_payload = {
                "messages": messages_payload,
                "metadata": metadata_payload,
                "config": config_payload
            }
            
            logger.info(f"Requesting summary for conversation {conversation_id} from LLM service.")
            
            response = await self.client.post(
                "/summarize",
                json=final_payload 
            )
            response.raise_for_status()
            
            return response.json().get("summary", "Could not generate a summary.")

        except httpx.RequestError as e:
            logger.error(f"LLM service request failed: {e}")
            return "Error: Could not connect to the AI service."
        except Exception as e:
            logger.error(f"Error getting AI summary: {e}")
            import traceback
            traceback.print_exc() 
            return "Error: An internal error occurred while getting the summary."

ai_service = AIService()