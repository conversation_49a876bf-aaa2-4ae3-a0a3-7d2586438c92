import logging
from datetime import datetime
from typing import List, Optional, Dict, Any
from pathlib import Path

from fastapi import BackgroundTasks
from fastapi_mail import FastMail, MessageSchema, ConnectionConfig, MessageType
from jinja2 import Environment, FileSystemLoader

from app.core.config import settings

logger = logging.getLogger(__name__)

class EmailService:
    """
    Centralized email service for sending various types of notifications.
    Supports HTML templates and can be easily extended for different notification types.
    """
    
    def __init__(self):
        """Initialize the email service with FastAPI-Mail configuration."""
        self.conf = ConnectionConfig(
            MAIL_USERNAME=settings.MAIL_USERNAME,
            MAIL_PASSWORD=settings.MAIL_PASSWORD,
            MAIL_FROM=settings.MAIL_FROM,
            MAIL_PORT=settings.MAIL_PORT,
            MAIL_SERVER=settings.MAIL_SERVER,
            MAIL_FROM_NAME=settings.MAIL_FROM_NAME,
            MAIL_STARTTLS=settings.MAIL_STARTTLS,
            MAIL_SSL_TLS=settings.MAIL_SSL_TLS,
            USE_CREDENTIALS=settings.USE_CREDENTIALS,
            VALIDATE_CERTS=settings.VALIDATE_CERTS,
            TEMPLATE_FOLDER=Path(__file__).parent.parent.parent / settings.MAIL_TEMPLATE_FOLDER,
        )
        
        self.fastmail = FastMail(self.conf)
        
        # Initialize Jinja2 environment for template rendering
        template_dir = Path(__file__).parent.parent.parent / settings.MAIL_TEMPLATE_FOLDER
        self.jinja_env = Environment(loader=FileSystemLoader(template_dir))
        
        logger.info(f"📧 EmailService initialized with template folder: {template_dir}")

    async def send_email(
        self,
        recipients: List[str],
        subject: str,
        template_name: str,
        template_data: Dict[str, Any],
        cc: Optional[List[str]] = None,
        bcc: Optional[List[str]] = None
    ) -> bool:
        """
        Send an email using a template.
        
        Args:
            recipients: List of recipient email addresses
            subject: Email subject
            template_name: Name of the HTML template file (e.g., 'new_conversation.html')
            template_data: Data to pass to the template
            cc: Optional CC recipients
            bcc: Optional BCC recipients
            
        Returns:
            bool: True if email was sent successfully, False otherwise
        """
        try:
            if not settings.ENABLE_EMAIL_NOTIFICATIONS:
                logger.info("📧 Email notifications are disabled, skipping email send")
                return True
                
            if not recipients:
                logger.warning("📧 No recipients provided, skipping email send")
                return False
                
            # Render the HTML template
            template = self.jinja_env.get_template(template_name)
            html_body = template.render(**template_data)
            
            # Create the message
            message = MessageSchema(
                subject=subject,
                recipients=recipients,
                body=html_body,
                subtype=MessageType.html,
                cc=cc or [],
                bcc=bcc or []
            )
            
            # Send the email
            await self.fastmail.send_message(message)
            
            logger.info(f"✅ Email sent successfully to {len(recipients)} recipients: {', '.join(recipients)}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to send email: {e}")
            return False

    async def send_new_conversation_notification(
        self,
        team_email: str,
        conversation_id: int,
        customer_name: str,
        customer_email: Optional[str] = None,
        team_name: Optional[str] = None,
        priority: str = "normal",
        dashboard_url: Optional[str] = None
    ) -> bool:
        """
        Send a new conversation notification email to a team.
        
        Args:
            team_email: Email address of the team to notify
            conversation_id: ID of the new conversation
            customer_name: Name of the customer
            customer_email: Optional customer email
            team_name: Optional team name
            priority: Priority level (normal, high, urgent)
            dashboard_url: Optional URL to the dashboard
            
        Returns:
            bool: True if email was sent successfully
        """
        try:
            # Prepare template data
            template_data = {
                "app_name": settings.APP_NAME,
                "conversation_id": conversation_id,
                "customer_name": customer_name,
                "customer_email": customer_email,
                "team_name": team_name,
                "priority": priority,
                "status": "Active",
                "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S UTC"),
                "dashboard_url": dashboard_url or f"http://localhost:5173/conversations/{conversation_id}"
            }
            
            # Determine subject based on priority
            priority_prefix = {
                "urgent": "🚨 URGENT",
                "high": "⚡ HIGH PRIORITY",
                "normal": "📋"
            }.get(priority.lower(), "📋")
            
            subject = f"{priority_prefix} New Customer Conversation #{conversation_id} - {customer_name}"
            
            return await self.send_email(
                recipients=[team_email],
                subject=subject,
                template_name="new_conversation.html",
                template_data=template_data
            )
            
        except Exception as e:
            logger.error(f"❌ Failed to send new conversation notification: {e}")
            return False

    async def send_message_notification(
        self,
        team_email: str,
        conversation_id: int,
        customer_name: str,
        message_content: str,
        dashboard_url: Optional[str] = None
    ) -> bool:
        """
        Send a new message notification email.
        This can be extended in the future for different message types.
        """
        try:
            template_data = {
                "app_name": settings.APP_NAME,
                "conversation_id": conversation_id,
                "customer_name": customer_name,
                "message_content": message_content[:200] + "..." if len(message_content) > 200 else message_content,
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S UTC"),
                "dashboard_url": dashboard_url or f"http://localhost:5173/conversations/{conversation_id}"
            }
            
            subject = f"💬 New Message from {customer_name} - Conversation #{conversation_id}"
            
            # For now, we'll use a simple template. In the future, create message_notification.html
            simple_html = f"""
            <h2>New Message Alert</h2>
            <p><strong>From:</strong> {customer_name}</p>
            <p><strong>Conversation:</strong> #{conversation_id}</p>
            <p><strong>Message:</strong> {template_data['message_content']}</p>
            <p><a href="{template_data['dashboard_url']}">View Conversation</a></p>
            """
            
            message = MessageSchema(
                subject=subject,
                recipients=[team_email],
                body=simple_html,
                subtype=MessageType.html
            )
            
            await self.fastmail.send_message(message)
            logger.info(f"✅ Message notification sent to {team_email}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to send message notification: {e}")
            return False

    def send_email_background(
        self,
        background_tasks: BackgroundTasks,
        recipients: List[str],
        subject: str,
        template_name: str,
        template_data: Dict[str, Any]
    ):
        """
        Add email sending to background tasks.
        Useful for non-blocking email sending in API endpoints.
        """
        background_tasks.add_task(
            self.send_email,
            recipients=recipients,
            subject=subject,
            template_name=template_name,
            template_data=template_data
        )
        logger.info(f"📧 Email task added to background queue for {len(recipients)} recipients")

# Global email service instance
email_service = EmailService()
