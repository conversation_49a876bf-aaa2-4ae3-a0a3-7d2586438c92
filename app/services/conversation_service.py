# yupcha-customerbot-ai/app/services/conversation_service.py

import logging
from fastapi import Depends, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.session import get_async_db
from app.crud import crud_chat
from app.schemas.chat import ConversationCreate
from app.models.chat import Conversation
from app.api.endpoints.notifications import send_new_conversation_notification
from app.services.email_service import email_service 

logger = logging.getLogger(__name__)

class ConversationService:
    def __init__(self, db: AsyncSession = Depends(get_async_db)):
        self.db = db

    async def create_conversation(
        self, 
        conversation_data: ConversationCreate,
        background_tasks: BackgroundTasks
    ) -> Conversation:
        """
        Creates a new conversation, auto-assigns it, and triggers all necessary notifications.
        """
        try:
            new_conversation = await crud_chat.create_conversation(db=self.db, conversation=conversation_data)
            
            # relationships needed for notifications
            await self.db.refresh(new_conversation, ['assigned_team', 'customer', 'organization'])

            # real-time WebSocket notification
            await send_new_conversation_notification(
                conversation_id=new_conversation.id,
                customer_id=new_conversation.customer.id,
                customer_name=new_conversation.customer.name or f"Customer #{new_conversation.customer.id}",
                organization_id=new_conversation.organization_id,
                team_id=new_conversation.assigned_team_id
            )

            # background email notification if a team was assigned
            if new_conversation.assigned_team_id:
                team_email = "<EMAIL>" 
                
                background_tasks.add_task(
                    email_service.send_new_conversation_notification,
                    team_email=team_email,
                    conversation_id=new_conversation.id,
                    customer_name=new_conversation.customer.name or f"Customer #{new_conversation.customer.id}"
                )

            logger.info(f"✅ Successfully created conversation {new_conversation.id}")
            return new_conversation

        except Exception as e:
            logger.error(f"❌ Error creating conversation: {e}")
            raise