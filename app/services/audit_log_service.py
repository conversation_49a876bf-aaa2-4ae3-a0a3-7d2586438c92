from fastapi import BackgroundTasks, Request
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Any, Optional

from app.crud.crud_activity_log import crud_activity_log
from app.schemas.activity_log import ActivityLogCreate
from app.models.user import User

class AuditLogService:
    async def _create_log_entry(self, db: AsyncSession, log_data: ActivityLogCreate):
        """Internal method to create the log entry in the database."""
        await crud_activity_log.create(db, obj_in=log_data)

    def _prepare_log_data(
        self,
        request: Request,
        # MODIFICATION: current_user is now optional
        current_user: Optional[User], 
        action: str,
        target_resource: Any = None,
        details: dict = None
    ) -> ActivityLogCreate:
        """Private helper to construct the log data object."""
        ip_address = request.client.host if request.client else "unknown"
        user_agent = request.headers.get("user-agent", "unknown")

        log_data = ActivityLogCreate(
            # MODIFICATION: Only set user details if a user is present
            user_id=current_user.id if current_user else None,
            user_email=current_user.email if current_user else "anonymous",
            action=action,
            ip_address=ip_address,
            user_agent=user_agent,
            details=details
        )
        
        if target_resource:
            log_data.target_resource_type = getattr(target_resource, '__tablename__', None)
            log_data.target_resource_id = getattr(target_resource, 'id', None)
        
        return log_data

    async def _create_log_entry_wrapper(self, db: AsyncSession, **kwargs):
        """
        A wrapper to handle the async session for background tasks.
        This method is called by the background task.
        """
        log_data = self._prepare_log_data(**kwargs)
        await self._create_log_entry(db, log_data)

    # --- NEW: Synchronous (within request) logging method ---
    async def log_activity_sync(self, **kwargs):
        """Logs an audit entry directly. Use when BackgroundTasks is not available."""
        db_session = kwargs.pop('db')
        log_data = self._prepare_log_data(**kwargs)
        await self._create_log_entry(db_session, log_data)

    # --- REFACTORED: Background logging method ---
    def log_activity_background(self, background_tasks: BackgroundTasks, **kwargs):
        """Logs an audit entry using a background task."""
        db_session = kwargs.pop('db')
        log_data = self._prepare_log_data(**kwargs)
        background_tasks.add_task(self._create_log_entry, db_session, log_data)

# Keep the original method name for backward compatibility if you still use it elsewhere
# For simplicity, I'm assuming you'll switch all calls to use the new decorator.
# If not, you can keep the original `log_activity` method and have it call `log_activity_background`.

audit_log_service = AuditLogService()
