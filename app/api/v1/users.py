from fastapi import APIRouter, Depends, HTTPException, Query, Request, status, BackgroundTasks
from fastapi_cache.decorator import cache
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional
from uuid import UUID
from app.core.permissions import get_enforcer
import casbin
from app.crud.crud_activity_log import get_activity_logs_query
from app.db.session import get_async_db
from app.crud import crud_user
from app.schemas.activity_log import ActivityLogResponse
from app.schemas.user import UserResponse, UserCreate, UserUpdate
from app.models.user import User
from app.auth.dependencies import require_permission, require_role
from app.services.audit_log_service import audit_log_service
from app.crud import crud_asset
from app.core.s3 import s3_manager
from fastapi_pagination import Page
from fastapi_pagination.ext.sqlalchemy import paginate as async_paginate

router = APIRouter()

@router.post("/", response_model=UserResponse, dependencies=[Depends(require_permission("create"))])
async def create_new_user(
    request: Request,
    user: UserCreate,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_async_db),
):
    current_user: User = request.state.user
    """Create a new user (for admins to create agents). Admin access required."""
    existing_user = await crud_user.get_user_by_email(db=db, email=user.email)
    if existing_user:
        raise HTTPException(status_code=400, detail="Email already registered")
    new_user = await crud_user.create_user(db=db, user_in=user)
    user_response = UserResponse.model_validate(new_user)
    if new_user.profile_image:
        user_response.profile_image_url = new_user.profile_image.s3_url
    
    audit_log_service.log_activity(
        background_tasks=background_tasks,
        db=db,
        request=request,
        current_user=current_user,
        action="user.create",
        target_resource=new_user,
        details={"new_user_email": new_user.email}
    )
    return user_response

@router.get("/", response_model=Page[UserResponse], dependencies=[Depends(require_permission("read"))])
async def list_all_users(
    db: AsyncSession = Depends(get_async_db),
    search: Optional[str] = None,
    role_name: Optional[str] = None
):
    """List all users with pagination."""
    query = crud_user.get_all_users_query(search=search, role_name=role_name)
    return await async_paginate(db, query)

@router.get(
    "/casbin-policy", 
    response_model=List[List[str]], 
    dependencies=[Depends(require_permission("admin"))] # Secure it
)
async def get_casbin_policy(
    request: Request,
    enforcer: casbin.Enforcer = Depends(get_enforcer)
):
    """
    Get all raw Casbin policies from the database. For debugging and admin visibility.
    """
    return enforcer.get_policy()

@router.get("/activity-log", response_model=Page[ActivityLogResponse], dependencies=[Depends(require_permission("admin"))])
async def get_activity_log(
    request: Request,
    db: AsyncSession = Depends(get_async_db),
    user_id: Optional[UUID] = Query(None, description="Filter by user ID.")
):
    """Get the system-wide activity log with pagination. Admin access required."""
    query = get_activity_logs_query(user_id=user_id)
    return await async_paginate(db, query)

@router.get("/{user_id}", response_model=UserResponse, dependencies=[Depends(require_permission("read"))])
async def get_user_by_id(
    request: Request,
    user_id: UUID,
    db: AsyncSession = Depends(get_async_db),
):
    """Get a specific user by ID. Agent or Admin access required."""
    user = await crud_user.get_user(db=db, id=user_id)
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    
    user_response = UserResponse.model_validate(user)
    if user.profile_image:
        user_response.profile_image_url = user.profile_image.s3_url

    return user_response

@router.put("/{user_id}", response_model=UserResponse, dependencies=[Depends(require_permission("update"))])
async def update_user(
    request: Request,
    user_id: UUID,
    user_update: UserUpdate,
    background_tasks: BackgroundTasks, # Add this
    db: AsyncSession = Depends(get_async_db),
):
    current_user: User = request.state.user
    
    updated_user = await crud_user.update_user(
        db=db, 
        user_id=user_id, 
        user_update=user_update, 
        current_user=current_user,
        background_tasks=background_tasks
    )

    if not updated_user:
        raise HTTPException(status_code=404, detail="User not found or update failed")

    # Construct the final response
    user_response = UserResponse.from_orm(updated_user)
    if updated_user.profile_image:
        user_response.profile_image_url = updated_user.profile_image.s3_url
    
    # Audit log
    audit_log_service.log_activity(
        background_tasks=background_tasks,
        db=db,
        request=request,
        current_user=current_user,
        action="user.update",
        target_resource=updated_user,
        details={"update_data": user_update.model_dump(exclude_unset=True)}
    )
    
    return user_response

@router.patch("/{user_id}", response_model=UserResponse, dependencies=[Depends(require_permission("update"))])
async def patch_user(
    request: Request,
    user_id: UUID,
    user_update: UserUpdate,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_async_db)
):
    current_user: User = request.state.user
    """Partially update a user. Admin access required."""
    user = await crud_user.get_user(db, id=user_id)
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    updated_user = await crud_user.update_user(db, user_id=user_id, user_update=user_update, current_user=current_user, background_tasks=background_tasks)
    # Reload with role for response
    user_with_role = await crud_user.get_user(db, id=user_id)
    user_response = UserResponse.model_validate(user_with_role)
    if user_with_role and user_with_role.profile_image:
        user_response.profile_image_url = user_with_role.profile_image.s3_url

    audit_log_service.log_activity(
        background_tasks=background_tasks,
        db=db,
        request=request,
        current_user=current_user,
        action="user.update",
        target_resource=updated_user,
        details={"update_data": user_update.model_dump(exclude_unset=True)}
    )
    return user_response

@router.delete("/{user_id}", status_code=status.HTTP_204_NO_CONTENT, dependencies=[Depends(require_permission("delete"))])
async def delete_user(
    request: Request,
    user_id: UUID,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_async_db),
):
    current_user: User = request.state.user
    """Soft delete a user. Admin access required."""
    user_to_delete = await crud_user.get_user(db, id=user_id)
    if not user_to_delete:
        raise HTTPException(status_code=404, detail="User not found")
    
    await crud_user.soft_delete_user(db, user_id=user_id)
    
    # --- LOG THE ACTION ---
    audit_log_service.log_activity(
        background_tasks=background_tasks,
        db=db,
        request=request,
        current_user=current_user,
        action="user.delete",
        target_resource=user_to_delete,
        details={"deleted_user_email": user_to_delete.email}
    )
    return

@router.patch("/{user_id}/restore", response_model=UserResponse, dependencies=[Depends(require_permission("restore"))])
async def restore_user_endpoint(
    request: Request,
    user_id: UUID,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_async_db),
):
    current_user: User = request.state.user
    """Restore a soft-deleted user. Admin access required."""
    restored_user = await crud_user.restore_user(db, user_id=user_id)
    if not restored_user:
        raise HTTPException(status_code=404, detail="User not found or not deleted")
    # Reload with role for response
    user_with_role = await crud_user.get_user(db, id=user_id)
    user_response = UserResponse.model_validate(user_with_role)
    if user_with_role and user_with_role.profile_image:
        user_response.profile_image_url = user_with_role.profile_image.s3_url

    audit_log_service.log_activity(
        background_tasks=background_tasks,
        db=db,
        request=request,
        current_user=current_user,
        action="user.restore",
        target_resource=restored_user,
        details={"restored_user_email": restored_user.email}
    )
    return user_response

@router.delete("/me/profile-image", status_code=status.HTTP_204_NO_CONTENT)
async def delete_my_profile_image(
    request: Request,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_async_db)
):
    current_user: User = request.state.user
    """Delete the current user's profile image."""
    if current_user.profile_image_id is None:
        raise HTTPException(status_code=404, detail="User does not have a profile image.")

    old_asset_id = current_user.profile_image_id
    old_asset = await crud_asset.get_asset(db, asset_id=old_asset_id) # type: ignore

    if old_asset:
        try:
            background_tasks.add_task(s3_manager.delete_file, old_asset.s3_key)
            background_tasks.add_task(crud_asset.delete_asset, db, old_asset_id) # type: ignore
        except Exception as e:
            # Log the error, but proceed with updating the user's profile_image_id to None
            # as the S3 file might be gone or inaccessible, and we want to maintain DB consistency.
            print(f"Error deleting S3 file for profile image: {e}") # Replace with proper logging

    current_user.profile_image_id = None # type: ignore
    db.add(current_user)
    await db.commit()
    await db.refresh(current_user)

    audit_log_service.log_activity(
        background_tasks=background_tasks,
        db=db,
        request=request,
        current_user=current_user,
        action="user.delete_profile_image",
        target_resource=current_user,
        details={"deleted_asset_id": old_asset_id}
    )
    return
