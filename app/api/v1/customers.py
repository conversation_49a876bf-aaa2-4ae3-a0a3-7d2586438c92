from fastapi import APIRouter, Depends, HTTPException, Request, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional
from  fastapi_cache.decorator import cache

from app.db.session import get_async_db
from app.crud import crud_customer
from app.schemas.customer import CustomerResponse, CustomerCreate, CustomerUpdate
from app.models.user import User
from app.auth.dependencies import get_current_user, require_permission
from fastapi_pagination import Page
from fastapi_pagination.ext.sqlalchemy import paginate as async_paginate

router = APIRouter()

@router.post("/", response_model=CustomerResponse)
async def create_customer(
    customer: CustomerCreate,
    db: AsyncSession = Depends(get_async_db)
):
    """Create a new customer (public endpoint)."""
    existing_customer = await crud_customer.get_customer_by_customer_id(db=db, customer_id=customer.customer_id)
    if existing_customer:
        return existing_customer
    return await crud_customer.create_customer(db=db, customer=customer)

@router.get("/", response_model=Page[CustomerResponse], dependencies=[Depends(require_permission("read"))])
async def list_customers(
    request: Request,
    db: AsyncSession = Depends(get_async_db),
    search: Optional[str] = Query(None, description="Search customers by name or email.")
):
    """List all customers for the user's organization with pagination."""
    current_user: User = request.state.user
    query = crud_customer.get_customers_query(organization_id=current_user.company_id, search=search)
    return await async_paginate(db, query)

@router.get("/{customer_id}", response_model=CustomerResponse, dependencies=[Depends(require_permission("read"))])
@cache(expire=300)
async def get_customer(
    request: Request,
    customer_id: int,
    db: AsyncSession = Depends(get_async_db),
):
    """Get a specific customer by ID. Agent or Admin access required."""
    customer = await crud_customer.get_customer(db=db, customer_id=customer_id)
    if not customer:
        raise HTTPException(status_code=404, detail="Customer not found")
    return customer

@router.get("/by-customer-id/{customer_id}", response_model=CustomerResponse, dependencies=[Depends(require_permission("read"))])
async def get_customer_by_customer_id(
    request: Request,
    customer_id: str,
    db: AsyncSession = Depends(get_async_db),
):
    """Get a customer by their external customer_id. Agent or Admin access required."""
    customer = await crud_customer.get_customer_by_customer_id(db=db, customer_id=customer_id)
    if not customer:
        raise HTTPException(status_code=404, detail="Customer not found")
    return customer

@router.put("/{customer_id}", response_model=CustomerResponse, dependencies=[Depends(require_permission("update"))])
async def update_customer(
    request: Request,
    customer_id: int,
    customer_update: CustomerUpdate,
    db: AsyncSession = Depends(get_async_db),

):
    """Update a customer. Agent or Admin access required."""
    customer = await crud_customer.update_customer(
        db=db, customer_id=customer_id, customer_update=customer_update
    )
    if not customer:
        raise HTTPException(status_code=404, detail="Customer not found")
    return customer

@router.delete("/{customer_id}", status_code=status.HTTP_204_NO_CONTENT, dependencies=[Depends(require_permission("delete"))])
async def delete_customer(
    request: Request,
    customer_id: int,
    db: AsyncSession = Depends(get_async_db),
):
    """MODIFIED: Soft-delete a customer. Agent or admin access required."""
    customer_to_delete = await crud_customer.get_customer(db, customer_id=customer_id)
    if not customer_to_delete:
        raise HTTPException(status_code=404, detail="Customer not found")
        
    await crud_customer.soft_delete_customer(db, customer_id=customer_id)
    return

@router.patch("/{customer_id}/restore", response_model=CustomerResponse, dependencies=[Depends(require_permission("restore"))])
async def restore_customer_endpoint(
    request: Request,
    customer_id: int,
    db: AsyncSession = Depends(get_async_db),
):
    """NEW: Restore a soft-deleted customer. Admin access required."""
    restored_customer = await crud_customer.restore_customer(db, customer_id=customer_id)
    if not restored_customer:
        raise HTTPException(status_code=404, detail="Customer not found or was not deleted.")
    return restored_customer