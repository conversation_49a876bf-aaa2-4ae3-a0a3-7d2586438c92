from fastapi import APIRouter, Depends, HTTPException, status, Response, Request, BackgroundTasks
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.session import get_async_db
from app.crud import crud_user
from app.core.security import verify_password, get_password_hash
from app.auth.dependencies import serializer, SESSION_COOKIE_NAME, get_current_user
from app.models.user import User
from app.schemas.user import UserResponse, RoleResponse
from app.schemas.auth import PasswordResetRequest, PasswordResetConfirm
from app.services.email_service import email_service

router = APIRouter()

@router.post("/login")
async def login(
    response: Response,
    db: AsyncSession = Depends(get_async_db),
    form_data: OAuth2PasswordRequestForm = Depends(),
):
    """
    Logs in a user and sets a session cookie.
    Expects form data with 'username' (email) and 'password'.
    """
    user = await crud_user.get_user_by_email(db, email=form_data.username)
    if not user or not verify_password(form_data.password, user.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Create session data (signed user_id)
    session_data = serializer.dumps(str(user.id))
    
    # Set the cookie in the response
    response.set_cookie(
        key=SESSION_COOKIE_NAME,
        value=session_data,
        httponly=True,       # Prevents client-side JS from accessing the cookie
        samesite="lax",      # CSRF protection
        secure=False,        # In production with HTTPS, set this to True
        max_age=86400,       # 1 day
    )
    return {"message": "Login successful"}

@router.post("/logout")
def logout(response: Response):
    """Logs out the user by deleting the session cookie."""
    response.delete_cookie(SESSION_COOKIE_NAME)
    return {"message": "Logout successful"}

@router.get("/me", response_model=UserResponse)
async def read_users_me(current_user: User = Depends(get_current_user)):
    """Returns the current authenticated user's information."""
    # Get all role names for the user
    role_names = list(current_user.get_role_names) if hasattr(current_user, 'get_role_names') else []
    is_admin = "Admin" in role_names
    user_data = {
        "id": current_user.id,
        "email": current_user.email,
        "full_name": current_user.full_name,
        "company_id": getattr(current_user, "company_id", None),
        "team_id": current_user.team_id,
        "is_active": current_user.is_active,
        "created_at": current_user.created_at,
        "updated_at": current_user.updated_at,
        "is_admin": is_admin,
        "roles": [RoleResponse.model_validate(role) for role in current_user.roles],
    }
    return UserResponse(**user_data)


@router.post("/forgot-password")
async def forgot_password(
    request_data: PasswordResetRequest,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_async_db)
):
    """
    Handles a user's request to reset their password.
    Generates a secure token and emails a reset link.
    """
    user = await crud_user.get_user_by_email(db, email=request_data.email)
    
    # IMPORTANT: For security, always return a success message, even if the user
    # doesn't exist. This prevents attackers from finding out which emails are registered.
    if user:
        # Generate a short-lived token (e.g., 15 minutes)
        # We use a different "salt" to ensure these tokens can't be used for login.
        token = serializer.dumps(user.email, salt='password-reset')
        
        # This should point to your frontend's reset password page
        reset_link = f"http://localhost:5173/reset-password?token={token}"
        
        # Use a background task to send the email
        background_tasks.add_task(
            email_service.send_password_reset_email,
            recipient_email=user.email,
            user_name=user.full_name,
            reset_link=reset_link
        )

    return {"message": "A password reset link has been sent to the email link to the account."}


@router.post("/reset-password")
async def reset_password(
    request_data: PasswordResetConfirm,
    db: AsyncSession = Depends(get_async_db)
):
    """
    Resets a user's password using a valid token.
    """
    try:
        # Verify the token is valid and not expired (using the same salt)
        email = serializer.loads(request_data.token, salt='password-reset', max_age=900) # 900 seconds = 15 minutes
    except Exception:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid or expired token")

    user = await crud_user.get_user_by_email(db, email=email)
    if not user:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid token")

    # Hash the new password and update the user
    hashed_password = get_password_hash(request_data.new_password)
    user.hashed_password = hashed_password
    await db.commit()

    return {"message": "Password has been reset successfully."}