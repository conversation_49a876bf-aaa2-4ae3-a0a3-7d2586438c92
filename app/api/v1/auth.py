from fastapi import APIRouter, Depends, HTTPException, status, Response, Request, BackgroundTasks
from fastapi.security import OAuth2Pass<PERSON>Re<PERSON>Form
from sqlalchemy.ext.asyncio import AsyncSession
from app.schemas.user import UserResponse
from app.db.session import get_async_db
from app.crud import crud_user
from app.core.security import verify_password, get_password_hash
from app.auth.dependencies import serializer, SESSION_COOKIE_NAME, get_current_user
from app.models.user import User
from app.schemas.user import UserResponse, RoleResponse
from app.schemas.auth import PasswordResetRequest, PasswordResetConfirm, UserSignUp
from app.schemas.user import UserCreate
from app.services.email_service import email_service
from app.core.config import settings
from app.api.utils.audit import audit

router = APIRouter()

# --- NEW SIGNUP ENDPOINT ---
@router.post("/signup", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
async def signup(
    response: Response,
    user_in: UserSignUp,
    db: AsyncSession = Depends(get_async_db),
):
    """
    Handles new user registration.
    Creates a user without an organization, logs them in, and prepares for onboarding.
    """
    existing_user = await crud_user.get_user_by_email(db, email=user_in.email)
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail="An account with this email already exists.",
        )

    # Create the user without an organization_id for now.
    # The UserCreate schema is fine, as organization_id is optional.
    new_user = await crud_user.create_user(
        db, 
        user_in=UserCreate(
            email=user_in.email, 
            password=user_in.password, 
            full_name=user_in.full_name
        )
    )

    # Log the user in immediately by setting the session cookie
    session_data = serializer.dumps(str(new_user.id))
    response.set_cookie(
        key=SESSION_COOKIE_NAME,
        value=session_data,
        httponly=True,
        samesite="lax",
        secure=settings.SESSION_SECURE_COOKIE,
        max_age=settings.SESSION_COOKIE_MAX_AGE,
    )
    
    # Return the new user object (without the password)
    return UserResponse.model_validate(new_user)

@router.post("/login")
@audit("auth.login")
async def login(
    response: Response,
    request: Request,
    background_tasks: BackgroundTasks, 
    db: AsyncSession = Depends(get_async_db),
    form_data: OAuth2PasswordRequestForm = Depends(),
):
    """
    Logs in a user and sets a session cookie.
    Expects form data with 'username' (email) and 'password'.
    """
    user = await crud_user.get_user_by_email(db, email=form_data.username)
    if not user or not verify_password(form_data.password, user.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Create session data (signed user_id)
    session_data = serializer.dumps(str(user.id))
    
    # Set the cookie in the response
    response.set_cookie(
        key=SESSION_COOKIE_NAME,
        value=session_data,
        httponly=True,       # Prevents client-side JS from accessing the cookie
        samesite="lax",      # CSRF protection
        secure=settings.SESSION_SECURE_COOKIE,       
        max_age=settings.SESSION_COOKIE_MAX_AGE,      
    )
    request.state.user = user # Store user in request state for audit_action
    return {"message": "Login successful"}

@router.post("/logout")
@audit("auth.logout")
async def logout(
    response: Response, 
    request: Request,
    background_tasks: BackgroundTasks, # <-- ADD THIS (even for sync functions)
    db: AsyncSession = Depends(get_async_db)
):
    """Logs out the user by deleting the session cookie."""
    response.delete_cookie(SESSION_COOKIE_NAME)
    return {"message": "Logout successful"}

@router.get("/me", response_model=UserResponse)
async def read_users_me(current_user: User = Depends(get_current_user)):
    """Returns the current authenticated user's information."""
    
    return UserResponse.model_validate(current_user)


@router.post("/forgot-password")
@audit("auth.forgot_password")
async def forgot_password(
    request: Request,
    request_data: PasswordResetRequest,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_async_db)
):
    """
    Handles a user's request to reset their password.
    Generates a secure token and emails a reset link.
    """
    user = await crud_user.get_user_by_email(db, email=request_data.email)
    
    if user:
        request.state.user = user # Store user in request state for audit_action
        token = serializer.dumps(user.email, salt='password-reset')
        
        reset_link = f"{str(settings.FRONTEND_URL).rstrip('/')}/reset-password?token={token}"
        
        # Use a background task to send the email
        background_tasks.add_task(
            email_service.send_password_reset_email,
            recipient_email=user.email,
            user_name=user.full_name,
            reset_link=reset_link
        )

    return {"message": "A password reset link has been sent to the email link to the account."}


@router.post("/reset-password")
@audit("auth.reset_password")
async def reset_password(
    request: Request,
    request_data: PasswordResetConfirm,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_async_db),
):
    """
    Resets a user's password using a valid token.
    """
    try:
        # Verify the token is valid and not expired (using the same salt)
        email = serializer.loads(request_data.token, salt='password-reset', max_age=900) # 900 seconds = 15 minutes
    except Exception:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid or expired token")

    user = await crud_user.get_user_by_email(db, email=email)
    if not user:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid token")

    # Hash the new password and update the user
    hashed_password = get_password_hash(request_data.new_password)
    user.hashed_password = hashed_password
    await db.commit()
    request.state.user = user # Store user in request state for audit_action

    return {"message": "Password has been reset successfully."}