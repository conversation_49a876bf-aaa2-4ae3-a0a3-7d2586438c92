from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, Request, Query
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional
from fastapi_cache.decorator import cache

from app.db.session import get_async_db
from app.crud import crud_chat, crud_team, crud_label
from app.schemas.ai import SuggestionResponse, SummaryResponse
from app.schemas.chat import ConversationCreate, ConversationResponse, MessageResponse, MessageCreate, MessageCreateRequest, ConversationStatus
from app.schemas.label import ConversationLabelRequest
from app.models.user import User
from app.services.ai_service import ai_service
from app.services.conversation_service import ConversationService
from app.auth.dependencies import require_permission
from app.services.audit_log_service import audit_log_service

from fastapi_pagination import Page
from fastapi_pagination.ext.sqlalchemy import paginate as async_paginate

import logging

logger = logging.getLogger(__name__)

router = APIRouter()

#* A placeholder function for sending an email
def send_team_notification_email(team_email: str, conversation_id: int):
    """Send notification email to team about new conversation"""
    print(f"📧 Sending email to {team_email} about new conversation {conversation_id}")

@router.post("/", response_model=ConversationResponse, dependencies=[Depends(require_permission("create"))])
async def create_conversation(
    request: Request,
    conversation: ConversationCreate,
    service: ConversationService = Depends(ConversationService),
    background_tasks: BackgroundTasks = BackgroundTasks(),
):
    current_user: User = request.state.user
    """
    Create a new conversation.
    The service layer handles auto-assignment and notifications.
    """
    return await service.create_conversation(conversation, background_tasks)


# --- NEW PUBLIC ENDPOINT ---
@router.post("/public/initiate", response_model=ConversationResponse)
async def initiate_public_conversation(
    # We don't use Depends(get_current_user) here, so it's a public endpoint.
    conversation_data: ConversationCreate,
    service: ConversationService = Depends(ConversationService),
    background_tasks: BackgroundTasks = BackgroundTasks()
):
    """
    Public endpoint for an unauthenticated customer (e.g., from a website widget)
    to create a new conversation.
    """
    # Here you might want to add some extra security, like a captcha check
    # or a check against a trusted origin header, but for now, this is fine.
    
    # We call the same robust service layer function.
    return await service.create_conversation(conversation_data, background_tasks)

@router.get("/{conversation_id}", response_model=ConversationResponse, dependencies=[Depends(require_permission("read"))])
@cache(expire=300)
async def get_conversation(
    request: Request,
    conversation_id: int,
    db: AsyncSession = Depends(get_async_db),
):
    """Get a specific conversation by ID"""
    logger.info(f"Attempting to get conversation: {conversation_id}")
    conversation = await crud_chat.get_conversation(db=db, id=conversation_id)
    if conversation is None:
        logger.warning(f"Conversation {conversation_id} not found.")
        raise HTTPException(status_code=404, detail="Conversation not found")
    logger.info(f"Successfully retrieved conversation: {conversation_id}")
    return conversation

@router.get("/{conversation_id}/messages", response_model=List[MessageResponse], dependencies=[Depends(require_permission("read"))])
@cache(expire=300)
async def get_conversation_messages(
    request: Request,
    conversation_id: int,
    db: AsyncSession = Depends(get_async_db),
):
    """Get all messages for a specific conversation"""
    logger.info(f"Attempting to get messages for conversation: {conversation_id}")
    messages = await crud_chat.get_messages_by_conversation(db=db, conversation_id=conversation_id)
    logger.info(f"Retrieved {len(messages)} messages for conversation: {conversation_id}")
    return messages

@router.get("/", response_model=Page[ConversationResponse], dependencies=[Depends(require_permission("read"))])
async def list_conversations(
    request: Request,
    db: AsyncSession = Depends(get_async_db),
    status: Optional[ConversationStatus] = Query(None, description="Filter by conversation status."),
    team_id: Optional[int] = Query(None, description="Filter by assigned team ID."),
    customer_id: Optional[int] = Query(None, description="Filter by customer ID."),
    unassigned: Optional[bool] = Query(False, description="Filter for unassigned conversations.")
):
    """List conversations with pagination and filtering."""
    current_user: User = request.state.user
    query = crud_chat.get_conversations_query(
        organization_id=current_user.company_id,
        status=status,
        team_id=team_id,
        customer_id=customer_id,
        unassigned=unassigned
    )
    return await async_paginate(db, query)

@router.post("/{conversation_id}/messages", response_model=MessageResponse, dependencies=[Depends(require_permission("create"))])
async def create_message(
    request: Request,
    conversation_id: int,
    message: MessageCreateRequest,
    db: AsyncSession = Depends(get_async_db),
):
    """Create a new message in a conversation. Agent or Admin access required."""
    conversation = await crud_chat.get_conversation(db, id=conversation_id)
    if not conversation:
        raise HTTPException(status_code=404, detail="Conversation not found")

    # Create the message with conversation_id
    message_data = MessageCreate(
        conversation_id=conversation_id,
        content=message.content,
        sender=message.sender,
        message_type=message.message_type
    )
    created_message = await crud_chat.create_message(db=db, message=message_data)

    return created_message

@router.post("/{conversation_id}/assign/{team_id}", response_model=ConversationResponse, dependencies=[Depends(require_permission("assign"))])
async def assign_team(
    request: Request,
    background_tasks: BackgroundTasks,
    conversation_id: int,
    team_id: int,
    db: AsyncSession = Depends(get_async_db),
):
    current_user: User = request.state.user
    """Assign a team to a conversation. Admin access required."""
    team = await crud_team.get_team(db=db, id=team_id)
    if not team:
        raise HTTPException(status_code=404, detail="Team not found")

    conversation = await crud_chat.assign_team_to_conversation(
        db, conversation_id=conversation_id, team_id=team_id
    )
    if not conversation:
        raise HTTPException(status_code=404, detail="Conversation not found")

    audit_log_service.log_activity(
        background_tasks=background_tasks,
        db=db,
        request=request,
        current_user=current_user,
        action="conversation.assign_team",
        target_resource=conversation,
        details={"assigned_to_team_id": team_id}
    )
    return conversation

@router.get("/unassigned", response_model=List[ConversationResponse], dependencies=[Depends(require_permission("read"))])
async def get_unassigned_conversations(
    request: Request,
    db: AsyncSession = Depends(get_async_db),
):
    """Get unassigned conversations. Agent or Admin access required."""
    return await crud_chat.get_unassigned_conversations(db=db)

@router.get("/team/{team_id}", response_model=List[ConversationResponse], dependencies=[Depends(require_permission("read"))])
async def get_team_conversations(
    request: Request,
    team_id: int,
    db: AsyncSession = Depends(get_async_db),
):
    """Get conversations for a specific team. Admins can see any, agents only their team."""
    return await crud_chat.get_conversations_by_team(db=db, team_id=team_id)

@router.get("/customer/{customer_id}", response_model=List[ConversationResponse], dependencies=[Depends(require_permission("read"))])
async def get_conversations_by_customer(
    request: Request,
    customer_id: int,
    db: AsyncSession = Depends(get_async_db),
):
    """
    Get all conversations for a specific customer.
    """
    # TODO: Add permission check to ensure agent's organization matches customer's organization
    return await crud_chat.get_conversations_by_customer(db=db, customer_id=customer_id)

@router.post("/{conversation_id}/labels", response_model=ConversationResponse, dependencies=[Depends(require_permission("update"))])
async def attach_labels_to_conversation(
    request_obj: Request,
    background_tasks: BackgroundTasks,
    conversation_id: int,
    request: ConversationLabelRequest,
    db: AsyncSession = Depends(get_async_db),
):
    """Attach one or more labels to a conversation."""
    conversation = await crud_chat.get_conversation(db, id=conversation_id)
    if not conversation:
        raise HTTPException(status_code=404, detail="Conversation not found")

    updated_conversation = await crud_label.attach_labels_to_conversation(
        db, conversation=conversation, label_ids=request.label_ids
    )

    if not updated_conversation:
        raise HTTPException(status_code=400, detail="One or more labels are invalid for this organization.")

    audit_log_service.log_activity(
        background_tasks=background_tasks,
        db=db,
        request=request_obj,
        current_user=request_obj.state.user,
        action="conversation.attach_labels",
        target_resource=updated_conversation,
        details={"label_ids": request.label_ids}
    )
    return updated_conversation

@router.post("/{conversation_id}/suggest-replies", response_model=SuggestionResponse, dependencies=[Depends(require_permission("read"))])
async def suggest_replies(
    request: Request,
    conversation_id: int,
    db: AsyncSession = Depends(get_async_db),
):
    """
    Get AI-generated reply suggestions for a conversation.
    """
    suggestions = await ai_service.get_suggestions(conversation_id, db)
    return SuggestionResponse(suggestions=suggestions)


@router.get("/{conversation_id}/summary", response_model=SummaryResponse, dependencies=[Depends(require_permission("read"))])
async def get_conversation_summary(
    request: Request,
    conversation_id: int,
    db: AsyncSession = Depends(get_async_db),
):
    """
    Get an AI-generated summary of the entire conversation.
    """
    summary = await ai_service.get_summary(conversation_id, db)
    return SummaryResponse(summary=summary)