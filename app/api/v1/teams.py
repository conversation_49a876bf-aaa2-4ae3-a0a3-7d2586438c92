from fastapi import APIRouter, BackgroundTasks, Depends, HTTPException, Request, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional
from uuid import UUID
from app.services.audit_log_service import audit_log_service
from app.db.session import get_async_db
from app.crud import crud_team
from app.schemas.team import TeamResponse, TeamCreate, TeamUpdate
from app.models.user import User
from app.auth.dependencies import require_permission
from fastapi_pagination import Page
from fastapi_pagination.ext.sqlalchemy import paginate as async_paginate

router = APIRouter()

@router.post("/", response_model=TeamResponse, dependencies=[Depends(require_permission("create"))])
async def create_team(
    request: Request,
    background_tasks: BackgroundTasks,
    team: TeamCreate,
    db: AsyncSession = Depends(get_async_db),
):
    current_user: User = request.state.user
    """Create a new team. Admin access required."""
    new_team = await crud_team.create_team(db=db, team=team)
    audit_log_service.log_activity(
        background_tasks=background_tasks,
        db=db,
        request=request,
        current_user=current_user,
        action="team.create",
        target_resource=new_team,
        details={"team_name": new_team.name}
    )
    return new_team

@router.get("/", response_model=Page[TeamResponse], dependencies=[Depends(require_permission("read"))])
async def list_teams(
    request: Request,
    db: AsyncSession = Depends(get_async_db),
    search: Optional[str] = Query(None, description="Search teams by name.")
):
    """List all teams for the user's organization with pagination."""
    current_user: User = request.state.user
    query = crud_team.get_teams_query(organization_id=current_user.company_id, search=search)
    return await async_paginate(db, query)

@router.get("/organization/{organization_id}", response_model=List[TeamResponse], dependencies=[Depends(require_permission("read"))])
async def list_teams_by_organization(
    request: Request,
    organization_id: UUID,
    skip: int = 0,
    limit: int = 100,
    db: AsyncSession = Depends(get_async_db),
):
    """List teams by organization. Agent or Admin access required."""
    return await crud_team.get_teams_by_organization(db=db, organization_id=organization_id, skip=skip, limit=limit, load_relations=True)

@router.get("/{team_id}", response_model=TeamResponse, dependencies=[Depends(require_permission("read"))])
async def get_team(
    request: Request,
    team_id: UUID,
    db: AsyncSession = Depends(get_async_db),
):
    """Get a specific team by ID. Agent or Admin access required."""
    team = await crud_team.get_team(db=db, id=team_id, load_relations=True)
    if not team:
        raise HTTPException(status_code=404, detail="Team not found")
    return team

@router.put("/{team_id}", response_model=TeamResponse, dependencies=[Depends(require_permission("update"))])
async def update_team(
    request: Request,
    background_tasks: BackgroundTasks,
    team_id: UUID,
    team_update: TeamUpdate,
    db: AsyncSession = Depends(get_async_db),
):
    """Update a team. Admin access required."""
    updated_team = await crud_team.update_team(db=db, team_id=team_id, team_update=team_update)
    if not updated_team:
        raise HTTPException(status_code=404, detail="Team not found")

    # Re-fetch the team with relations loaded for proper serialization
    team_with_relations = await crud_team.get_team(db=db, id=updated_team.id, load_relations=True)
    if not team_with_relations:
        # This case should ideally not happen if update_team succeeded
        raise HTTPException(status_code=500, detail="Failed to retrieve updated team with relations.")

    audit_log_service.log_activity(
        background_tasks=background_tasks,
        db=db,
        request=request,
        current_user=request.state.user,
        action="team.update",
        target_resource=team_with_relations,
        details={"update_data": team_update.model_dump(exclude_unset=True)}
    )
    return team_with_relations

@router.delete("/{team_id}", status_code=status.HTTP_204_NO_CONTENT, dependencies=[Depends(require_permission("delete"))])
async def delete_team(
    request: Request,
    background_tasks: BackgroundTasks,
    team_id: UUID,
    db: AsyncSession = Depends(get_async_db),
):
    """MODIFIED: Soft-delete a team. Admin access required."""
    team_to_delete = await crud_team.get_team(db, id=team_id)
    if not team_to_delete:
        raise HTTPException(status_code=404, detail="Team not found")
    
    await crud_team.soft_delete_team(db=db, team_id=team_id)

    audit_log_service.log_activity(
        background_tasks=background_tasks,
        db=db,
        request=request,
        current_user=request.state.user,
        action="team.delete",
        target_resource=team_to_delete,
        details={"deleted_team_name": team_to_delete.name}
    )
    return

@router.patch("/{team_id}/restore", response_model=TeamResponse, dependencies=[Depends(require_permission("restore"))])
async def restore_team_endpoint(
    request: Request,
    background_tasks: BackgroundTasks,
    team_id: UUID,
    db: AsyncSession = Depends(get_async_db),
):
    """NEW: Restore a soft-deleted team. Admin access required."""
    restored_team = await crud_team.restore_team(db, id=team_id)
    if not restored_team:
        raise HTTPException(status_code=404, detail="Team not found or was not deleted.")

    audit_log_service.log_activity(
        background_tasks=background_tasks,
        db=db,
        request=request,
        current_user=request.state.user,
        action="team.restore",
        target_resource=restored_team,
        details={"restored_team_name": restored_team.name}
    )
    return restored_team
