from fastapi import APIRouter, BackgroundTasks, Depends, HTTPException, Request, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional

from app.db.session import get_async_db
from app.crud.crud_role import crud_role
from app.schemas.role import RoleResponse, RoleCreate, RoleUpdate
from app.models.user import User
from app.auth.dependencies import get_current_user, require_permission
from app.services.audit_log_service import audit_log_service

router = APIRouter()

@router.post("/", response_model=RoleResponse, status_code=status.HTTP_201_CREATED, dependencies=[Depends(require_permission("create"))])
async def create_role(
    request: Request,
    background_tasks: BackgroundTasks,
    role_in: RoleCreate,
    db: AsyncSession = Depends(get_async_db),
):
    current_user: User = request.state.user
    """Create a new role. Admin access required."""
    if role_in.company_id and role_in.company_id != current_user.company_id:
        raise HTTPException(status_code=403, detail="Cannot create role for another organization.")
    role_in.company_id = current_user.company_id
    existing_role = await crud_role.get_role_by_name(db, role_in.name, company_id=current_user.company_id)
    if existing_role:
        raise HTTPException(status_code=400, detail="Role with this name already exists in your organization.")
    new_role = await crud_role.create_role(db=db, role=role_in)
    audit_log_service.log_activity(
        background_tasks=background_tasks,
        db=db,
        request=request,
        current_user=current_user,
        action="role.create",
        target_resource=new_role,
        details={"role_name": new_role.name}
    )
    return new_role

@router.get("/", response_model=List[RoleResponse], dependencies=[Depends(require_permission("read"))])
async def list_roles(
    request: Request,
    skip: int = 0,
    limit: int = 100,
    active_only: bool = Query(True),
    include_system: bool = Query(True),
    db: AsyncSession = Depends(get_async_db),
):
    current_user: User = request.state.user
    """List all roles with filtering options. Admin access required."""
    return await crud_role.get_roles(db=db, skip=skip, limit=limit, company_id=current_user.company_id, active_only=active_only, include_system=include_system)

@router.get("/{role_id}", response_model=RoleResponse, dependencies=[Depends(require_permission("read"))])
async def get_role(
    request: Request,
    role_id: int,
    db: AsyncSession = Depends(get_async_db),
):
    current_user: User = request.state.user
    """Get a specific role by ID. Admin access required."""
    role = await crud_role.get_role(db=db, role_id=role_id)
    if not role or role.company_id != current_user.company_id:
        raise HTTPException(status_code=404, detail="Role not found in your organization.")
    return role

@router.put("/{role_id}", response_model=RoleResponse, dependencies=[Depends(require_permission("update"))])
async def update_role(
    request: Request,
    role_id: int,
    role_in: RoleUpdate,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_async_db),
):
    current_admin: User = request.state.user
    """Update a role. Admin access required."""
    role = await crud_role.get_role(db=db, role_id=role_id)
    if not role or role.company_id != current_admin.company_id:
        raise HTTPException(status_code=404, detail="Role not found in your organization.")
    
    # Check for name conflict if name is being updated
    if role_in.name and role_in.name != role.name:
        existing_role = await crud_role.get_role_by_name(db, role_in.name, company_id=current_admin.company_id)
        if existing_role:
            raise HTTPException(status_code=400, detail="Role with this name already exists in your organization.")

    return await crud_role.update_role(db=db, role_id=role_id, role_update=role_in)

    audit_log_service.log_activity(
        background_tasks=background_tasks,
        db=db,
        request=request,
        current_user=current_admin,
        action="role.update",
        target_resource=role,
        details={"update_data": role_in.model_dump(exclude_unset=True)}
    )

@router.delete("/{role_id}", status_code=status.HTTP_204_NO_CONTENT, dependencies=[Depends(require_permission("delete"))])
async def delete_role(
    request: Request,
    background_tasks: BackgroundTasks,
    role_id: int,
    db: AsyncSession = Depends(get_async_db),
):
    current_admin: User = request.state.user
    """Delete a role. Admin access required. System roles cannot be deleted."""
    role = await crud_role.get_role(db=db, role_id=role_id)
    if not role or role.company_id != current_admin.company_id:
        raise HTTPException(status_code=404, detail="Role not found in your organization.")
    
    if role.is_system_role:
        raise HTTPException(status_code=403, detail="Cannot delete system roles")

    # Check if any users are assigned to this role
    users_with_role = await crud_role.get_users_with_role(db, role_id)
    if users_with_role:
        raise HTTPException(status_code=400, detail="Cannot delete role: users are still assigned to it")

    success = await crud_role.delete_role(db=db, role_id=role_id)
    if success:
        audit_log_service.log_activity(
            background_tasks=background_tasks,
            db=db,
            request=request,
            current_user=current_admin,
            action="role.delete",
            target_resource=role,
            details={"deleted_role_name": role.name}
        )
    if not success:
        raise HTTPException(status_code=500, detail="Failed to delete role")
    return