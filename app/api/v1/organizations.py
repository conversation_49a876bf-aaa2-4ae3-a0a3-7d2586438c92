from fastapi import APIRouter, BackgroundTasks, Depends, HTTPException, Request, status
from fastapi_cache.decorator import cache
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List
from uuid import UUID
from app.services.audit_log_service import audit_log_service
from app.db.session import get_async_db
from app.crud import crud_organization, crud_user, crud_role # Import new CRUDs
from app.schemas.organization import OrganizationResponse, OrganizationCreate, OrganizationUpdate, OrganizationOnboard # Add new schema
from app.models.user import User
from app.auth.dependencies import require_permission, get_current_user # remove require_permission for this one
from app.api.utils.audit import audit
from app.core.permissions import enforcer # Import enforcer directly

router = APIRouter()

@router.post("/onboard", response_model=OrganizationResponse)
async def onboard_new_organization(
    org_data: OrganizationOnboard,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user)
):
    """
    Creates the first organization for a newly signed-up user.
    This endpoint can only be used ONCE by a user without an organization.
    """
    # 1. Security Check: Ensure user doesn't already belong to an organization.
    if current_user.organization_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User is already part of an organization."
        )

    # 2. Create the Organization
    new_organization = await crud_organization.create_organization(
        db, OrganizationCreate(name=org_data.name)
    )

    # 3. Create the default system roles for this new organization
    admin_role, agent_role = await crud_role.create_default_roles(db, company_id=new_organization.id)
    
    # You could also create Manager, HR here if they are considered "system" roles
    # for every new organization.

    # 4. Assign the current user as the Admin of this new organization
    current_user.organization_id = new_organization.id
    current_user.role_id = admin_role.id
    db.add(current_user)
    await db.commit()
    await db.refresh(current_user)

    # 5. Add Casbin policy for the new Admin
    # Policy: p, Admin, company:<new_org_id>:*, *
    enforcer.add_policy("Admin", f"company:{new_organization.id}:*", "*")
    await enforcer.save_policy()

    # 6. Return the newly created organization
    return new_organization

@router.post("/", response_model=OrganizationResponse, dependencies=[Depends(require_permission("create"))])
@audit("organization.create")
async def create_organization(
    request: Request,
    background_tasks: BackgroundTasks,
    organization: OrganizationCreate,
    db: AsyncSession = Depends(get_async_db),
):
    current_user: User = request.state.user
    """Create a new organization. Admin access required."""
    new_organization = await crud_organization.create_organization(db=db, organization=organization)
    return new_organization

@router.get("/", response_model=List[OrganizationResponse], dependencies=[Depends(require_permission("read"))])
async def list_organizations(
    request: Request,
    skip: int = 0,
    limit: int = 100,
    db: AsyncSession = Depends(get_async_db),
):
    """List all organizations. Admin access required."""
    return await crud_organization.get_organizations(db=db, skip=skip, limit=limit, load_relations=True)

@router.get("/{organization_id}", response_model=OrganizationResponse, dependencies=[Depends(require_permission("read"))])
async def get_organization(
    request: Request,
    organization_id: UUID,
    db: AsyncSession = Depends(get_async_db),
):
    """Get a specific organization by ID. Admin access required."""
    organization = await crud_organization.get_organization(db=db, organization_id=organization_id, load_relations=True)
    if not organization:
        raise HTTPException(status_code=404, detail="Organization not found")
    return organization

@router.put("/{organization_id}", response_model=OrganizationResponse, dependencies=[Depends(require_permission("update"))])
@audit("organization.update")
async def update_organization(
    request: Request,
    organization_id: UUID,
    background_tasks: BackgroundTasks,
    organization_update: OrganizationUpdate,
    db: AsyncSession = Depends(get_async_db),
):
    """Update an organization. Admin access required."""
    updated_organization = await crud_organization.update_organization(
        db=db, organization_id=organization_id, organization_update=organization_update
    )
    if not updated_organization:
        raise HTTPException(status_code=404, detail="Organization not found")

    # Re-fetch the organization with relations loaded for proper serialization
    organization_with_relations = await crud_organization.get_organization(db=db, organization_id=updated_organization.id, load_relations=True)
    if not organization_with_relations:
        raise HTTPException(status_code=500, detail="Failed to retrieve updated organization with relations.")

    return organization_with_relations

@router.delete("/{organization_id}", dependencies=[Depends(require_permission("delete"))])
@audit("organization.delete")
async def delete_organization(
    request: Request,
    background_tasks: BackgroundTasks,
    organization_id: UUID,
    db: AsyncSession = Depends(get_async_db),
):
    """MODIFIED: Soft-delete an organization. Admin access required."""
    org_to_delete = await crud_organization.get_organization(db, organization_id=organization_id)
    if not org_to_delete:
        raise HTTPException(status_code=404, detail="Organization not found")
    
    await crud_organization.soft_delete_organization(db=db, organization_id=organization_id)
    request.state.resource = org_to_delete # Store the deleted organization for audit_action

    return

@router.patch("/{organization_id}/restore", response_model=OrganizationResponse, dependencies=[Depends(require_permission("restore"))])
@audit("organization.restore")
async def restore_organization_endpoint(
    request: Request,
    background_tasks: BackgroundTasks,
    organization_id: UUID,
    db: AsyncSession = Depends(get_async_db),
):
    """NEW: Restore a soft-deleted organization. Admin access required."""
    restored_org = await crud_organization.restore_organization(db, organization_id=organization_id)
    if not restored_org:
        raise HTTPException(status_code=404, detail="Organization not found or was not deleted.")

    return restored_org
