from fastapi import APIRouter, BackgroundTasks, Depends, HTTPException, Request, status
from fastapi_cache.decorator import cache
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List
from app.services.audit_log_service import audit_log_service
from app.db.session import get_async_db
from app.crud import crud_organization
from app.schemas.organization import OrganizationResponse, OrganizationCreate, OrganizationUpdate
from app.models.user import User
from app.auth.dependencies import require_permission

router = APIRouter()

@router.post("/", response_model=OrganizationResponse, dependencies=[Depends(require_permission("create"))])
async def create_organization(
    request: Request,
    background_tasks: BackgroundTasks,
    organization: OrganizationCreate,
    db: AsyncSession = Depends(get_async_db),
):
    current_user: User = request.state.user
    """Create a new organization. Admin access required."""
    new_organization = await crud_organization.create_organization(db=db, organization=organization)
    audit_log_service.log_activity(
        background_tasks=background_tasks,
        db=db,
        request=request,
        current_user=current_user,
        action="organization.create",
        target_resource=new_organization,
        details={"organization_name": new_organization.name}
    )
    return new_organization

@router.get("/", response_model=List[OrganizationResponse], dependencies=[Depends(require_permission("read"))])
async def list_organizations(
    request: Request,
    skip: int = 0,
    limit: int = 100,
    db: AsyncSession = Depends(get_async_db),
):
    """List all organizations. Admin access required."""
    return await crud_organization.get_organizations(db=db, skip=skip, limit=limit, load_relations=True)

@router.get("/{organization_id}", response_model=OrganizationResponse, dependencies=[Depends(require_permission("read"))])
async def get_organization(
    request: Request,
    organization_id: int,
    db: AsyncSession = Depends(get_async_db),
):
    """Get a specific organization by ID. Admin access required."""
    organization = await crud_organization.get_organization(db=db, organization_id=organization_id, load_relations=True)
    if not organization:
        raise HTTPException(status_code=404, detail="Organization not found")
    return organization

@router.put("/{organization_id}", response_model=OrganizationResponse, dependencies=[Depends(require_permission("update"))])
async def update_organization(
    request: Request,
    organization_id: int,
    background_tasks: BackgroundTasks,
    organization_update: OrganizationUpdate,
    db: AsyncSession = Depends(get_async_db),
):
    """Update an organization. Admin access required."""
    updated_organization = await crud_organization.update_organization(
        db=db, organization_id=organization_id, organization_update=organization_update
    )
    if not updated_organization:
        raise HTTPException(status_code=404, detail="Organization not found")

    # Re-fetch the organization with relations loaded for proper serialization
    organization_with_relations = await crud_organization.get_organization(db=db, organization_id=updated_organization.id, load_relations=True)
    if not organization_with_relations:
        raise HTTPException(status_code=500, detail="Failed to retrieve updated organization with relations.")

    audit_log_service.log_activity(
        background_tasks=background_tasks,
        db=db,
        request=request,
        current_user=request.state.user,
        action="organization.update",
        target_resource=organization_with_relations,
        details={"update_data": organization_update.model_dump(exclude_unset=True)}
    )
    return organization_with_relations

@router.delete("/{organization_id}", dependencies=[Depends(require_permission("delete"))])
async def delete_organization(
    request: Request,
    background_tasks: BackgroundTasks,
    organization_id: int,
    db: AsyncSession = Depends(get_async_db),
):
    """MODIFIED: Soft-delete an organization. Admin access required."""
    org_to_delete = await crud_organization.get_organization(db, organization_id=organization_id)
    if not org_to_delete:
        raise HTTPException(status_code=404, detail="Organization not found")
    
    await crud_organization.soft_delete_organization(db=db, organization_id=organization_id)

    audit_log_service.log_activity(
        background_tasks=background_tasks,
        db=db,
        request=request,
        current_user=request.state.user,
        action="organization.delete",
        target_resource=org_to_delete,
        details={"deleted_organization_name": org_to_delete.name}
    )
    return

@router.patch("/{organization_id}/restore", response_model=OrganizationResponse, dependencies=[Depends(require_permission("restore"))])
async def restore_organization_endpoint(
    request: Request,
    background_tasks: BackgroundTasks,
    organization_id: int,
    db: AsyncSession = Depends(get_async_db),
):
    """NEW: Restore a soft-deleted organization. Admin access required."""
    restored_org = await crud_organization.restore_organization(db, organization_id=organization_id)
    if not restored_org:
        raise HTTPException(status_code=404, detail="Organization not found or was not deleted.")

    audit_log_service.log_activity(
        background_tasks=background_tasks,
        db=db,
        request=request,
        current_user=request.state.user,
        action="organization.restore",
        target_resource=restored_org,
        details={"restored_organization_name": restored_org.name}
    )
    return restored_org
