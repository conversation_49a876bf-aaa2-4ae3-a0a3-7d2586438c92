from fastapi import APIRouter
from .conversations import router as conversations_router
from .messages import router as messages_router
from .labels import router as labels_router
from .notes import router as notes_router
from .conversation_notes import router as conversation_notes_router

chat_router = APIRouter(prefix="/chat")
chat_router.include_router(conversations_router, prefix="/conversations", tags=["conversations"])
chat_router.include_router(messages_router, prefix="/messages", tags=["messages"])
chat_router.include_router(labels_router, prefix="/labels", tags=["labels"])
chat_router.include_router(notes_router, prefix="/notes", tags=["notes"])
chat_router.include_router(conversation_notes_router, prefix="/conversation-notes", tags=["conversation-notes"])