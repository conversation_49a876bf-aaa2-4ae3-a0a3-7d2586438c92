from fastapi import APIRouter, Depends, HTTPException, Request, status
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List
from uuid import UUID

from app.db.session import get_async_db
from app.crud import crud_note
from app.schemas.note import Note, NoteCreate, NoteUpdate
from app.models.note import Note as NoteModel
from app.models.customer import Customer
from app.models.user import User
from app.auth.dependencies import require_permission
from fastapi_pagination import Page
from fastapi_pagination.ext.sqlalchemy import paginate as async_paginate

router = APIRouter()

@router.post("/", response_model=Note, status_code=status.HTTP_201_CREATED, dependencies=[Depends(require_permission("create", "notes"))])
async def create_note(
    request: Request,
    note_in: NoteCreate,
    db: AsyncSession = Depends(get_async_db),
):
    return await crud_note.create_note(db=db, note_in=note_in)

@router.get("/customer/{customer_id}", response_model=Page[Note], dependencies=[Depends(require_permission("read", "customers"))])
async def get_notes_by_customer(
    request: Request,
    db: AsyncSession = Depends(get_async_db),
):
    """Get all notes for a specific customer."""
    # The customer was already fetched and validated by the dependency
    customer: Customer = request.state.resource
    query = crud_note.get_notes_query(customer_id=customer.id)
    return await async_paginate(db, query)

@router.put("/{note_id}", response_model=Note, dependencies=[Depends(require_permission("update", "notes"))])
async def update_note(
    request: Request,
    note_in: NoteUpdate,
    db: AsyncSession = Depends(get_async_db),
):
    """Update a specific note."""
    # The note was already fetched, validated for tenancy, and attached to the request by the dependency.
    note: NoteModel = request.state.resource
    current_user: User = request.state.user

    # Additional ownership check
    if not current_user.is_admin_or_above() and note.user_id != current_user.id:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="You do not have permission to edit this note.")

    return await crud_note.update_note(db=db, note=note, note_in=note_in)

@router.delete("/{note_id}", status_code=status.HTTP_204_NO_CONTENT, dependencies=[Depends(require_permission("delete", "notes"))])
async def delete_note(
    request: Request,
    db: AsyncSession = Depends(get_async_db),
):
    """Delete a specific note."""
    note: NoteModel = request.state.resource
    current_user: User = request.state.user

    # Additional ownership check
    if not current_user.is_admin_or_above() and note.user_id != current_user.id:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="You do not have permission to delete this note.")

    await crud_note.delete_note(db=db, note=note)
    return None
