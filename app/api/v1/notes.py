from fastapi import APIRouter, Depends, HTTPException, Request
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List
from uuid import UUID

from app.db.session import get_async_db
from app.crud import crud_note
from app.schemas.note import Note, NoteCreate, NoteUpdate
from app.models.user import User
from app.auth.dependencies import get_current_user, require_permission
from fastapi_pagination import Page
from fastapi_pagination.ext.sqlalchemy import paginate as async_paginate

router = APIRouter()

@router.post("/", response_model=Note, dependencies=[Depends(require_permission("create"))])
async def create_note(
    request: Request,
    note_in: NoteCreate,
    db: AsyncSession = Depends(get_async_db),
):
    current_user: User = request.state.user
    # TODO: Add permission check to ensure user can create notes for this customer
    return await crud_note.create_note(db=db, note_in=note_in)

@router.get("/customer/{customer_id}", response_model=Page[Note], dependencies=[Depends(require_permission("read"))])
async def get_notes_by_customer(
    request: Request,
    customer_id: UUID,
    db: AsyncSession = Depends(get_async_db),
):
    """List notes for a specific customer with pagination."""
    current_user: User = request.state.user
    # TODO: Add permission check to ensure user can view notes for this customer
    query = crud_note.get_notes_query(customer_id=customer_id)
    return await async_paginate(db, query)

@router.put("/{note_id}", response_model=Note, dependencies=[Depends(require_permission("update"))])
async def update_note(
    request: Request,
    note_id: UUID,
    note_in: NoteUpdate,
    db: AsyncSession = Depends(get_async_db),
):
    current_user: User = request.state.user
    note = await crud_note.get_note(db=db, note_id=note_id)
    if not note:
        raise HTTPException(status_code=404, detail="Note not found")
    # TODO: Add permission check to ensure user can update this note
    return await crud_note.update_note(db=db, note=note, note_in=note_in)

@router.delete("/{note_id}", response_model=Note, dependencies=[Depends(require_permission("delete"))])
async def delete_note(
    request: Request,
    note_id: UUID,
    db: AsyncSession = Depends(get_async_db),
):
    current_user: User = request.state.user
    note = await crud_note.get_note(db=db, note_id=note_id)
    if not note:
        raise HTTPException(status_code=404, detail="Note not found")
    # TODO: Add permission check to ensure user can delete this note
    await crud_note.delete_note(db=db, note=note)
    return note
