from fastapi import APIRouter, Depends, HTTPException, Request, UploadFile, File, Form
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional
import os
from PIL import Image
import io

from app.db.session import get_async_db
from app.crud.crud_asset import crud_asset
from app.crud import crud_customer, crud_chat
from app.schemas.asset import AssetResponse, FileUploadResponse, AssetType, AssetCreate
from app.core.s3 import s3_manager
from app.models.user import User
from app.auth.dependencies import require_permission

router = APIRouter()

def validate_file_metadata(file: UploadFile, file_size: int) -> tuple[bool, str, AssetType]:
    """Validate file based on its metadata without reading its content."""
    content_type = file.content_type

    if file_size > s3_manager.MAX_FILE_SIZE:
        return False, f"File too large. Max: {s3_manager.MAX_FILE_SIZE // (1024*1024)}MB", None

    if content_type in s3_manager.ALLOWED_IMAGE_TYPES:
        if file_size > s3_manager.MAX_IMAGE_SIZE:
            return False, f"Image too large. Max: {s3_manager.MAX_IMAGE_SIZE // (1024*1024)}MB", None
        file_type = AssetType.gif if content_type == "image/gif" else AssetType.image
        return True, "Valid image", file_type
    elif content_type in s3_manager.ALLOWED_VIDEO_TYPES:
        if file_size > s3_manager.MAX_VIDEO_SIZE:
            return False, f"Video too large. Max: {s3_manager.MAX_VIDEO_SIZE // (1024*1024)}MB", None
        return True, "Valid video", AssetType.video
    elif content_type in s3_manager.ALLOWED_AUDIO_TYPES:
        return True, "Valid audio", AssetType.audio
    elif content_type in s3_manager.ALLOWED_DOCUMENT_TYPES:
        return True, "Valid document", AssetType.document
    else:
        return False, f"Unsupported file type: {content_type}", None

def get_image_dimensions(file_content: bytes) -> tuple[Optional[int], Optional[int]]:
    """Get image dimensions from its byte content."""
    try:
        image = Image.open(io.BytesIO(file_content))
        return image.width, image.height
    except Exception:
        return None, None

@router.post("/upload", response_model=FileUploadResponse, dependencies=[Depends(require_permission("create"))])
async def upload_file(
    request: Request,
    file: UploadFile = File(...),
    db: AsyncSession = Depends(get_async_db),
):
    current_user: User = request.state.user
    """Upload a media file (image, video, etc.)"""

    # 1. Read file content ONCE.
    file_content = await file.read()
    file_size = len(file_content)

    # 2. Validate based on metadata and the size we just got.
    is_valid, message, file_type = validate_file_metadata(file, file_size)
    if not is_valid:
        raise HTTPException(status_code=400, detail=message)

    try:
        # Get image dimensions if it's an image
        width, height = (None, None)
        if file_type == AssetType.image:
            # Process image: resize and convert to WebP
            image = Image.open(io.BytesIO(file_content))
            width, height = image.size

            # Resize if larger than max dimensions (e.g., 1920x1080)
            max_dim = (1920, 1080) # Example max dimensions
            if width > max_dim[0] or height > max_dim[1]:
                image.thumbnail(max_dim, Image.Resampling.LANCZOS)
                width, height = image.size # Update dimensions after resize

            # Convert to WebP and save to bytes buffer
            output_buffer = io.BytesIO()
            image.save(output_buffer, format="WEBP", quality=85) # Adjust quality as needed
            file_content = output_buffer.getvalue()
            file.content_type = "image/webp" # Update content type
            file_size = len(file_content) # Update file size

        # 3. Upload to S3/MinIO using the content we already have.
        #    Pass the content as a file-like object.
        s3_key, s3_url = s3_manager.upload_file(
            file_obj=io.BytesIO(file_content), # Pass the bytes as a file-like object
            filename=file.filename.split('.')[0] + ".webp" if file_type == AssetType.image else file.filename,
            content_type=file.content_type
        )

        # 4. Create asset record in the database.
        asset_data = AssetCreate(
            filename=os.path.basename(s3_key),
            original_filename=file.filename,
            file_type=file_type,
            mime_type=file.content_type,
            file_size=file_size,
            s3_bucket=s3_manager.bucket_name,
            s3_key=s3_key,
            s3_url=s3_url,
            width=width,
            height=height,
            is_processed=True, # Simple files are immediately processed
            owner_id=current_user.id # Set owner for authenticated uploads
        )

        asset = await crud_asset.create_asset(db, asset_data)

        return FileUploadResponse(
            asset_id=asset.id,
            filename=asset.original_filename,
            file_type=asset.file_type,
            file_size=asset.file_size,
            s3_url=asset.s3_url
        )

    except Exception as e:
        # It's good practice to log the full error for debugging
        print(f"❌ Failed to upload file: {e}")

        # Provide specific error messages for common S3/MinIO issues
        error_message = str(e)
        if "InvalidAccessKeyId" in error_message:
            detail = "S3/MinIO not configured properly. Please check your credentials in .env file."
        elif "NoSuchBucket" in error_message:
            detail = "S3/MinIO bucket not found. Please ensure MinIO is running and bucket exists."
        elif "AccessDenied" in error_message or "Forbidden" in error_message:
            detail = "S3/MinIO access denied. Please check your credentials and permissions."
        elif "ConnectionError" in error_message or "EndpointConnectionError" in error_message:
            detail = "Cannot connect to S3/MinIO. Please ensure MinIO is running on the configured endpoint."
        else:
            detail = f"File upload failed: {error_message}"

        raise HTTPException(status_code=500, detail=detail)

@router.get("/assets", response_model=List[AssetResponse], dependencies=[Depends(require_permission("read"))])
async def list_assets(
    request: Request,
    skip: int = 0,
    limit: int = 100,
    file_type: Optional[str] = None,
    db: AsyncSession = Depends(get_async_db),
):
    """List uploaded assets"""
    if file_type:
        assets = await crud_asset.get_assets_by_type(db, file_type, skip, limit)
    else:
        assets = await crud_asset.get_assets(db, skip, limit)
    return assets

@router.get("/assets/{asset_id}", response_model=AssetResponse, dependencies=[Depends(require_permission("read"))])
async def get_asset(
    request: Request,
    asset_id: int,
    db: AsyncSession = Depends(get_async_db),
):
    """Get specific asset"""
    asset = await crud_asset.get_asset(db, asset_id)
    if not asset:
        raise HTTPException(status_code=404, detail="Asset not found")
    return asset

@router.delete("/assets/{asset_id}", dependencies=[Depends(require_permission("delete"))])
async def delete_asset(
    request: Request,
    asset_id: int,
    db: AsyncSession = Depends(get_async_db),
):
    """Delete an asset"""
    asset = await crud_asset.get_asset(db, asset_id)
    if not asset:
        raise HTTPException(status_code=404, detail="Asset not found")
    
    # Delete from S3
    s3_manager.delete_file(asset.s3_key)
    
    # Delete from database
    await crud_asset.delete_asset(db, asset_id)

    return {"message": "Asset deleted successfully"}

# NEW: Public endpoint for customer uploads
@router.post("/upload/public", response_model=FileUploadResponse)
async def upload_public_file(
    file: UploadFile = File(...),
    customer_id: str = Form(...),  # Require customer_id
    conversation_id: int = Form(...),  # And conversation_id
    db: AsyncSession = Depends(get_async_db)
):
    """
    Public-facing file upload endpoint for unauthenticated customers.
    Requires customer_id and conversation_id to associate the file correctly.
    """
    # 1. Verify the customer and conversation exist
    customer = await crud_customer.get_customer_by_customer_id(db, customer_id=customer_id)
    if not customer:
        raise HTTPException(status_code=404, detail="Customer not found")

    conversation = await crud_chat.get_conversation(db, conversation_id=conversation_id)
    if not conversation or conversation.customer_id != customer.id:
        raise HTTPException(status_code=403, detail="Invalid conversation for this customer")

    # 2. The rest of the logic is the same as the authenticated upload
    file_content = await file.read()
    file_size = len(file_content)

    # Validate file
    is_valid, message, file_type = validate_file_metadata(file, file_size)
    if not is_valid:
        raise HTTPException(status_code=400, detail=message)

    try:
        # Get image dimensions if it's an image
        width, height = (None, None)
        if file_type == AssetType.image:
            # Process image: resize and convert to WebP
            image = Image.open(io.BytesIO(file_content))
            width, height = image.size

            # Resize if larger than max dimensions (e.g., 1920x1080)
            max_dim = (1920, 1080) # Example max dimensions
            if width > max_dim[0] or height > max_dim[1]:
                image.thumbnail(max_dim, Image.Resampling.LANCZOS)
                width, height = image.size # Update dimensions after resize

            # Convert to WebP and save to bytes buffer
            output_buffer = io.BytesIO()
            image.save(output_buffer, format="WEBP", quality=85) # Adjust quality as needed
            file_content = output_buffer.getvalue()
            file.content_type = "image/webp" # Update content type
            file_size = len(file_content) # Update file size

        # Upload to S3/MinIO
        s3_key, s3_url = s3_manager.upload_file(
            file_obj=io.BytesIO(file_content),
            filename=file.filename.split('.')[0] + ".webp" if file_type == AssetType.image else file.filename,
            content_type=file.content_type
        )

        # Create asset record
        asset_data = AssetCreate(
            filename=os.path.basename(s3_key),
            original_filename=file.filename,
            file_type=file_type,
            mime_type=file.content_type,
            file_size=file_size,
            s3_bucket=s3_manager.bucket_name,
            s3_key=s3_key,
            s3_url=s3_url,
            width=width,
            height=height,
            is_processed=True,  # Simple files are immediately processed
            is_public=True # Mark as public for public uploads
        )

        asset = await crud_asset.create_asset(db, asset_data)

        return FileUploadResponse(
            asset_id=asset.id,
            filename=asset.original_filename,
            file_type=asset.file_type,
            file_size=asset.file_size,
            s3_url=asset.s3_url
        )

    except Exception as e:
        print(f"❌ Public file upload failed: {e}")

        # Provide specific error messages for common S3/MinIO issues
        error_message = str(e)
        if "InvalidAccessKeyId" in error_message:
            detail = "S3/MinIO not configured properly. Please check your credentials in .env file."
        elif "NoSuchBucket" in error_message:
            detail = "S3/MinIO bucket not found. Please ensure MinIO is running and bucket exists."
        elif "AccessDenied" in error_message or "Forbidden" in error_message:
            detail = "S3/MinIO access denied. Please check your credentials and permissions."
        elif "ConnectionError" in error_message or "EndpointConnectionError" in error_message:
            detail = "Cannot connect to S3/MinIO. Please ensure MinIO is running on the configured endpoint."
        else:
            detail = f"File upload failed: {error_message}"

        raise HTTPException(status_code=500, detail=detail)
