from fastapi import APIRouter
from .users import router as users_router
from .teams import router as teams_router
from .roles import router as roles_router
from .permissions import router as permissions_router

user_management_router = APIRouter(prefix="/user-management")
user_management_router.include_router(users_router, prefix="/users", tags=["users"])
user_management_router.include_router(teams_router, prefix="/teams", tags=["teams"])
user_management_router.include_router(roles_router, prefix="/roles", tags=["roles"])
user_management_router.include_router(permissions_router, prefix="/permissions", tags=["permissions"])