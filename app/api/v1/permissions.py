from fastapi import APIRouter, Depends, HTTPException, Request, status
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Dict, Any, List
from pydantic import BaseModel

from app.db.session import get_async_db
from app.models.user import User
from app.auth.dependencies import get_current_user
from app.core.casbin_enforcer import enforcer

router = APIRouter()

class PermissionCheckRequest(BaseModel):
    resource: str
    action: str

class PermissionCheckResponse(BaseModel):
    allowed: bool
    user_role: str
    resource: str
    action: str

class RolePermissionsResponse(BaseModel):
    role: str
    permissions: List[Dict[str, Any]]

@router.post("/check", response_model=PermissionCheckResponse)
async def check_permission(
    request: Request,
    permission_request: PermissionCheckRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_db),
):
    """Check if current user has permission for a specific resource and action."""
    
    # Get user role
    user_role = current_user.role_name if current_user.role_name else "Agent"
    
    # Check permission using Casbin
    allowed = enforcer.enforce(user_role, permission_request.resource, permission_request.action)
    
    return PermissionCheckResponse(
        allowed=allowed,
        user_role=user_role,
        resource=permission_request.resource,
        action=permission_request.action
    )

@router.get("/roles", response_model=List[RolePermissionsResponse])
async def get_user_roles_and_permissions(
    request: Request,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_db),
):
    """Get current user's roles and their permissions."""
    
    user_role = current_user.role_name if current_user.role_name else "Agent"
    
    # Get all policies for this role
    policies = enforcer.get_filtered_policy(0, user_role)
    
    permissions = []
    for policy in policies:
        if len(policy) >= 3:
            permissions.append({
                "resource": policy[1],
                "action": policy[2],
                "effect": policy[3] if len(policy) > 3 else "allow"
            })
    
    return [RolePermissionsResponse(
        role=user_role,
        permissions=permissions
    )]

@router.get("/resources")
async def get_available_resources(
    request: Request,
    current_user: User = Depends(get_current_user),
):
    """Get list of available resources and actions for permission management."""
    
    # Only admins can see this
    if current_user.role_name != "Admin":
        raise HTTPException(status_code=403, detail="Admin access required")
    
    resources = {
        "users": ["create", "read", "update", "delete"],
        "roles": ["create", "read", "update", "delete"],
        "conversations": ["create", "read", "update", "delete", "assign"],
        "messages": ["create", "read", "update", "delete"],
        "customers": ["create", "read", "update", "delete"],
        "teams": ["create", "read", "update", "delete"],
        "organizations": ["create", "read", "update", "delete"],
        "notes": ["create", "read", "update", "delete"],
        "labels": ["create", "read", "update", "delete"],
        "canned_responses": ["create", "read", "update", "delete"],
        "media": ["upload", "read", "delete"],
        "admin": ["access", "manage_users", "manage_roles", "view_analytics"]
    }
    
    return resources

class AddPermissionRequest(BaseModel):
    role: str
    resource: str
    action: str

@router.post("/add")
async def add_permission(
    request: Request,
    permission_request: AddPermissionRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_db),
):
    """Add a permission to a role. Admin access required."""
    
    if current_user.role_name != "Admin":
        raise HTTPException(status_code=403, detail="Admin access required")
    
    # Add policy to Casbin
    success = enforcer.add_policy(
        permission_request.role,
        permission_request.resource,
        permission_request.action
    )
    
    if success:
        # Save to database
        enforcer.save_policy()
        return {"message": f"Permission added: {permission_request.role} can {permission_request.action} {permission_request.resource}"}
    else:
        raise HTTPException(status_code=400, detail="Permission already exists or failed to add")

class RemovePermissionRequest(BaseModel):
    role: str
    resource: str
    action: str

@router.delete("/remove")
async def remove_permission(
    request: Request,
    permission_request: RemovePermissionRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_db),
):
    """Remove a permission from a role. Admin access required."""
    
    if current_user.role_name != "Admin":
        raise HTTPException(status_code=403, detail="Admin access required")
    
    # Remove policy from Casbin
    success = enforcer.remove_policy(
        permission_request.role,
        permission_request.resource,
        permission_request.action
    )
    
    if success:
        # Save to database
        enforcer.save_policy()
        return {"message": f"Permission removed: {permission_request.role} can no longer {permission_request.action} {permission_request.resource}"}
    else:
        raise HTTPException(status_code=400, detail="Permission not found or failed to remove")

@router.get("/policies")
async def get_all_policies(
    request: Request,
    current_user: User = Depends(get_current_user),
):
    """Get all Casbin policies. Admin access required."""
    
    if current_user.role_name != "Admin":
        raise HTTPException(status_code=403, detail="Admin access required")
    
    policies = enforcer.get_policy()
    
    formatted_policies = []
    for policy in policies:
        if len(policy) >= 3:
            formatted_policies.append({
                "role": policy[0],
                "resource": policy[1],
                "action": policy[2],
                "effect": policy[3] if len(policy) > 3 else "allow"
            })
    
    return {
        "total": len(formatted_policies),
        "policies": formatted_policies
    }
