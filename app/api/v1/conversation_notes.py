from fastapi import APIRouter, Depends, HTTPException, Request, status
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List
from uuid import UUID

from app.db.session import get_async_db
from app.crud import crud_conversation_note, crud_chat # Added crud_chat
from app.schemas.conversation_note import ConversationNote, ConversationNoteCreate, ConversationNoteUpdate
from app.models.user import User
from app.auth.dependencies import get_current_user, require_permission
from app.core.permissions import enforcer # Added enforcer

router = APIRouter()

@router.post("/", response_model=ConversationNote, status_code=status.HTTP_201_CREATED, dependencies=[Depends(require_permission("create"))])
async def create_conversation_note(
    request: Request,
    note_in: ConversationNoteCreate,
    db: AsyncSession = Depends(get_async_db),
):
    current_user: User = request.state.user
    
    # Check permission to create notes for this conversation
    conversation = await crud_chat.get_conversation(db, id=note_in.conversation_id)
    if not conversation:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Conversation not found")

    # Check if user has permission to update/manage this conversation
    # This implies permission to add notes to it.
    # The object for Casbin should be the conversation itself.
    obj_conversation = f"company:{current_user.company_id}:conversation:{conversation.id}"
    user_role = current_user.role_name
    if not enforcer.enforce(user_role, obj_conversation, "update"):
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized to add notes to this conversation")

    return await crud_conversation_note.create_conversation_note(db=db, note_in=note_in)

@router.get("/conversation/{conversation_id}", response_model=List[ConversationNote], dependencies=[Depends(require_permission("read"))])
async def get_conversation_notes_by_conversation(
    request: Request,
    conversation_id: UUID,
    db: AsyncSession = Depends(get_async_db),
    skip: int = 0,
    limit: int = 100,
):
    current_user: User = request.state.user

    # NEW: Check permission to view notes for this conversation
    conversation = await crud_chat.get_conversation(db, id=conversation_id)
    if not conversation:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Conversation not found")

    # Check if user has permission to read this conversation
    obj_conversation = f"company:{current_user.company_id}:conversation:{conversation.id}"
    
    # Check if user's role has permission
    user_role = current_user.role_name
    if not enforcer.enforce(user_role, obj_conversation, "read"):
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized to view notes for this conversation")

    return await crud_conversation_note.get_conversation_notes_by_conversation(
        db=db, conversation_id=conversation_id, skip=skip, limit=limit
    )

@router.put("/{note_id}", response_model=ConversationNote, dependencies=[Depends(require_permission("update"))])
async def update_conversation_note(
    request: Request,
    note_id: UUID,
    note_in: ConversationNoteUpdate,
    db: AsyncSession = Depends(get_async_db),
):
    current_user: User = request.state.user
    note = await crud_conversation_note.get_conversation_note(db=db, note_id=note_id)
    if not note:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Conversation note not found")
    
    # Only the creator of the note can update it
    # This check is now handled by Casbin policies
    
    return await crud_conversation_note.update_conversation_note(db=db, note=note, note_in=note_in)

@router.delete("/{note_id}", status_code=status.HTTP_204_NO_CONTENT, dependencies=[Depends(require_permission("delete"))])
async def delete_conversation_note(
    request: Request,
    note_id: UUID,
    db: AsyncSession = Depends(get_async_db),
):
    current_user: User = request.state.user
    note = await crud_conversation_note.get_conversation_note(db=db, note_id=note_id)
    if not note:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Conversation note not found")
    
    # Only the creator of the note can delete it
    # This check is now handled by Casbin policies
        
    await crud_conversation_note.delete_conversation_note(db=db, note=note)
    return None