from fastapi import APIRouter, Depends, HTTPException, Request, status
from fastapi_cache.decorator import cache
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.session import get_async_db
from app.crud import crud_chat
from app.models.user import User
from app.auth.dependencies import get_current_user, require_permission
from app.core.websocket_manager import manager
import json

router = APIRouter()

@router.delete("/{message_id}", status_code=status.HTTP_204_NO_CONTENT, dependencies=[Depends(require_permission("delete"))])
async def delete_message(
    request: Request,
    message_id: int,
    db: AsyncSession = Depends(get_async_db),
):
    """
    Soft-delete a message. Only the sender or an admin can delete a message.
    """
    message = await crud_chat.get_message(db, message_id=message_id)
    if not message:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Message not found")

    # Permission check: Allow admins or the original sender to delete
    # This logic is now handled by <PERSON>asbin policies

    # Soft delete the message
    deleted_message = await crud_chat.soft_delete_message(db, message_id=message_id)
    if not deleted_message:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to delete message")

    # Broadcast the deletion event to the conversation room
    deletion_event = {
        "type": "message_deleted",
        "message_id": message_id,
        "timestamp": deleted_message.created_at.isoformat()
    }
    
    await manager.broadcast_to_conversation(
        json.dumps(deletion_event),
        message.conversation_id
    )
    
    return

@router.patch("/{message_id}/restore", status_code=status.HTTP_200_OK, dependencies=[Depends(require_permission("restore"))])
async def restore_message(
    request: Request,
    message_id: int,
    db: AsyncSession = Depends(get_async_db),
):
    """
    Restore a soft-deleted message. Only admins can restore messages.
    """
    message = await crud_chat.get_message(db, message_id=message_id)
    if not message:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Message not found")
    
    # Restore the message
    restored_message = await crud_chat.restore_message(db, message_id=message_id)
    if not restored_message:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to restore message")
    
    # Broadcast the restoration event to the conversation room
    restoration_event = {
        "type": "message_restored",
        "message_id": message_id,
        "timestamp": restored_message.created_at.isoformat()
    }
    
    await manager.broadcast_to_conversation(
        json.dumps(restoration_event),
        message.conversation_id
    )
    
    return {"message": "Message restored successfully"}

@router.get("/{message_id}", dependencies=[Depends(require_permission("read"))])
@cache(expire=120)
async def get_message(
    request: Request,
    message_id: int,
    db: AsyncSession = Depends(get_async_db),
):
    """
    Get a specific message by ID. Includes deleted messages for admins.
    """
    message = await crud_chat.get_message(db, message_id=message_id)
    if not message:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Message not found")
    
    return message
