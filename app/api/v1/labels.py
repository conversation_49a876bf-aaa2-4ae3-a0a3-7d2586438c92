from fastapi import APIRouter, Depends, HTTPException, Request, status
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List

from app.db.session import get_async_db
from app.crud import crud_label, crud_chat
from app.schemas.label import Label<PERSON><PERSON>, LabelResponse, ConversationLabelRequest
from app.schemas.chat import ConversationResponse
from app.models.user import User
from app.auth.dependencies import require_permission

router = APIRouter()

@router.post("/", response_model=LabelResponse, status_code=status.HTTP_201_CREATED, dependencies=[Depends(require_permission("create"))])
async def create_label(
    request: Request,
    label_in: LabelCreate,
    db: AsyncSession = Depends(get_async_db),
):
    current_user: User = request.state.user
    """
    Create a new label for the user's organization.
    """
    if not current_user.company_id:
        raise HTTPException(status_code=400, detail="User not part of an organization.")
    
    return await crud_label.create_label(db, obj_in=label_in, organization_id=current_user.company_id)

@router.get("/", response_model=List[LabelResponse], dependencies=[Depends(require_permission("read"))])
async def list_labels_for_organization(
    request: Request,
    db: AsyncSession = Depends(get_async_db),
):
    current_user: User = request.state.user
    """
    List all labels available for the user's organization.
    """
    if not current_user.company_id:
        return []
    return await crud_label.get_labels_by_organization(db, organization_id=current_user.company_id)