from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_
from app.models.chat import Conversation, Message
from app.models.team import Team
from app.db.session import get_async_db
from app.models.user import User
from app.auth.dependencies import require_any_role, get_current_user
from datetime import datetime, timedelta
from typing import List, Optional

router = APIRouter()

@router.get("/stats")
async def get_analytics_stats(
    current_user: User = Depends(require_any_role("Admin", "Manager")),
    db: AsyncSession = Depends(get_async_db)
):
    """Get key analytics stats for the organization."""
    org_id = current_user.organization_ids

    # Total conversations for the organization
    total_conversations = await db.execute(
        select(func.count(Conversation.id))
        .where(Conversation.organization_id == org_id)
    )

    # Open conversations
    open_conversations = await db.execute(
        select(func.count(Conversation.id))
        .where(and_(
            Conversation.organization_id == org_id,
            Conversation.status == 'open'
        ))
    )

    # Active conversations (not archived)
    active_conversations = await db.execute(
        select(func.count(Conversation.id))
        .where(and_(
            Conversation.organization_id == org_id,
            Conversation.status != 'archived'
        ))
    )

    # Total messages in the organization
    total_messages = await db.execute(
        select(func.count(Message.id))
        .join(Conversation, Message.conversation_id == Conversation.id)
        .where(Conversation.organization_id == org_id)
    )

    # Messages today
    today = datetime.now().date()
    messages_today = await db.execute(
        select(func.count(Message.id))
        .join(Conversation, Message.conversation_id == Conversation.id)
        .where(and_(
            Conversation.organization_id == org_id,
            func.date(Message.created_at) == today
        ))
    )

    return {
        "total_conversations": total_conversations.scalar_one(),
        "open_conversations": open_conversations.scalar_one(),
        "active_conversations": active_conversations.scalar_one(),
        "total_messages": total_messages.scalar_one(),
        "messages_today": messages_today.scalar_one()
    }

@router.get("/agent-performance")
async def get_agent_performance(
    current_user: User = Depends(require_any_role("Admin", "Manager")),
    db: AsyncSession = Depends(get_async_db)
):
    """Get agent performance statistics."""
    org_id = current_user.organization_ids

    # Get message counts by agent
    result = await db.execute(
        select(User.full_name, User.email, func.count(Message.id).label('message_count'))
        .join(Message, Message.user_id == User.id)
        .join(Conversation, Message.conversation_id == Conversation.id)
        .where(and_(
            Conversation.organization_id == org_id,
            User.organization_ids == org_id
        ))
        .group_by(User.id, User.full_name, User.email)
        .order_by(func.count(Message.id).desc())
    )

    return [
        {
            "agent_name": row[0],
            "agent_email": row[1],
            "messages_sent": row[2]
        }
        for row in result.all()
    ]

@router.get("/team-performance")
async def get_team_performance(
    current_user: User = Depends(require_any_role("Admin", "Manager")),
    db: AsyncSession = Depends(get_async_db)
):
    """Get team performance statistics."""
    org_id = current_user.organization_ids

    # Get conversation counts by team
    result = await db.execute(
        select(Team.name, func.count(Conversation.id).label('conversation_count'))
        .join(Conversation, Conversation.assigned_team_id == Team.id)
        .where(and_(
            Team.organization_id == org_id,
            Conversation.organization_id == org_id
        ))
        .group_by(Team.id, Team.name)
        .order_by(func.count(Conversation.id).desc())
    )

    return [
        {
            "team_name": row[0],
            "conversations_assigned": row[1]
        }
        for row in result.all()
    ]
