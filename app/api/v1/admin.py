"""
Admin endpoints for database management and fixes.
"""
from app.core.permissions import get_enforcer
import casbin
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Request, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text
from app.db.session import get_async_db
from app.crud.crud_activity_log import crud_activity_log, get_activity_logs_query
from app.schemas.activity_log import ActivityLogResponse
from app.auth.dependencies import require_permission
from fastapi_pagination import Page
from fastapi_pagination.ext.sqlalchemy import paginate as async_paginate

router = APIRouter()


@router.get(
    "/casbin-policy", 
    response_model=List[List[str]], 
    dependencies=[Depends(require_permission("admin"))] # Secure it
)
async def get_casbin_policy(
    request: Request,
    enforcer: casbin.Enforcer = Depends(get_enforcer)
):
    """
    Get all raw Casbin policies from the database. For debugging and admin visibility.
    """
    return enforcer.get_policy()

@router.get("/activity-log", response_model=Page[ActivityLogResponse], dependencies=[Depends(require_permission("admin"))])
async def get_activity_log(
    request: Request,
    db: AsyncSession = Depends(get_async_db),
    user_id: Optional[int] = Query(None, description="Filter by user ID.")
):
    """Get the system-wide activity log with pagination. Admin access required."""
    query = get_activity_logs_query(user_id=user_id)
    return await async_paginate(db, query)

@router.post("/fix-database", dependencies=[Depends(require_permission("admin"))])
async def fix_database_schema(
    request: Request,
    db: AsyncSession = Depends(get_async_db),
):
    """Fix database schema issues. Admin access required."""
    try:
        # Check if organization_id column exists in customers table
        result = await db.execute(text("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'customers' AND column_name = 'organization_id'
        """))
        
        columns = result.fetchall()
        
        if columns:
            return {"message": "organization_id column already exists in customers table!", "status": "ok"}
        else:
            # Add the organization_id column
            await db.execute(text("""
                ALTER TABLE customers 
                ADD COLUMN organization_id INTEGER REFERENCES organizations(id)
            """))
            
            await db.commit()
            
            # Set default organization for existing customers
            # Get the first organization ID
            org_result = await db.execute(text("SELECT id FROM organizations ORDER BY id LIMIT 1"))
            org_row = org_result.fetchone()
            
            if org_row:
                default_org_id = org_row[0]
                await db.execute(text("""
                    UPDATE customers 
                    SET organization_id = :org_id 
                    WHERE organization_id IS NULL
                """), {"org_id": default_org_id})
                
                await db.commit()
                
                return {
                    "message": "Database schema fixed successfully!",
                    "status": "fixed",
                    "default_org_id": default_org_id
                }
            else:
                return {
                    "message": "organization_id column added but no organizations found",
                    "status": "partial"
                }
                
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"Database fix failed: {str(e)}")

@router.get("/database-status", dependencies=[Depends(require_permission("admin"))])
async def check_database_status(
    request: Request,
    db: AsyncSession = Depends(get_async_db),
):
    """Check database status and schema. Admin access required."""
    try:
        status = {}
        
        # Check customers table
        customers_result = await db.execute(text("""
            SELECT COUNT(*) as count
            FROM customers
        """))
        customers_count = customers_result.fetchone()[0]
        status["customers_count"] = customers_count
        
        # Check if organization_id column exists
        org_col_result = await db.execute(text("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'customers' AND column_name = 'organization_id'
        """))
        status["customers_has_org_id"] = bool(org_col_result.fetchall())
        
        # Check conversations
        conv_result = await db.execute(text("""
            SELECT COUNT(*) as count
            FROM conversations
        """))
        conversations_count = conv_result.fetchone()[0]
        status["conversations_count"] = conversations_count
        
        # Check organizations
        org_result = await db.execute(text("""
            SELECT COUNT(*) as count
            FROM organizations
        """))
        organizations_count = org_result.fetchone()[0]
        status["organizations_count"] = organizations_count
        
        return status
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Database status check failed: {str(e)}")

@router.post("/create-test-data", dependencies=[Depends(require_permission("admin"))])
async def create_test_data(
    request: Request,
    db: AsyncSession = Depends(get_async_db),
):
    """Create test customers and conversations. Admin access required."""
    try:
        # Get the first organization
        org_result = await db.execute(text("SELECT id FROM organizations ORDER BY id LIMIT 1"))
        org_row = org_result.fetchone()
        
        if not org_row:
            raise HTTPException(status_code=400, detail="No organizations found. Create an organization first.")
        
        org_id = org_row[0]
        
        # Create test customers
        test_customers = [
            ("cust_001", "John Doe", "<EMAIL>", "******-0001", "New York, NY"),
            ("cust_002", "Jane Smith", "<EMAIL>", "******-0002", "Los Angeles, CA"),
            ("cust_003", "Mike Wilson", "<EMAIL>", "******-0003", "Chicago, IL"),
        ]
        
        created_customers = []
        for customer_id, name, email, phone, location in test_customers:
            # Check if customer already exists
            existing = await db.execute(text("""
                SELECT id FROM customers WHERE customer_id = :customer_id
            """), {"customer_id": customer_id})
            
            if not existing.fetchone():
                # Create customer
                result = await db.execute(text("""
                    INSERT INTO customers (customer_id, name, email, phone, location, organization_id, is_active)
                    VALUES (:customer_id, :name, :email, :phone, :location, :org_id, true)
                    RETURNING id
                """), {
                    "customer_id": customer_id,
                    "name": name,
                    "email": email,
                    "phone": phone,
                    "location": location,
                    "org_id": org_id
                })
                
                customer_db_id = result.fetchone()[0]
                created_customers.append({"id": customer_db_id, "customer_id": customer_id, "name": name})
        
        await db.commit()
        
        return {
            "message": "Test data created successfully!",
            "created_customers": created_customers,
            "organization_id": org_id
        }
        
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"Test data creation failed: {str(e)}")