from functools import wraps
from fastapi import Request, BackgroundTasks, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from app.services.audit_log_service import audit_log_service
from typing import Optional

# This makes getting background_tasks optional in the decorated function
def get_optional_background_tasks(background_tasks: BackgroundTasks):
    return background_tasks

def audit(action: str):
    def decorator(func):
        @wraps(func)
        async def wrapper(request: Request, db: AsyncSession, *args, **kwargs):
            # We can inject our own dependency to get background_tasks if it exists
            background_tasks: Optional[BackgroundTasks] = kwargs.get("background_tasks")
            
            # Execute the original function to get the response
            response = await func(request=request, db=db, *args, **kwargs)
            
            current_user = getattr(request.state, "user", None)
            
            # The resource is expected to be set by the require_permission dependency
            target_resource = getattr(request.state, "resource", None)
            
            # If the response is the target (e.g., on create), use it.
            if response and not target_resource:
                target_resource = response

            log_method = audit_log_service.log_activity_background if background_tasks else audit_log_service.log_activity_sync

            # Await sync method, or add task for background method
            if background_tasks:
                 background_tasks.add_task(
                     audit_log_service._create_log_entry_wrapper, # A wrapper to handle the async session
                     db=db, # Pass the session from the endpoint
                     request=request,
                     current_user=current_user,
                     action=action,
                     target_resource=target_resource
                 )
            else:
                await audit_log_service.log_activity_sync(
                    db=db,
                    request=request,
                    current_user=current_user,
                    action=action,
                    target_resource=target_resource
                )
            return response
        return wrapper
    return decorator