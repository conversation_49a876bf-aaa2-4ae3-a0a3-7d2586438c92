from fastapi import APIRouter
from app.api.v1.chat import chat_router
from app.api.v1.user_management import user_management_router
from app.api.v1 import auth, customers, organizations, media, analytics, notifications, canned_responses, websocket

v1_router = APIRouter(prefix="/v1")

# Endpoint routers under the v1 prefix
v1_router.include_router(chat_router)
v1_router.include_router(user_management_router)
v1_router.include_router(auth.router, prefix="/auth", tags=["auth"])
v1_router.include_router(customers.router, prefix="/customers", tags=["customers"])
v1_router.include_router(organizations.router, prefix="/organizations", tags=["organizations"])
v1_router.include_router(media.router, prefix="/media", tags=["media"])
v1_router.include_router(analytics.router, prefix="/analytics", tags=["analytics"])
v1_router.include_router(notifications.router, prefix="/notifications", tags=["notifications"])
v1_router.include_router(canned_responses.router, prefix="/canned-responses", tags=["canned-responses"])
v1_router.include_router(websocket.router, prefix="/ws", tags=["websocket"])

api_router = APIRouter()
api_router.include_router(v1_router)

