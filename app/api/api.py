from fastapi import APIRouter
from fastapi import APIRouter
from app.api.v1 import (
    conversations,
    websocket,
    users, auth,
    organizations,
    customers,
    teams,
    media,
    messages,
    admin,
    canned_responses,
    roles,
    permissions,
    labels,
    notes,
    conversation_notes
)

v1_router = APIRouter(prefix="/v1")

# Include all endpoint routers under the v1 prefix
v1_router.include_router(auth.router, prefix="/auth", tags=["auth"])
v1_router.include_router(organizations.router, prefix="/organizations", tags=["organizations"])
v1_router.include_router(customers.router, prefix="/customers", tags=["customers"])
v1_router.include_router(teams.router, prefix="/teams", tags=["teams"])
v1_router.include_router(users.router, prefix="/users", tags=["users"])
v1_router.include_router(canned_responses.router, prefix="/canned-responses", tags=["canned-responses"]) 
v1_router.include_router(conversations.router, prefix="/conversations", tags=["conversations"])
v1_router.include_router(media.router, prefix="/media", tags=["media"])
v1_router.include_router(messages.router, prefix="/messages", tags=["messages"])
v1_router.include_router(websocket.router, prefix="/ws", tags=["websocket"])
v1_router.include_router(admin.router, prefix="/admin", tags=["admin"])
v1_router.include_router(roles.router, prefix="/roles", tags=["roles"])
v1_router.include_router(permissions.router, prefix="/permissions", tags=["permissions"])
v1_router.include_router(labels.router, prefix="/labels", tags=["labels"])
v1_router.include_router(notes.router, prefix="/notes", tags=["notes"])
v1_router.include_router(conversation_notes.router, prefix="/conversation-notes", tags=["conversation-notes"])

api_router = APIRouter()
api_router.include_router(v1_router)

