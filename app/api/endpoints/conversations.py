from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List

from app.db.session import get_async_db
from app.crud import crud_chat, crud_team
from app.schemas.chat import Conversation<PERSON><PERSON>, ConversationResponse, MessageResponse, MessageCreate, MessageCreateRequest
from app.models.user import User, UserRole
from app.auth.dependencies import get_current_active_admin, get_current_active_agent
from app.services.conversation_service import ConversationService

router = APIRouter()

#* A placeholder function for sending an email
def send_team_notification_email(team_email: str, conversation_id: int):
    """Send notification email to team about new conversation"""
    print(f"📧 Sending email to {team_email} about new conversation {conversation_id}")

@router.post("/", response_model=ConversationResponse)
async def create_conversation(
    conversation: ConversationCreate,
    service: ConversationService = Depends(ConversationService)
):
    """
    Create a new conversation.
    The service layer handles auto-assignment and notifications.
    """
    return await service.create_conversation(conversation)

@router.get("/{conversation_id}", response_model=ConversationResponse)
async def get_conversation(
    conversation_id: int,
    db: AsyncSession = Depends(get_async_db)
):
    """Get a specific conversation by ID"""
    conversation = await crud_chat.get_conversation(db=db, conversation_id=conversation_id)
    if conversation is None:
        raise HTTPException(status_code=404, detail="Conversation not found")
    return conversation

@router.get("/", response_model=List[ConversationResponse])
async def get_conversations(
    skip: int = 0,
    limit: int = 100,
    db: AsyncSession = Depends(get_async_db),
    current_admin: User = Depends(get_current_active_admin)
):
    """Get all conversations with pagination. Admin access required."""
    return await crud_chat.get_conversations(db=db, skip=skip, limit=limit)

@router.get("/{conversation_id}/messages", response_model=List[MessageResponse])
async def get_conversation_messages(
    conversation_id: int,
    db: AsyncSession = Depends(get_async_db)
):
    """Get all messages for a specific conversation"""
    messages = await crud_chat.get_messages_by_conversation(db=db, conversation_id=conversation_id)
    return messages

@router.post("/{conversation_id}/messages", response_model=MessageResponse)
async def create_message(
    conversation_id: int,
    message: MessageCreateRequest,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_active_agent)  # Agent or Admin access required
):
    """Create a new message in a conversation. Agent or Admin access required."""
    # Verify conversation exists
    conversation = await crud_chat.get_conversation(db=db, conversation_id=conversation_id)
    if not conversation:
        raise HTTPException(status_code=404, detail="Conversation not found")

    # Create the message with conversation_id
    message_data = MessageCreate(
        conversation_id=conversation_id,
        content=message.content,
        sender=message.sender,
        message_type=message.message_type
    )
    created_message = await crud_chat.create_message(db=db, message=message_data)

    return created_message

@router.post("/{conversation_id}/assign/{team_id}", response_model=ConversationResponse)
async def assign_team(
    conversation_id: int,
    team_id: int,
    db: AsyncSession = Depends(get_async_db),
    _: User = Depends(get_current_active_admin)  # Admin access required
):
    """Assign a team to a conversation. Admin access required."""
    team = await crud_team.get_team(db=db, team_id=team_id)
    if not team:
        raise HTTPException(status_code=404, detail="Team not found")

    conversation = await crud_chat.assign_team_to_conversation(
        db, conversation_id=conversation_id, team_id=team_id
    )
    if not conversation:
        raise HTTPException(status_code=404, detail="Conversation not found")
    return conversation

@router.get("/unassigned", response_model=List[ConversationResponse])
async def get_unassigned_conversations(
    db: AsyncSession = Depends(get_async_db),
    _: User = Depends(get_current_active_agent)  # Agent or Admin access required
):
    """Get unassigned conversations. Agent or Admin access required."""
    return await crud_chat.get_unassigned_conversations(db=db)

@router.get("/team/{team_id}", response_model=List[ConversationResponse])
async def get_team_conversations(
    team_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_active_agent)
):
    """Get conversations for a specific team. Admins can see any, agents only their team."""
    if current_user.role == UserRole.agent and current_user.team_id != team_id:
        raise HTTPException(status_code=403, detail="Agents can only view their team's conversations")

    return await crud_chat.get_conversations_by_team(db=db, team_id=team_id)

@router.get("/customer/{customer_id}", response_model=List[ConversationResponse])
async def get_conversations_by_customer(
    customer_id: int,
    db: AsyncSession = Depends(get_async_db),
    _: User = Depends(get_current_active_agent)  # Agent or Admin access required
):
    """
    Get all conversations for a specific customer.
    """
    # TODO: Add permission check to ensure agent's organization matches customer's organization
    return await crud_chat.get_conversations_by_customer(db=db, customer_id=customer_id)