from fastapi import APIRouter, Depends, HTTPException, status
from fastapi_cache.decorator import cache
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List

from app.db.session import get_async_db
from app.crud import crud_organization
from app.schemas.organization import OrganizationResponse, OrganizationCreate, OrganizationUpdate
from app.models.user import User
from app.auth.dependencies import (
    # Casbin-based dependencies
    require_permission, organization_resource_resolver, organizations_resource_resolver,
    require_read_organizations, require_edit_organizations
)

router = APIRouter()

@router.post("/", response_model=OrganizationResponse)
async def create_organization(
    organization: OrganizationCreate,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(require_edit_organizations()),
):
    """Create a new organization. Requires 'edit' permission on 'organizations' resource."""
    return await crud_organization.create_organization(db=db, organization=organization)

@router.get("/", response_model=List[OrganizationResponse])
@cache(expire=3600)
async def list_organizations(
    skip: int = 0,
    limit: int = 100,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(require_read_organizations()),
):
    """List all organizations. Requires 'read' permission on 'organizations' resource."""
    return await crud_organization.get_organizations(db=db, skip=skip, limit=limit)

@router.get("/{organization_id}", response_model=OrganizationResponse)
@cache(expire=3600)
async def get_organization(
    organization_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(require_permission("read", organization_resource_resolver)),
):
    """Get a specific organization by ID. Requires 'read' permission on specific organization."""
    organization = await crud_organization.get_organization(db=db, organization_id=organization_id)
    if not organization:
        raise HTTPException(status_code=404, detail="Organization not found")
    return organization

@router.put("/{organization_id}", response_model=OrganizationResponse)
async def update_organization(
    organization_id: int,
    organization_update: OrganizationUpdate,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(require_permission("edit", organization_resource_resolver)),
):
    """Update an organization. Requires 'edit' permission on specific organization resource."""
    organization = await crud_organization.update_organization(
        db=db, organization_id=organization_id, organization_update=organization_update
    )
    if not organization:
        raise HTTPException(status_code=404, detail="Organization not found")
    return organization

@router.delete("/{organization_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_organization(
    organization_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(require_permission("delete", organization_resource_resolver)),
):
    """Soft-delete an organization. Requires 'delete' permission on specific organization resource."""
    org_to_delete = await crud_organization.get_organization(db, organization_id=organization_id)
    if not org_to_delete:
        raise HTTPException(status_code=404, detail="Organization not found")

    await crud_organization.soft_delete_organization(db=db, organization_id=organization_id)
    return

@router.patch("/{organization_id}/restore", response_model=OrganizationResponse)
async def restore_organization_endpoint(
    organization_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(require_permission("edit", organization_resource_resolver)),
):
    """Restore a soft-deleted organization. Requires 'edit' permission on specific organization resource."""
    restored_org = await crud_organization.restore_organization(db, organization_id=organization_id)
    if not restored_org:
        raise HTTPException(status_code=404, detail="Organization not found or was not deleted.")
    return restored_org
