from fastapi import APIRouter, Depends, HTTPException, status
from fastapi_cache.decorator import cache
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.session import get_async_db
from app.crud import crud_chat
from app.models.user import User, UserRole
from app.auth.dependencies import get_current_user
from app.core.websocket_manager import manager
import json

router = APIRouter()

@router.delete("/{message_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_message(
    message_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user),
):
    """
    Soft-delete a message. Only the sender or an admin can delete a message.
    """
    message = await crud_chat.get_message(db, message_id=message_id)
    if not message:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Message not found")

    # Permission check: Allow admins or the original sender to delete
    if current_user.role != UserRole.admin and message.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, 
            detail="Not authorized to delete this message"
        )

    # Soft delete the message
    deleted_message = await crud_chat.soft_delete_message(db, message_id=message_id)
    if not deleted_message:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to delete message")

    # Broadcast the deletion event to the conversation room
    deletion_event = {
        "type": "message_deleted",
        "message_id": message_id,
        "deleted_by": {
            "id": current_user.id,
            "name": current_user.full_name,
            "role": current_user.role.value
        },
        "timestamp": deleted_message.created_at.isoformat()
    }
    
    await manager.broadcast_to_conversation(
        json.dumps(deletion_event),
        message.conversation_id
    )
    
    return

@router.patch("/{message_id}/restore", status_code=status.HTTP_200_OK)
async def restore_message(
    message_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user),
):
    """
    Restore a soft-deleted message. Only admins can restore messages.
    """
    if current_user.role != UserRole.admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only admins can restore deleted messages"
        )
    
    message = await crud_chat.get_message(db, message_id=message_id)
    if not message:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Message not found")
    
    if not message.deleted:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Message is not deleted")
    
    # Restore the message
    restored_message = await crud_chat.restore_message(db, message_id=message_id)
    if not restored_message:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to restore message")
    
    # Broadcast the restoration event to the conversation room
    restoration_event = {
        "type": "message_restored",
        "message_id": message_id,
        "restored_by": {
            "id": current_user.id,
            "name": current_user.full_name,
            "role": current_user.role.value
        },
        "timestamp": restored_message.created_at.isoformat()
    }
    
    await manager.broadcast_to_conversation(
        json.dumps(restoration_event),
        message.conversation_id
    )
    
    return {"message": "Message restored successfully"}

@router.get("/{message_id}")
@cache(expire=120)
async def get_message(
    message_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user),
):
    """
    Get a specific message by ID. Includes deleted messages for admins.
    """
    message = await crud_chat.get_message(db, message_id=message_id)
    if not message:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Message not found")
    
    # Check if user has access to this conversation
    conversation = message.conversation
    if (current_user.role not in [UserRole.admin, UserRole.agent] and 
        conversation.customer_id != current_user.id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to view this message"
        )
    
    # Hide deleted messages from non-admins
    if message.deleted and current_user.role != UserRole.admin:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Message not found")
    
    return message
