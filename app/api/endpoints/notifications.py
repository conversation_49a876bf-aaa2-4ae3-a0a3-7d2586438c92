"""
Real-time notification endpoints
"""

import json
import logging
from datetime import datetime
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends, HTTPException, Query
from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.session import get_async_db
from app.models.user import User
from app.auth.dependencies import get_current_user, get_current_active_admin, get_current_user_from_websocket_token
from app.services.notification_service import notification_service
from app.schemas.notification import NotificationResponse, NotificationCreate, NotificationType, NotificationPriority
from app.crud import crud_user

logger = logging.getLogger(__name__)

router = APIRouter()

@router.websocket("/ws/notifications")
async def websocket_notifications(
    websocket: WebSocket,
    token: str = Query(...),
    db: AsyncSession = Depends(get_async_db)
):
    """WebSocket endpoint for real-time notifications"""
    await websocket.accept()
    
    user = None
    connection_id = None
    
    try:
        # Authenticate user from token
        user = await get_current_user_from_websocket_token(token, db)
        if not user or not user.is_active:
            await websocket.close(code=4001, reason="Authentication failed")
            return
        
        logger.info(f"🔔 User {user.full_name} ({user.id}) connected to notifications")
        
        # Add connection to notification service
        connection_id = await notification_service.add_connection(
            user_id=user.id,
            websocket=websocket,
            organization_id=user.organization_id,
            team_id=user.team_id
        )
        
        # Send welcome message
        welcome_notification = NotificationResponse(
            id="welcome",
            type=NotificationType.SYSTEM_ALERT,
            title="Connected",
            message="Real-time notifications are now active",
            priority=NotificationPriority.LOW,
            timestamp=datetime.utcnow(),
            organization_id=user.organization_id
        )
        await websocket.send_text(welcome_notification.model_dump_json())
        
        # Keep connection alive and handle incoming messages
        while True:
            try:
                # Wait for messages (ping/pong, mark read, etc.)
                data = await websocket.receive_text()
                message = json.loads(data)
                
                # Handle different message types
                if message.get("type") == "ping":
                    await websocket.send_text(json.dumps({"type": "pong"}))
                
                elif message.get("type") == "mark_read":
                    notification_id = message.get("notification_id")
                    if notification_id:
                        await notification_service.mark_notification_read(notification_id, user.id)
                
                elif message.get("type") == "get_history":
                    # Send recent notifications
                    notifications = await notification_service.get_user_notifications(
                        user_id=user.id,
                        organization_id=user.organization_id,
                        limit=message.get("limit", 20)
                    )
                    await websocket.send_text(json.dumps({
                        "type": "history",
                        "notifications": [n.model_dump() for n in notifications]
                    }))
                
            except WebSocketDisconnect:
                break
            except json.JSONDecodeError:
                logger.warning(f"⚠️ Invalid JSON received from user {user.id}")
            except Exception as e:
                logger.error(f"❌ Error handling WebSocket message: {e}")
                break
    
    except WebSocketDisconnect:
        logger.info(f"🔔 User {user.id if user else 'unknown'} disconnected from notifications")
    except Exception as e:
        logger.error(f"❌ WebSocket error: {e}")
        try:
            await websocket.close(code=4000, reason="Internal server error")
        except:
            pass
    finally:
        # Clean up connection
        if user and connection_id:
            await notification_service.remove_connection(user.id, connection_id)

@router.get("/", response_model=List[NotificationResponse])
async def get_notifications(
    limit: int = Query(50, ge=1, le=100),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_db)
):
    """Get recent notifications for the current user"""
    notifications = await notification_service.get_user_notifications(
        user_id=current_user.id,
        organization_id=current_user.organization_id,
        limit=limit
    )
    return notifications

@router.post("/mark-read/{notification_id}")
async def mark_notification_read(
    notification_id: str,
    current_user: User = Depends(get_current_user)
):
    """Mark a notification as read"""
    await notification_service.mark_notification_read(notification_id, current_user.id)
    return {"message": "Notification marked as read"}

@router.post("/test")
async def send_test_notification(
    current_user: User = Depends(get_current_user)
):
    """Send a test notification (for development/testing)"""
    test_notification = NotificationCreate(
        type=NotificationType.SYSTEM_ALERT,
        title="Test Notification",
        message="This is a test notification to verify the system is working",
        priority=NotificationPriority.NORMAL,
        organization_id=current_user.organization_id,
        team_id=current_user.team_id
    )
    
    await notification_service.publish_notification(test_notification)
    return {"message": "Test notification sent"}

# Helper function to send notifications from other parts of the app
async def send_new_message_notification(
    message_id: int,
    conversation_id: int,
    customer_id: int,
    customer_name: str,
    message_content: str,
    organization_id: int,
    team_id: Optional[int] = None
):
    """Helper function to send new message notifications"""
    from app.schemas.notification import create_new_message_notification
    
    notification = create_new_message_notification(
        message_id=message_id,
        conversation_id=conversation_id,
        customer_id=customer_id,
        customer_name=customer_name,
        message_content=message_content,
        organization_id=organization_id,
        team_id=team_id
    )
    
    await notification_service.publish_notification(notification)

async def send_new_conversation_notification(
    conversation_id: int,
    customer_id: int,
    customer_name: str,
    organization_id: int,
    team_id: Optional[int] = None
):
    """Helper function to send new conversation notifications"""
    from app.schemas.notification import create_new_conversation_notification
    
    notification = create_new_conversation_notification(
        conversation_id=conversation_id,
        customer_id=customer_id,
        customer_name=customer_name,
        organization_id=organization_id,
        team_id=team_id
    )
    
    await notification_service.publish_notification(notification)
