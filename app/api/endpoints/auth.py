from fastapi import <PERSON><PERSON>out<PERSON>, Depends, HTTPException, status, Response
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.session import get_async_db
from app.crud import crud_user
from app.core.security import verify_password
from app.auth.dependencies import serializer, SESSION_COOKIE_NAME, get_current_user
from app.models.user import User
from app.schemas.user import UserResponse

router = APIRouter()

@router.post("/login")
async def login(
    response: Response,
    db: AsyncSession = Depends(get_async_db),
    form_data: OAuth2PasswordRequestForm = Depends(),
):
    """
    Logs in a user and sets a session cookie.
    Expects form data with 'username' (email) and 'password'.
    """
    user = await crud_user.get_user_by_email(db, email=form_data.username)
    if not user or not verify_password(form_data.password, user.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Create session data (signed user_id)
    session_data = serializer.dumps(str(user.id))
    
    # Set the cookie in the response
    response.set_cookie(
        key=SESSION_COOKIE_NAME,
        value=session_data,
        httponly=True,       # Prevents client-side JS from accessing the cookie
        samesite="lax",      # CSRF protection
        secure=False,        # In production with HTTPS, set this to True
        max_age=86400,       # 1 day
    )
    return {"message": "Login successful"}

@router.post("/logout")
def logout(response: Response):
    """Logs out the user by deleting the session cookie."""
    response.delete_cookie(SESSION_COOKIE_NAME)
    return {"message": "Logout successful"}

@router.get("/me", response_model=UserResponse)
async def read_users_me(current_user: User = Depends(get_current_user)):
    """Returns the current authenticated user's information."""
    # Convert SQLAlchemy model to Pydantic model with proper role serialization
    user_data = {
        "id": current_user.id,
        "email": current_user.email,
        "full_name": current_user.full_name,
        "organization_id": current_user.organization_id,
        "team_id": current_user.team_id,
        "is_active": current_user.is_active,
        "created_at": current_user.created_at,
        "updated_at": current_user.updated_at,
        "roles": [
            {
                "id": role.id,
                "name": role.name,
                "description": role.description
            }
            for role in current_user.roles
        ]
    }
    return UserResponse(**user_data)
