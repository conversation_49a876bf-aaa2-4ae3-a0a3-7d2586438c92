from fastapi import APIRouter, Depends, HTTPException, status
from fastapi_cache.decorator import cache
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List

from app.db.session import get_async_db
from app.crud import crud_team
from app.schemas.team import TeamR<PERSON>ponse, TeamCreate, TeamUpdate
from app.models.user import User
from app.auth.dependencies import get_current_active_admin, get_current_active_agent

router = APIRouter()

@router.post("/", response_model=TeamResponse)
async def create_team(
    team: TeamCreate,
    db: AsyncSession = Depends(get_async_db),
    current_admin: User = Depends(get_current_active_admin),
):
    """Create a new team. Admin access required."""
    return await crud_team.create_team(db=db, team=team)

@router.get("/", response_model=List[TeamResponse])
@cache(expire=360)
async def list_teams(
    skip: int = 0,
    limit: int = 100,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_active_agent),
):
    """List all teams. Agent or Admin access required."""
    return await crud_team.get_teams(db=db, skip=skip, limit=limit)

@router.get("/organization/{organization_id}", response_model=List[TeamResponse])
async def list_teams_by_organization(
    organization_id: int,
    skip: int = 0,
    limit: int = 100,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_active_agent),
):
    """List teams by organization. Agent or Admin access required."""
    # Agents can only see teams from their organization
    if not current_user.has_role("Admin") and current_user.organization_id != organization_id:
        raise HTTPException(status_code=403, detail="Access denied")
    
    return await crud_team.get_teams_by_organization(db=db, organization_id=organization_id, skip=skip, limit=limit)

@router.get("/{team_id}", response_model=TeamResponse)
async def get_team(
    team_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_active_agent),
):
    """Get a specific team by ID. Agent or Admin access required."""
    team = await crud_team.get_team(db=db, team_id=team_id)
    if not team:
        raise HTTPException(status_code=404, detail="Team not found")
    return team

@router.put("/{team_id}", response_model=TeamResponse)
async def update_team(
    team_id: int,
    team_update: TeamUpdate,
    db: AsyncSession = Depends(get_async_db),
    current_admin: User = Depends(get_current_active_admin),
):
    """Update a team. Admin access required."""
    team = await crud_team.update_team(db=db, team_id=team_id, team_update=team_update)
    if not team:
        raise HTTPException(status_code=404, detail="Team not found")
    return team

@router.delete("/{team_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_team(
    team_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_admin: User = Depends(get_current_active_admin),
):
    """MODIFIED: Soft-delete a team. Admin access required."""
    team_to_delete = await crud_team.get_team(db, team_id=team_id)
    if not team_to_delete:
        raise HTTPException(status_code=404, detail="Team not found")
    
    await crud_team.soft_delete_team(db=db, team_id=team_id)
    return

@router.patch("/{team_id}/restore", response_model=TeamResponse)
async def restore_team_endpoint(
    team_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_admin: User = Depends(get_current_active_admin),
):
    """NEW: Restore a soft-deleted team. Admin access required."""
    restored_team = await crud_team.restore_team(db, team_id=team_id)
    if not restored_team:
        raise HTTPException(status_code=404, detail="Team not found or was not deleted.")
    return restored_team
