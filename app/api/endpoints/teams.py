from fastapi import APIRouter, Depends, HTTPException, status
from fastapi_cache.decorator import cache
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List

from app.db.session import get_async_db
from app.crud import crud_team
from app.schemas.team import TeamResponse, TeamCreate, TeamUpdate
from app.models.user import User
from app.auth.dependencies import require_read_teams, require_edit_teams, require_permission, team_resource_resolver

router = APIRouter()

@router.post("/", response_model=TeamResponse)
async def create_team(
    team: TeamCreate,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(require_edit_teams()),
):
    """Create a new team. Requires 'edit' permission on 'teams' resource."""
    return await crud_team.create_team(db=db, team=team)

@router.get("/", response_model=List[TeamResponse])
@cache(expire=360)
async def list_teams(
    skip: int = 0,
    limit: int = 100,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(require_read_teams()),
):
    """List all teams. Requires 'read' permission on 'teams' resource."""
    return await crud_team.get_teams(db=db, skip=skip, limit=limit)

@router.get("/organization/{organization_id}", response_model=List[TeamResponse])
async def list_teams_by_organization(
    organization_id: int,
    skip: int = 0,
    limit: int = 100,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(require_read_teams()),
):
    """List teams by organization. Requires 'read' permission on 'teams' resource."""
    # TODO: Add organization-specific permission check using Casbin
    return await crud_team.get_teams_by_organization(db=db, organization_id=organization_id, skip=skip, limit=limit)

@router.get("/{team_id}", response_model=TeamResponse)
async def get_team(
    team_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(require_permission("read", team_resource_resolver)),
):
    """Get a specific team by ID. Requires 'read' permission on specific team resource."""
    team = await crud_team.get_team(db=db, team_id=team_id)
    if not team:
        raise HTTPException(status_code=404, detail="Team not found")
    return team

@router.put("/{team_id}", response_model=TeamResponse)
async def update_team(
    team_id: int,
    team_update: TeamUpdate,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(require_permission("edit", team_resource_resolver)),
):
    """Update a team. Requires 'edit' permission on specific team resource."""
    team = await crud_team.update_team(db=db, team_id=team_id, team_update=team_update)
    if not team:
        raise HTTPException(status_code=404, detail="Team not found")
    return team

@router.delete("/{team_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_team(
    team_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(require_permission("delete", team_resource_resolver)),
):
    """Soft-delete a team. Requires 'delete' permission on specific team resource."""
    team_to_delete = await crud_team.get_team(db, team_id=team_id)
    if not team_to_delete:
        raise HTTPException(status_code=404, detail="Team not found")

    await crud_team.soft_delete_team(db=db, team_id=team_id)
    return

@router.patch("/{team_id}/restore", response_model=TeamResponse)
async def restore_team_endpoint(
    team_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(require_permission("edit", team_resource_resolver)),
):
    """Restore a soft-deleted team. Requires 'edit' permission on specific team resource."""
    restored_team = await crud_team.restore_team(db, team_id=team_id)
    if not restored_team:
        raise HTTPException(status_code=404, detail="Team not found or was not deleted.")
    return restored_team
