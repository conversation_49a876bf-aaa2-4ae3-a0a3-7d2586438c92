from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List

from app.db.session import get_async_db
from app.crud import crud_customer
from app.schemas.customer import CustomerResponse, CustomerCreate, CustomerUpdate
from app.models.user import User
from app.auth.dependencies import require_permission, customers_resource_resolver, get_current_user

router = APIRouter()

@router.post("/", response_model=CustomerResponse)
async def create_customer(
    customer: CustomerCreate,
    db: AsyncSession = Depends(get_async_db)
):
    """Create a new customer (public endpoint)."""
    existing_customer = await crud_customer.get_customer_by_customer_id(db=db, customer_id=customer.customer_id)
    if existing_customer:
        return existing_customer
    return await crud_customer.create_customer(db=db, customer=customer)

@router.get("/", response_model=List[CustomerResponse])
async def list_customers(
    skip: int = 0,
    limit: int = 100,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(require_permission("read", customers_resource_resolver)),
):
    """List all customers. Requires 'read' permission on 'customers' resource."""
    return await crud_customer.get_customers(db=db, skip=skip, limit=limit)

@router.get("/{customer_id}", response_model=CustomerResponse)
async def get_customer(
    customer_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user),
):
    """Get a specific customer by ID. Authenticated user required."""
    customer = await crud_customer.get_customer(db=db, customer_id=customer_id)
    if not customer:
        raise HTTPException(status_code=404, detail="Customer not found")
    return customer

@router.get("/by-customer-id/{customer_id}", response_model=CustomerResponse)
async def get_customer_by_customer_id(
    customer_id: str,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user),
):
    """Get a customer by their external customer_id. Authenticated user required."""
    customer = await crud_customer.get_customer_by_customer_id(db=db, customer_id=customer_id)
    if not customer:
        raise HTTPException(status_code=404, detail="Customer not found")
    return customer

@router.put("/{customer_id}", response_model=CustomerResponse)
async def update_customer(
    customer_id: int,
    customer_update: CustomerUpdate,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(require_permission("edit", customers_resource_resolver)),
):
    """Update a customer. Requires 'edit' permission on 'customers' resource."""
    customer = await crud_customer.update_customer(
        db=db, customer_id=customer_id, customer_update=customer_update
    )
    if not customer:
        raise HTTPException(status_code=404, detail="Customer not found")
    return customer

@router.delete("/{customer_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_customer(
    customer_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(require_permission("delete", customers_resource_resolver))
):
    """Soft-delete a customer. Requires 'delete' permission on 'customers' resource."""
    customer_to_delete = await crud_customer.get_customer(db, customer_id=customer_id)
    if not customer_to_delete:
        raise HTTPException(status_code=404, detail="Customer not found")

    await crud_customer.soft_delete_customer(db, customer_id=customer_id)
    return

@router.patch("/{customer_id}/restore", response_model=CustomerResponse)
async def restore_customer_endpoint(
    customer_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(require_permission("edit", customers_resource_resolver))
):
    """Restore a soft-deleted customer. Requires 'edit' permission on 'customers' resource."""
    restored_customer = await crud_customer.restore_customer(db, customer_id=customer_id)
    if not restored_customer:
        raise HTTPException(status_code=404, detail="Customer not found or was not deleted.")
    return restored_customer