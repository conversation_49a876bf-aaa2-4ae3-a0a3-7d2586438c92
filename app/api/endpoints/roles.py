from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional

from app.db.session import get_async_db
from app.crud.crud_role import crud_role
from app.crud.crud_user import get_user_with_roles, get_users_by_role_name
from app.schemas.role import (
    RoleCreate, RoleUpdate, RoleResponse, RoleWithUsers, 
    UserRoleAssignment, UserRoleResponse, BulkRoleAssignment, RoleStats
)
from app.schemas.user import UserResponse
from app.models.user import User
from app.auth.dependencies import require_admin, require_any_role, get_current_user

router = APIRouter()

@router.get("/", response_model=List[RoleResponse])
async def list_roles(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    active_only: bool = Query(True),
    include_system: bool = Query(True),
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(require_admin)
):
    """List all roles with filtering options"""
    roles = await crud_role.get_roles(
        db, 
        skip=skip, 
        limit=limit, 
        active_only=active_only, 
        include_system=include_system
    )
    return roles

@router.get("/stats", response_model=RoleStats)
async def get_role_statistics(
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(require_admin())
):
    """Get role statistics"""
    stats = await crud_role.get_role_stats(db)
    return stats

@router.get("/{role_id}", response_model=RoleResponse)
async def get_role(
    role_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(require_admin)
):
    """Get a specific role by ID"""
    role = await crud_role.get_role(db, role_id)
    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Role not found"
        )
    return role

@router.post("/", response_model=RoleResponse, status_code=status.HTTP_201_CREATED)
async def create_role(
    role: RoleCreate,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(require_admin())
):
    """Create a new role"""
    # Check if role name already exists
    existing_role = await crud_role.get_role_by_name(db, role.name)
    if existing_role:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Role with this name already exists"
        )
    
    return await crud_role.create_role(db, role)

@router.put("/{role_id}", response_model=RoleResponse)
async def update_role(
    role_id: int,
    role_update: RoleUpdate,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(require_admin())
):
    """Update a role"""
    # Check if role exists
    existing_role = await crud_role.get_role(db, role_id)
    if not existing_role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Role not found"
        )
    
    # Check if new name conflicts with existing role
    if role_update.name and role_update.name != existing_role.name:
        name_conflict = await crud_role.get_role_by_name(db, role_update.name)
        if name_conflict:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Role with this name already exists"
            )
    
    updated_role = await crud_role.update_role(db, role_id, role_update)
    return updated_role

@router.delete("/{role_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_role(
    role_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(require_admin())
):
    """Delete a role (only if not system role and no users assigned)"""
    success = await crud_role.delete_role(db, role_id)
    if not success:
        role = await crud_role.get_role(db, role_id)
        if not role:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Role not found"
            )
        elif role.is_system_role:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot delete system role"
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot delete role with assigned users"
            )

# User-Role Management Endpoints
@router.post("/assign", response_model=UserRoleResponse)
async def assign_roles_to_user(
    assignment: UserRoleAssignment,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(require_admin)
):
    """Assign roles to a user"""
    # Check if user exists
    user = await get_user_with_roles(db, assignment.user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    # Assign roles
    success = await crud_role.assign_roles_to_user(
        db, assignment.user_id, assignment.role_ids, replace_existing=False
    )
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Failed to assign roles"
        )
    
    # Return updated user with roles
    updated_user = await get_user_with_roles(db, assignment.user_id)
    return UserRoleResponse(user_id=updated_user.id, roles=updated_user.roles)

@router.post("/assign/bulk", status_code=status.HTTP_200_OK)
async def bulk_assign_roles(
    assignment: BulkRoleAssignment,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(require_admin())
):
    """Bulk assign roles to multiple users"""
    results = {"success": [], "failed": []}
    
    for user_id in assignment.user_ids:
        try:
            success = await crud_role.assign_roles_to_user(
                db, user_id, assignment.role_ids, assignment.replace_existing
            )
            if success:
                results["success"].append(user_id)
            else:
                results["failed"].append({"user_id": user_id, "reason": "Assignment failed"})
        except Exception as e:
            results["failed"].append({"user_id": user_id, "reason": str(e)})
    
    return results

@router.delete("/users/{user_id}/roles/{role_id}", status_code=status.HTTP_204_NO_CONTENT)
async def remove_role_from_user(
    user_id: int,
    role_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(require_admin)
):
    """Remove a specific role from a user"""
    success = await crud_role.remove_role_from_user(db, user_id, role_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User-role assignment not found"
        )

@router.get("/users/{user_id}/roles", response_model=List[RoleResponse])
async def get_user_roles(
    user_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user)
):
    """Get all roles for a specific user"""
    # Users can view their own roles, admins can view any user's roles
    if user_id != current_user.id and not current_user.has_role("Admin"):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to view this user's roles"
        )
    
    roles = await crud_role.get_user_roles(db, user_id)
    return roles

@router.get("/{role_id}/users", response_model=List[UserResponse])
async def get_users_with_role(
    role_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(require_admin)
):
    """Get all users with a specific role"""
    users = await crud_role.get_users_with_role(db, role_id)
    return users

@router.put("/users/{user_id}/roles", response_model=UserRoleResponse)
async def update_user_roles(
    user_id: int,
    role_ids: List[int],
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(require_admin)
):
    """Update all roles for a user (replace existing roles)"""
    # Check if user exists
    user = await get_user_with_roles(db, user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )

    # Replace all existing roles with new ones
    success = await crud_role.assign_roles_to_user(
        db, user_id, role_ids, replace_existing=True
    )

    if not success:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Failed to update user roles"
        )

    # Return updated user with roles
    updated_user = await get_user_with_roles(db, user_id)
    return UserRoleResponse(user_id=updated_user.id, roles=updated_user.roles)

@router.post("/initialize", response_model=List[RoleResponse])
async def initialize_default_roles(
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(require_admin())
):
    """Initialize default system roles"""
    created_roles = await crud_role.create_default_roles(db)
    return created_roles
