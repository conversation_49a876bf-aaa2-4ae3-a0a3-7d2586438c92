from fastapi import APIRouter, Depends, HTTPException, status
from fastapi_cache.decorator import cache
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List

from app.db.session import get_async_db
from app.crud import crud_user
from app.schemas.user import UserResponse, UserCreate, UserUpdate
from app.models.user import User, UserRole
from app.auth.dependencies import get_current_active_admin, get_current_active_agent

router = APIRouter()

@router.post("/", response_model=UserResponse)
async def create_new_user(
    user: UserCreate,
    db: AsyncSession = Depends(get_async_db),
    current_admin: User = Depends(get_current_active_admin),
):
    """Create a new user (for admins to create agents). Admin access required."""
    existing_user = await crud_user.get_user_by_email(db=db, email=user.email)
    if existing_user:
        raise HTTPException(status_code=400, detail="Email already registered")
    return await crud_user.create_user(db=db, user=user)

@router.get("/agents", response_model=List[UserResponse])
@cache(expire=60)
async def list_agents(
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_active_agent),
):
    """List all users with the 'agent' role. Agent or Admin access required."""
    return await crud_user.get_users_by_role(db=db, role=UserRole .agent)

@router.get("/admins", response_model=List[UserResponse])
@cache(expire=300)
async def list_admins(
    db: AsyncSession = Depends(get_async_db),
    current_admin: User = Depends(get_current_active_admin),
):
    """List all users with the 'admin' role. Admin access required."""
    return await crud_user.get_users_by_role(db=db, role=UserRole.admin)

@router.get("/", response_model=List[UserResponse])
@cache(expire=300)
async def list_all_users(
    db: AsyncSession = Depends(get_async_db),
    current_admin: User = Depends(get_current_active_admin),
):
    """List all users. Admin access required."""
    return await crud_user.get_all_users(db=db)

@router.get("/{user_id}", response_model=UserResponse)
@cache(expire=300)
async def get_user_by_id(
    user_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_active_agent),
):
    """Get a specific user by ID. Agent or Admin access required."""
    user = await crud_user.get_user(db=db, user_id=user_id)
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    return user

@router.put("/{user_id}", response_model=UserResponse)
async def update_user(
    user_id: int,
    user_update: UserUpdate,
    db: AsyncSession = Depends(get_async_db),
    current_admin: User = Depends(get_current_active_admin)
):
    """Update a user. Admin access required."""
    user = await crud_user.get_user(db, user_id=user_id)
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    updated_user = await crud_user.update_user(db, user_id=user_id, user_update=user_update)
    return updated_user

@router.patch("/{user_id}", response_model=UserResponse)
async def patch_user(
    user_id: int,
    user_update: UserUpdate,
    db: AsyncSession = Depends(get_async_db),
    current_admin: User = Depends(get_current_active_admin)
):
    """Partially update a user. Admin access required."""
    user = await crud_user.get_user(db, user_id=user_id)
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    updated_user = await crud_user.update_user(db, user_id=user_id, user_update=user_update)
    return updated_user

@router.delete("/{user_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_user(
    user_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_admin: User = Depends(get_current_active_admin)
):
    """Soft delete a user. Admin access required."""
    user = await crud_user.get_user(db, user_id=user_id)
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    await crud_user.soft_delete_user(db, user_id=user_id)
    return

@router.patch("/{user_id}/restore", response_model=UserResponse)
async def restore_user_endpoint(
    user_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_admin: User = Depends(get_current_active_admin)
):
    """Restore a soft-deleted user. Admin access required."""
    restored_user = await crud_user.restore_user(db, user_id=user_id)
    if not restored_user:
        raise HTTPException(status_code=404, detail="User not found or not deleted")
    return restored_user