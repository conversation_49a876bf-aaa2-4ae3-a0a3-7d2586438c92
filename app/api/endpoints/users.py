from fastapi import APIRouter, Depends, HTTPException, status
from fastapi_cache.decorator import cache
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List

from app.db.session import get_async_db
from app.crud import crud_user
from app.schemas.user import UserResponse, UserCreate, UserUpdate
from app.models.user import User
from app.auth.dependencies import (
    # Casbin-based dependencies
    require_create_users, require_read_users,
    require_permission, user_resource_resolver
)

router = APIRouter()

@router.post("/", response_model=UserResponse)
async def create_new_user(
    user: UserCreate,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(require_create_users()),
):
    """Create a new user. Requires 'create' permission on 'users' resource."""
    existing_user = await crud_user.get_user_by_email(db=db, email=user.email)
    if existing_user:
        raise HTTPException(status_code=400, detail="Email already registered")
    return await crud_user.create_user(db=db, user=user)

# REMOVED: Role-specific user listing endpoints.
# Use the main /users endpoint with appropriate Casbin permissions instead.

@router.get("/", response_model=List[UserResponse])
@cache(expire=300)
async def list_all_users(
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(require_read_users()),
):
    """List all users. Requires 'read' permission on 'users' resource."""
    return await crud_user.get_all_users(db=db)

@router.get("/{user_id}", response_model=UserResponse)
@cache(expire=300)
async def get_user_by_id(
    user_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(require_permission("read", user_resource_resolver)),
):
    """Get a specific user by ID. Requires 'read' permission on specific user resource."""
    user = await crud_user.get_user(db=db, user_id=user_id)
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    return user

@router.put("/{user_id}", response_model=UserResponse)
async def update_user(
    user_id: int,
    user_update: UserUpdate,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(require_permission("edit", user_resource_resolver))
):
    """Update a user. Requires 'edit' permission on specific user resource."""
    user = await crud_user.get_user(db, user_id=user_id)
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    updated_user = await crud_user.update_user(db, user_id=user_id, user_update=user_update)
    return updated_user

@router.patch("/{user_id}", response_model=UserResponse)
async def patch_user(
    user_id: int,
    user_update: UserUpdate,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(require_permission("edit", user_resource_resolver))
):
    """Partially update a user. Requires 'edit' permission on specific user resource."""
    user = await crud_user.get_user(db, user_id=user_id)
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    updated_user = await crud_user.update_user(db, user_id=user_id, user_update=user_update)
    return updated_user

@router.delete("/{user_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_user(
    user_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(require_permission("delete", user_resource_resolver))
):
    """Soft delete a user. Requires 'delete' permission on specific user resource."""
    user = await crud_user.get_user(db, user_id=user_id)
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    await crud_user.soft_delete_user(db, user_id=user_id)
    return

@router.patch("/{user_id}/restore", response_model=UserResponse)
async def restore_user_endpoint(
    user_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(require_permission("edit", user_resource_resolver))
):
    """Restore a soft-deleted user. Requires 'edit' permission on specific user resource."""
    restored_user = await crud_user.restore_user(db, user_id=user_id)
    if not restored_user:
        raise HTTPException(status_code=404, detail="User not found or not deleted")
    return restored_user