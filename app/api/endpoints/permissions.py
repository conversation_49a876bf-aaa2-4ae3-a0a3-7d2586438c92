from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Dict, Any
from pydantic import BaseModel

from app.db.session import get_async_db
from app.models.user import User
from app.auth.dependencies import get_current_user, require_permission, organizations_resource_resolver
from app.core.casbin import (
    get_enforcer, check_permission, get_user_permissions, 
    get_user_roles, reload_policy, add_user_roles
)

router = APIRouter()

# Pydantic models for API
class PermissionCheck(BaseModel):
    resource: str
    action: str

class PermissionResult(BaseModel):
    allowed: bool
    resource: str
    action: str
    user_email: str
    user_roles: List[str]

class UserPermissions(BaseModel):
    user_email: str
    roles: List[str]
    permissions: List[List[str]]

class PolicyRule(BaseModel):
    subject: str
    object: str
    action: str

# Only admins can manage permissions
async def require_admin_permission():
    """Simple admin check for permission management endpoints."""
    return require_permission("*", organizations_resource_resolver)

@router.post("/check", response_model=PermissionResult)
async def check_user_permission(
    permission_check: PermissionCheck,
    current_user: User = Depends(get_current_user),
):
    """Check if the current user has permission to perform an action on a resource."""
    # Sync user roles with Casbin
    user_roles = [role.name for role in current_user.roles] if current_user.roles else []
    add_user_roles(current_user.email, user_roles)
    
    # Check permission
    allowed = check_permission(
        current_user.email, 
        permission_check.resource, 
        permission_check.action
    )
    
    return PermissionResult(
        allowed=allowed,
        resource=permission_check.resource,
        action=permission_check.action,
        user_email=current_user.email,
        user_roles=user_roles
    )

@router.get("/me", response_model=UserPermissions)
async def get_my_permissions(
    current_user: User = Depends(get_current_user),
):
    """Get all permissions for the current user."""
    # Sync user roles with Casbin
    user_roles = [role.name for role in current_user.roles] if current_user.roles else []
    add_user_roles(current_user.email, user_roles)
    
    # Get permissions from Casbin
    casbin_roles = get_user_roles(current_user.email)
    permissions = get_user_permissions(current_user.email)
    
    return UserPermissions(
        user_email=current_user.email,
        roles=casbin_roles,
        permissions=permissions
    )

@router.get("/users/{user_email}/permissions", response_model=UserPermissions)
async def get_user_permissions_endpoint(
    user_email: str,
    current_user: User = Depends(require_admin_permission()),
):
    """Get all permissions for a specific user. Admin only."""
    casbin_roles = get_user_roles(user_email)
    permissions = get_user_permissions(user_email)
    
    return UserPermissions(
        user_email=user_email,
        roles=casbin_roles,
        permissions=permissions
    )

@router.get("/policy", response_model=List[List[str]])
async def get_policy(
    current_user: User = Depends(require_admin_permission()),
):
    """Get all policy rules. Admin only."""
    enforcer = get_enforcer()
    return enforcer.get_policy()

@router.post("/policy/reload")
async def reload_policy_endpoint(
    current_user: User = Depends(require_admin_permission()),
):
    """Reload the policy from file. Admin only."""
    reload_policy()
    return {"message": "Policy reloaded successfully"}

@router.get("/roles", response_model=List[str])
async def get_all_roles(
    current_user: User = Depends(get_current_user),
):
    """Get all available roles from the policy."""
    enforcer = get_enforcer()
    policy = enforcer.get_policy()
    
    # Extract unique roles from policy
    roles = set()
    for rule in policy:
        if len(rule) >= 1:
            roles.add(rule[0])  # Subject (role) is the first element
    
    return sorted(list(roles))

@router.get("/resources", response_model=List[str])
async def get_all_resources(
    current_user: User = Depends(get_current_user),
):
    """Get all available resources from the policy."""
    enforcer = get_enforcer()
    policy = enforcer.get_policy()
    
    # Extract unique resources from policy
    resources = set()
    for rule in policy:
        if len(rule) >= 2:
            resources.add(rule[1])  # Object (resource) is the second element
    
    return sorted(list(resources))

@router.get("/actions", response_model=List[str])
async def get_all_actions(
    current_user: User = Depends(get_current_user),
):
    """Get all available actions from the policy."""
    enforcer = get_enforcer()
    policy = enforcer.get_policy()
    
    # Extract unique actions from policy
    actions = set()
    for rule in policy:
        if len(rule) >= 3:
            actions.add(rule[2])  # Action is the third element
    
    return sorted(list(actions))

@router.get("/matrix", response_model=Dict[str, Any])
async def get_permission_matrix(
    current_user: User = Depends(require_admin_permission()),
):
    """Get a complete permission matrix showing what each role can do. Admin only."""
    enforcer = get_enforcer()
    policy = enforcer.get_policy()
    
    # Build permission matrix
    matrix = {}
    for rule in policy:
        if len(rule) >= 3:
            role, resource, action = rule[0], rule[1], rule[2]
            if role not in matrix:
                matrix[role] = {}
            if resource not in matrix[role]:
                matrix[role][resource] = []
            matrix[role][resource].append(action)
    
    return {
        "permission_matrix": matrix,
        "total_rules": len(policy),
        "roles": len(matrix.keys()),
        "description": "Shows what actions each role can perform on each resource"
    }
