from sqlalchemy import <PERSON>umn, Integer, String, Table, Foreign<PERSON>ey, DateTime, Boolean
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.db.base import Base

# Association table for the many-to-many relationship between users and roles
user_role_association = Table(
    'user_role_association', 
    Base.metadata,
    Column('user_id', Integer, Foreign<PERSON>ey('users.id'), primary_key=True),
    Column('role_id', Integer, ForeignKey('roles.id'), primary_key=True),
    Column('assigned_at', DateTime(timezone=True), server_default=func.now()),
    Column('assigned_by', Integer, Foreign<PERSON>ey('users.id'), nullable=True)
)

class Role(Base):
    __tablename__ = "roles"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(50), unique=True, index=True, nullable=False)
    description = Column(String(255), nullable=True)
    is_active = Column(Boolean, default=True, nullable=False)
    is_system_role = Column(Boolean, default=False, nullable=False)  # Prevents deletion of core roles
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Many-to-many relationship with User
    users = relationship(
        "User",
        secondary=user_role_association,
        back_populates="roles",
        foreign_keys=[user_role_association.c.user_id, user_role_association.c.role_id]
    )

    def __repr__(self):
        return f"<Role(id={self.id}, name='{self.name}')>"
