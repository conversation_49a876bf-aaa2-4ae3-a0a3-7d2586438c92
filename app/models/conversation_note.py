from sqlalchemy import Column, Integer, String, DateTime, Text, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from uuid_extensions import uuid7

from app.db.base import Base

class ConversationNote(Base):
    __tablename__ = "conversation_notes"

    id = Column(UUID, primary_key=True, default=uuid7, index=True)
    content = Column(Text, nullable=False)
    
    conversation_id = Column(UUID, ForeignKey("conversations.id"), nullable=False, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True) # Agent who added the note
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    conversation = relationship("Conversation")
    user = relationship("User")
