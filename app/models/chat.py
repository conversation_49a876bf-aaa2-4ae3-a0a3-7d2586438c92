import enum
from sqlalchemy import <PERSON>umn, Integer, String, Text, DateTime, Foreign<PERSON>ey, Boolean, Enum as SQLAlchemyEnum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.db.base import Base

# New Enum for conversation status
class ConversationStatus(str, enum.Enum):
    new = "new"
    open = "open"
    closed = "closed"

class Conversation(Base):
    __tablename__ = "conversations"

    id = Column(Integer, primary_key=True, index=True)
    customer_id = Column(Integer, ForeignKey("customers.id"), nullable=False, index=True)
    organization_id = Column(Integer, ForeignKey("organizations.id"), nullable=False, index=True)
    status = Column(SQLAlchemyEnum(ConversationStatus), default=ConversationStatus.new, nullable=False, index=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Changed from assigned_agent_id to assigned_team_id
    assigned_team_id = Column(Integer, Foreign<PERSON>ey("teams.id"), nullable=True, index=True)

    # Relationships
    customer = relationship("Customer", back_populates="conversations")
    organization = relationship("Organization", back_populates="conversations")
    assigned_team = relationship("Team", back_populates="conversations")
    messages = relationship("Message", back_populates="conversation", cascade="all, delete-orphan")

class Message(Base):
    __tablename__ = "messages"

    id = Column(Integer, primary_key=True, index=True)
    conversation_id = Column(Integer, ForeignKey("conversations.id"), nullable=False, index=True)
    customer_id = Column(Integer, ForeignKey("customers.id"), nullable=True, index=True)  # For customer messages
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True, index=True)  # For agent/admin messages
    content = Column(Text, nullable=True)  # Made nullable for media messages
    sender = Column(String(50), nullable=False)  # 'customer', 'bot', 'agent', 'admin'
    message_type = Column(String(50), default="text")  # text, image, video, audio, gif, document
    asset_id = Column(Integer, ForeignKey("assets.id"), nullable=True, index=True)  # For media messages
    ip_address = Column(String(45), nullable=True)  # Renamed from message_metadata
    location = Column(String(255), nullable=True)  # New location column
    deleted = Column(Boolean, default=False, nullable=False, index=True)  # Boolean field for message deletion
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    conversation = relationship("Conversation", back_populates="messages")
    customer = relationship("Customer", back_populates="messages")
    user = relationship("User", back_populates="messages")
    asset = relationship("Asset", back_populates="messages")
