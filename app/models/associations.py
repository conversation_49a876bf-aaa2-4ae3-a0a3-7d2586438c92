from sqlalchemy import Table, <PERSON>umn, Integer, Foreign<PERSON>ey
from app.db.base import Base

user_role_association = Table(
    "user_role_association",
    Base.metadata,
    Column("user_id", Integer, ForeignKey("users.id"), primary_key=True),
    Column("role_id", <PERSON>teger, Foreign<PERSON>ey("roles.id"), primary_key=True),
)

# NEW: Association table for Conversations and Labels
conversation_label_association = Table(
    'conversation_label_association',
    Base.metadata,
    Column('conversation_id', Integer, ForeignKey('conversations.id'), primary_key=True),
    Column('label_id', Integer, ForeignKey('labels.id'), primary_key=True)
) 