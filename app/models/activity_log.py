from sqlalchemy import Column, Integer, String, DateTime, Text, ForeignKey, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.db.base import Base

class ActivityLog(Base):
    __tablename__ = "activity_logs"

    id = Column(Integer, primary_key=True, index=True)
    
    # Who performed the action?
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True, index=True)
    user_email = Column(String, nullable=True) # Denormalized for easy viewing

    # What was the action?
    action = Column(String(255), nullable=False, index=True) # e.g., "user_create", "role_assign"
    
    # What was the result?
    status = Column(String(50), nullable=False, default="success") # "success" or "failure"

    # On what resource was the action performed?
    target_resource_type = Column(String(100), nullable=True, index=True) # e.g., "user", "role"
    target_resource_id = Column(Integer, nullable=True, index=True)
    
    # Additional context
    details = Column(JSON, nullable=True)
    ip_address = Column(String(45), nullable=True)
    user_agent = Column(Text, nullable=True) # <-- ADD THIS LINE

    # Timestamp
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationship
    user = relationship("User")
