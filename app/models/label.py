from sqlalchemy import Column, Integer, String, Foreign<PERSON>ey, DateTime, UniqueConstraint
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.db.base import Base
from .associations import conversation_label_association

class Label(Base):
    __tablename__ = "labels"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(50), nullable=False, index=True)
    description = Column(String(255), nullable=True)
    color = Column(String(7), nullable=False, default="#475569") # Hex color code, e.g., #RRGGBB
    
    # Labels are scoped to an organization
    organization_id = Column(Integer, ForeignKey("organizations.id"), nullable=False, index=True)

    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Ensure a label name is unique within an organization
    __table_args__ = (UniqueConstraint('name', 'organization_id', name='_organization_label_name_uc'),)

    # Relationships
    organization = relationship("Organization", back_populates="labels")
    conversations = relationship(
        "Conversation",
        secondary=conversation_label_association,
        back_populates="labels"
    )
