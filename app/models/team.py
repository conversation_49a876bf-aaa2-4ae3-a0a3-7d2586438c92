from sqlalchemy import Column, String, DateTime, Text, Boolean, ForeignKey, select, func
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.sql import func
from uuid_extensions import uuid7

from app.db.base import Base

class Team(Base):
    __tablename__ = "teams"

    id = Column(UUID, primary_key=True, default=uuid7, index=True)
    name = Column(String(255), nullable=False, index=True)
    description = Column(Text, nullable=True)
    organization_id = Column(UUID, ForeignKey("organizations.id"), nullable=False, index=True)
    is_active = Column(Boolean, default=True, nullable=False)
    is_deleted = Column(Boolean, default=False, nullable=False, index=True)
    deleted_at = Column(DateTime(timezone=True), nullable=True)

    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    organization = relationship("Organization", back_populates="teams", foreign_keys=[organization_id])
    

    users = relationship("User", secondary="user_team_association", back_populates="teams")
    conversations = relationship("Conversation", back_populates="assigned_team")

    @hybrid_property
    def users_count(self):
        return len(self.users)

    @users_count.expression
    def users_count(cls):
        from app.models.user import User # Import here to avoid circular dependency
        return select(func.count(User.id)).where(User.team_id == cls.id).label("users_count")