from typing import Optional
from sqlalchemy import Column, String, Foreign<PERSON>ey, Boolean, DateTime, Table
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from uuid_extensions import uuid7
from app.db.base import Base

class User(Base):
    __tablename__ = "users"

    id = Column(UUID, primary_key=True, default=uuid7, index=True)

    full_name = Column(String(255), index=True)
    email = Column(String(255), unique=True, index=True, nullable=False)
    hashed_password = Column(String(255), nullable=False)

    company_id = Column(UUID, ForeignKey("organizations.id"), nullable=True, index=True)
    team_id = Column(UUID, ForeignKey("teams.id"), index=True, nullable=True)

    role_id = Column(UUID, ForeignKey("roles.id"), nullable=True, index=True)

    is_active = Column(Boolean, default=True, nullable=False)
    is_deleted = Column(Boolean, default=False, nullable=False, index=True)
    deleted_at = Column(DateTime(timezone=True), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # relationship
    role = relationship("Role", back_populates="users")
    organization = relationship("Organization", foreign_keys=[company_id], back_populates="users")
    team = relationship("Team", back_populates="users")
    messages = relationship("Message", back_populates="user")
    canned_responses = relationship("CannedResponse", back_populates="creator")
    profile_image_id = Column(UUID, ForeignKey("assets.id", ondelete="SET NULL"), nullable=True)
    profile_image = relationship("Asset", foreign_keys=[profile_image_id])
    owned_assets = relationship("Asset", back_populates="owner", foreign_keys="Asset.owner_id")
    
    @property
    def role_name(self) -> str:
        """Get the user's role name, default to 'Agent' if no role assigned."""
        if self.role:
            return self.role.name
        return "Agent"  # Default role

    def has_role(self, role_name: str) -> bool:
        """Check if the user has a specific role."""
        return self.role_name == role_name

    def is_agent_or_above(self) -> bool:
        """Check if user can handle customer conversations (Agent, Admin, HR, Manager, etc.)"""
        # Any user with a role can handle customer conversations
        return self.role is not None or self.role_name == "Agent"
