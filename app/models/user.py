import enum
from sqlalchemy import <PERSON>um<PERSON>, <PERSON>te<PERSON>, String, Enum as SQLAlchemyEnum, Foreign<PERSON>ey, Boolean
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from sqlalchemy import DateTime

from app.db.base import Base
from app.models.role import user_role_association

# Keep the old enum for backward compatibility during migration
class UserRole(str, enum.Enum):
    agent = "agent"  # All users are agents
    admin = "admin"  # Some agents have admin privileges

class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    full_name = Column(String(255), index=True)
    email = Column(String(255), unique=True, index=True, nullable=False)
    hashed_password = Column(String(255), nullable=False)

    # Keep old fields for backward compatibility during migration
    role = Column(SQLAlchemyEnum(UserRole), nullable=False, default=UserRole.agent)
    is_admin = Column(Boolean, default=False, nullable=False, index=True)

    organization_id = Column(Integer, ForeignKey("organizations.id"), index=True, nullable=True)
    team_id = Column(Integer, ForeignKey("teams.id"), index=True, nullable=True)
    is_active = Column(Boolean, default=True, nullable=False)
    is_deleted = Column(Boolean, default=False, nullable=False, index=True)
    deleted_at = Column(DateTime(timezone=True), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # NEW: Many-to-many relationship with Role
    roles = relationship(
        "Role",
        secondary=user_role_association,
        back_populates="users",
        lazy="select",  # Standard lazy loading
        foreign_keys=[user_role_association.c.user_id, user_role_association.c.role_id]
    )

    # Relationships
    organization = relationship("Organization", back_populates="users")
    team = relationship("Team", back_populates="users")
    messages = relationship("Message", back_populates="user")

    def has_role(self, role_name: str) -> bool:
        """Check if user has a specific role"""
        return any(role.name == role_name for role in self.roles)

    def get_role_names(self) -> set[str]:
        """Get all role names for this user"""
        return {role.name for role in self.roles}

    def is_admin_user(self) -> bool:
        """Check if user has admin role (new RBAC way)"""
        return self.has_role("Admin") or self.is_admin  # Backward compatibility
