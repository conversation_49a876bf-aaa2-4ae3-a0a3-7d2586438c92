from typing import Optional
from sqlalchemy import Column, <PERSON>te<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Boolean, DateTime, Table
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.db.base import Base
from app.models.associations import user_role_association

class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    full_name = Column(String(255), index=True)
    email = Column(String(255), unique=True, index=True, nullable=False)
    hashed_password = Column(String(255), nullable=False)
    
    company_id = Column(Integer, ForeignKey("organizations.id"), nullable=True, index=True)
    team_id = Column(Integer, ForeignKey("teams.id"), index=True, nullable=True)
    is_active = Column(Boolean, default=True, nullable=False)
    is_deleted = Column(Boolean, default=False, nullable=False, index=True)
    deleted_at = Column(DateTime(timezone=True), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    roles = relationship(
        "Role",
        secondary=user_role_association,
        back_populates="users",
        lazy="selectin"
    )
    organization = relationship("Organization", foreign_keys=[company_id], back_populates="users")
    team = relationship("Team", back_populates="users")
    messages = relationship("Message", back_populates="user")
    canned_responses = relationship("CannedResponse", back_populates="creator")
    profile_image_id = Column(Integer, ForeignKey("assets.id", ondelete="SET NULL"), nullable=True)
    profile_image = relationship("Asset", foreign_keys=[profile_image_id])
    owned_assets = relationship("Asset", back_populates="owner", foreign_keys="Asset.owner_id")
    
    @property
    def get_role_names(self):
        return [role.name for role in self.roles]

    def has_role(self, role_name: str) -> bool:
        """Check if the user has a specific role."""
        return role_name in self.get_role_names
