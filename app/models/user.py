from typing import Optional, List
from sqlalchemy import Column, String, Foreign<PERSON>ey, Boolean, DateTime, Table
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from uuid_extensions import uuid7
from app.db.base import Base

# Association tables for many-to-many relationships
user_organization_association = Table(
    'user_organization_association',
    Base.metadata,
    Column('user_id', UUID, ForeignKey('users.id'), primary_key=True),
    Column('organization_id', UUID, ForeignKey('organizations.id'), primary_key=True)
)

user_team_association = Table(
    'user_team_association',
    Base.metadata,
    Column('user_id', UUID, ForeignKey('users.id'), primary_key=True),
    Column('team_id', UUID, ForeignKey('teams.id'), primary_key=True)
)

class User(Base):
    __tablename__ = "users"

    id = Column(UUID, primary_key=True, default=uuid7, index=True)

    full_name = Column(String(255), index=True)
    email = Column(String(255), unique=True, index=True, nullable=False)
    hashed_password = Column(String(255), nullable=False)

    # Single role per user
    role_id = Column(UUID, ForeignKey("roles.id"), nullable=True, index=True)

    is_active = Column(Boolean, default=True, nullable=False)
    is_deleted = Column(Boolean, default=False, nullable=False, index=True)
    deleted_at = Column(DateTime(timezone=True), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    profile_image_id = Column(UUID, ForeignKey("assets.id", ondelete="SET NULL"), nullable=True)

    # Relationships
    role = relationship("Role", back_populates="users")

    # Many-to-many relationships
    organizations = relationship(
        "Organization",
        secondary=user_organization_association,
        back_populates="users"
    )
    teams = relationship(
        "Team",
        secondary=user_team_association,
        back_populates="users"
    )

    # Other relationships
    messages = relationship("Message", back_populates="user", foreign_keys="Message.user_id")

    canned_responses = relationship("CannedResponse", back_populates="creator")
    profile_image = relationship("Asset", foreign_keys=[profile_image_id])
    owned_assets = relationship("Asset", back_populates="owner", foreign_keys="Asset.owner_id")
    
    # Explicitly define the foreign key for messages READ by the user.
    read_messages = relationship("Message", back_populates="read_by", foreign_keys="Message.read_by_user_id")
    push_subscriptions = relationship("PushSubscription", back_populates="user", cascade="all, delete-orphan")
    
    @property
    def role_name(self) -> str:
        """Get the user's role name, default to 'Agent' if no role assigned."""
        if self.role:
            return self.role.name
        return "Agent"  # Default role

    def has_role(self, role_name: str) -> bool:
        """Check if the user has a specific role. SuperAdmin has all roles."""
        # SuperAdmin has access to everything
        if self.role_name == "SuperAdmin":
            return True
        return self.role_name == role_name

    def is_agent_or_above(self) -> bool:
        """Check if user can handle customer conversations (Agent, Admin, HR, Manager, etc.)"""
        # SuperAdmin can handle everything
        if self.role_name == "SuperAdmin":
            return True
        # Any user with a role can handle customer conversations
        return self.role is not None or self.role_name == "Agent"

    def is_admin_or_above(self) -> bool:
        """Check if user has admin-level permissions."""
        return self.role_name in ["SuperAdmin", "Admin"]

    def is_manager_or_above(self) -> bool:
        """Check if user has manager-level permissions."""
        return self.role_name in ["SuperAdmin", "Admin", "Manager"]

    @property
    def organization_ids(self) -> List[str]:
        """Get list of organization IDs the user belongs to."""
        return [str(org.id) for org in self.organizations]

    @property
    def team_ids(self) -> List[str]:
        """Get list of team IDs the user belongs to."""
        return [str(team.id) for team in self.teams]

    @property
    def primary_organization_id(self) -> Optional[str]:
        """Get the primary organization ID (first one if multiple)."""
        if self.organizations:
            return str(self.organizations[0].id)
        return None
