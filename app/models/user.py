import enum
from sqlalchemy import <PERSON><PERSON><PERSON>, Inte<PERSON>, String, Enum as SQLAlchemyEnum, ForeignKey, Boolean
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from sqlalchemy import DateTime

from app.db.base import Base

class UserRole(str, enum.Enum):
    admin = "admin"
    agent = "agent"

class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    full_name = Column(String(255), index=True)
    email = Column(String(255), unique=True, index=True, nullable=False)
    hashed_password = Column(String(255), nullable=False)
    role = Column(SQLAlchemyEnum(UserRole), nullable=False)
    organization_id = Column(Integer, ForeignKey("organizations.id"), index=True ,nullable=True)
    team_id = Column(Integer, ForeignKey("teams.id"), index=True ,nullable=True)
    is_active = Column(Boolean, default=True, nullable=False)
    is_deleted = Column(Boole<PERSON>, default=False, nullable=False, index=True)
    deleted_at = Column(DateTime(timezone=True), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    organization = relationship("Organization", back_populates="users")
    team = relationship("Team", back_populates="users")
    messages = relationship("Message", back_populates="user")
