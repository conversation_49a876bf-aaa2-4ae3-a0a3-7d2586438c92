from sqlalchemy import Column, Integer, String, DateTime, Text, Boolean, BigInteger
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from enum import Enum

from app.db.base import Base

class AssetType(str, Enum):
    image = "image"
    video = "video"
    audio = "audio"
    gif = "gif"
    document = "document"

class Asset(Base):
    __tablename__ = "assets"

    id = Column(Integer, primary_key=True, index=True)
    
    # File information
    filename = Column(String, nullable=False)
    original_filename = Column(String, nullable=False)
    file_type = Column(String, nullable=False)  # image, video, audio, gif, document
    mime_type = Column(String, nullable=False)  # image/jpeg, video/mp4, etc.
    file_size = Column(BigInteger, nullable=False)  # Size in bytes
    
    # S3/MinIO information
    s3_bucket = Column(String, nullable=False)
    s3_key = Column(String, nullable=False)  # Path in S3
    s3_url = Column(Text, nullable=False)  # Full S3 URL
    
    # Optional metadata
    width = Column(Integer, nullable=True)  # For images/videos
    height = Column(Integer, nullable=True)  # For images/videos
    duration = Column(Integer, nullable=True)  # For videos/audio (in seconds)
    thumbnail_s3_key = Column(String, nullable=True)  # Thumbnail for videos
    thumbnail_s3_url = Column(Text, nullable=True)  # Thumbnail URL
    
    # Status and metadata
    is_processed = Column(Boolean, default=False)  # For video processing, etc.
    file_metadata = Column(Text, nullable=True)  # JSON metadata (renamed to avoid conflict)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    messages = relationship("Message", back_populates="asset")
