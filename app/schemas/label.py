from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime

class LabelBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=50)
    description: Optional[str] = Field(None, max_length=255)
    color: str = Field("#475569", pattern=r"^#[0-9a-fA-F]{6}$")

class LabelCreate(LabelBase):
    pass

class LabelUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=50)
    description: Optional[str] = None
    color: Optional[str] = Field(None, pattern=r"^#[0-9a-fA-F]{6}$")

class LabelResponse(LabelBase):
    id: int
    organization_id: int
    created_at: datetime

    class Config:
        from_attributes = True

class ConversationLabelRequest(BaseModel):
    label_ids: List[int] = Field(..., description="List of label IDs to attach to the conversation.")