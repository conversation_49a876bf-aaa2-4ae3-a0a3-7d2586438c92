from pydantic import BaseModel, EmailStr
from typing import Optional
from datetime import datetime

# Shared properties
class UserBase(BaseModel):
    email: EmailStr
    full_name: Optional[str] = None
    organization_id: Optional[int] = None
    team_id: Optional[int] = None
    is_active: bool = True

# Properties to receive via API on creation
class UserCreate(UserBase):
    password: str
    # REMOVED: role_names is no longer needed here.
    # Role assignment is now a separate step managed by an Admin via the policy.

# Properties to receive via API on update
class UserUpdate(BaseModel):
    full_name: Optional[str] = None
    email: Optional[EmailStr] = None
    team_id: Optional[int] = None
    is_active: Optional[bool] = None
    # Role updates are now handled by the dedicated /api/roles/assign endpoint

# Properties to return to client
class UserResponse(UserBase):
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    # REMOVED: roles list is gone. The frontend should check permissions
    # by trying to access endpoints, not by checking a user's roles.

    class Config:
        from_attributes = True

# For backward compatibility
User = UserResponse
