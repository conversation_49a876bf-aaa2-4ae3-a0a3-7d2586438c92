from pydantic import BaseModel
from datetime import datetime
from typing import Optional

class ConversationNoteBase(BaseModel):
    content: str

class ConversationNoteCreate(ConversationNoteBase):
    conversation_id: int
    user_id: int

class ConversationNoteUpdate(ConversationNoteBase):
    pass

from app.schemas.user import UserResponse # Import UserResponse

class ConversationNoteInDBBase(ConversationNoteBase):
    id: int
    conversation_id: int
    user_id: int
    user: Optional[UserResponse] = None # Include user details
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class ConversationNote(ConversationNoteInDBBase):
    pass
