from pydantic import BaseModel
from typing import Optional
from datetime import datetime

# Shared properties
class OrganizationBase(BaseModel):
    name: str
    description: Optional[str] = None
    website: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    address: Optional[str] = None
    is_active: bool = True
    default_team_id: Optional[int] = None

# Properties to receive via API on creation
class OrganizationCreate(OrganizationBase):
    pass

# Properties to receive via API on update
class OrganizationUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    website: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    address: Optional[str] = None
    is_active: Optional[bool] = None
    default_team_id: Optional[int] = None

# Properties to return to client
class OrganizationResponse(OrganizationBase):
    id: int
    teams_count: Optional[int] = None
    users_count: Optional[int] = None
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

# For backward compatibility
Organization = OrganizationResponse
