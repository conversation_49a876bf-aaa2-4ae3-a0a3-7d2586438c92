from pydantic import BaseModel
from typing import Optional, Dict, Any
from datetime import datetime

class ActivityLogCreate(BaseModel):
    user_id: Optional[int] = None
    user_email: Optional[str] = None
    action: str
    status: str = "success"
    target_resource_type: Optional[str] = None
    target_resource_id: Optional[int] = None
    details: Optional[Dict[str, Any]] = None
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None # <-- ADD THIS LINE

class ActivityLogResponse(ActivityLogCreate):
    id: int
    created_at: datetime

    class Config:
        from_attributes = True
