from pydantic import BaseModel
from datetime import datetime
from typing import List, Optional
from uuid import UUID
from app.schemas.asset import AssetResponse
from app.schemas.label import LabelResponse # NEW
from enum import Enum

# Import the new models and the User schema
from app.models.chat import ConversationStatus

class MessageSender(str, Enum):
    AGENT = "agent"
    CUSTOMER = "customer"
    BOT = "bot"

# Message schemas
class MessageBase(BaseModel):
    content: Optional[str] = None  # Made optional for media messages
    sender: str
    message_type: str = "text"  # text, image, video, audio, gif, document
    asset_id: Optional[UUID] = None  # For media messages
    ip_address: Optional[str] = None
    location: Optional[str] = None
    deleted: bool = False  # Boolean field for message deletion

class MessageCreate(MessageBase):
    conversation_id: UUID
    customer_id: Optional[UUID] = None
    user_id: Optional[UUID] = None

class MessageCreateRequest(MessageBase):
    """Schema for creating messages via API (conversation_id comes from URL path)"""
    pass

class MessageResponse(MessageBase):
    id: UUID
    conversation_id: UUID
    customer_id: Optional[UUID] = None
    user_id: Optional[UUID] = None
    created_at: datetime
    asset: Optional[AssetResponse] = None  # Include asset data for media messages

    class Config:
        from_attributes = True

# Conversation schemas
class ConversationBase(BaseModel):
    customer_id: UUID
    organization_id: UUID

class ConversationCreate(ConversationBase):
    pass

class ConversationResponse(ConversationBase):
    id: UUID
    status: ConversationStatus
    created_at: datetime
    updated_at: Optional[datetime] = None
    assigned_team_id: Optional[UUID] = None
    labels: List[LabelResponse] = [] # NEW

    class Config:
        from_attributes = True

class ConversationWithMessages(ConversationResponse):
    messages: List[MessageResponse] = []

    class Config:
        from_attributes = True

# WebSocket message schemas
class WebSocketMessage(BaseModel):
    type: str  # "message", "notification", etc.
    content: str
    sender: str
    timestamp: Optional[datetime] = None
    metadata: Optional[dict] = None
