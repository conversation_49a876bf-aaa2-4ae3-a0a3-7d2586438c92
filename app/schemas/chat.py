from pydantic import BaseModel
from datetime import datetime
from typing import List, Optional
from app.schemas.asset import AssetResponse

# Import the new models and the User schema
from app.models.chat import ConversationStatus

# Message schemas
class MessageBase(BaseModel):
    content: Optional[str] = None  # Made optional for media messages
    sender: str
    message_type: str = "text"  # text, image, video, audio, gif, document
    asset_id: Optional[int] = None  # For media messages
    ip_address: Optional[str] = None
    location: Optional[str] = None
    deleted: bool = False  # Boolean field for message deletion

class MessageCreate(MessageBase):
    conversation_id: int
    customer_id: Optional[int] = None
    user_id: Optional[int] = None

class MessageResponse(MessageBase):
    id: int
    conversation_id: int
    customer_id: Optional[int] = None
    user_id: Optional[int] = None
    created_at: datetime
    asset: Optional[AssetResponse] = None  # Include asset data for media messages

    class Config:
        from_attributes = True

# Conversation schemas
class ConversationBase(BaseModel):
    customer_id: int
    organization_id: int

class ConversationCreate(ConversationBase):
    pass

class ConversationResponse(ConversationBase):
    id: int
    status: ConversationStatus
    created_at: datetime
    updated_at: Optional[datetime] = None
    assigned_team_id: Optional[int] = None

    class Config:
        from_attributes = True

class ConversationWithMessages(ConversationResponse):
    messages: List[MessageResponse] = []

    class Config:
        from_attributes = True

# WebSocket message schemas
class WebSocketMessage(BaseModel):
    type: str  # "message", "notification", etc.
    content: str
    sender: str
    timestamp: Optional[datetime] = None
    metadata: Optional[dict] = None
