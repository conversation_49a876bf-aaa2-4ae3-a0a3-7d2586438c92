from pydantic import BaseModel
from datetime import datetime
from typing import Optional

class NoteBase(BaseModel):
    content: str

class NoteCreate(NoteBase):
    customer_id: int
    user_id: int

class NoteUpdate(NoteBase):
    pass

class NoteInDBBase(NoteBase):
    id: int
    customer_id: int
    user_id: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class Note(NoteInDBBase):
    pass
