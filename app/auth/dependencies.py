from fastapi import Depends, HTTPException, status, Cookie, Request
from sqlalchemy.ext.asyncio import AsyncSession
from itsdangerous import URLSafeTimedSerializer, SignatureExpired, BadTimeSignature
from typing import Optional, Callable, Dict, Any
import logging
from typing import Any
import casbin

from app.core.config import settings
from app.core.permissions import enforcer, get_enforcer
from app.db.session import get_async_db
from app.crud import crud_asset, crud_canned_response, crud_conversation_note, crud_label, crud_note, crud_user, crud_chat, crud_team
from app.crud.crud_role import crud_role
from app.models.user import User

SESSION_COOKIE_NAME = settings.SESSION_COOKIE_NAME
serializer = URLSafeTimedSerializer(settings.SECRET_KEY)
logger = logging.getLogger(__name__)

async def get_current_user(
    session_cookie: Optional[str] = <PERSON><PERSON>(None, alias=SESSION_COOKIE_NAME),
    db: AsyncSession = Depends(get_async_db),
) -> User:
    """Dependency to get the current, active, non-deleted user from the session cookie."""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
    )
    if not session_cookie:
        raise credentials_exception
    try:
        user_id = int(serializer.loads(session_cookie, max_age=86400))
    except (SignatureExpired, BadTimeSignature, ValueError):
        raise credentials_exception

    user = await crud_user.get_user_with_roles(db, user_id=user_id)
    
    if not user or not user.is_active or user.is_deleted:
        raise credentials_exception
        
    return user

async def get_resource(request: Request, db: AsyncSession) -> Any:
    """A helper dependency to extract a resource object from the request path."""
    resource_map = {
        "conversations": (crud_chat.get_conversation, "conversation_id"),
        "messages": (crud_chat.get_message, "message_id"),
        "users": (crud_user.get_user, "user_id"),
        "teams": (crud_team.get_team, "team_id"),
        "roles": (crud_role.get_role, "role_id"),
        "labels": (crud_label.get_label, "label_id"),
        "canned_responses": (crud_canned_response, "response_id"),
        "notes": (crud_note.get_note, "note_id"),
        "conversation_notes": (crud_conversation_note.get_conversation_note, "note_id"),
        "assets": (crud_asset, "asset_id"),
    }
    
    for resource_name, (crud_instance, id_key) in resource_map.items():
        if id_key in request.path_params:
            resource_id = int(request.path_params[id_key])
            if resource_name == "canned_responses":
                resource = await crud_instance.get_canned_response(db, response_id=resource_id)
            elif resource_name == "assets":
                resource = await crud_instance.get_asset(db, asset_id=resource_id)
            else:
                resource = await crud_instance(db, id=resource_id) # Use generic 'id'
            if not resource:
                raise HTTPException(status_code=404, detail=f"{resource_name.capitalize()} not found")
            request.state.resource = resource
            return resource
    return None

def require_permission(action: str):
    """
    Powerful ABAC dependency. Ensures the current user has permission
    to perform an action. It checks for permissions hierarchically.
    """
    async def permission_checker(
        request: Request,
        db: AsyncSession = Depends(get_async_db),
        current_user: User = Depends(get_current_user),
        enforcer: casbin.Enforcer = Depends(get_enforcer)
    ):
        request.state.user = current_user
        resource = await get_resource(request, db)
        user_roles = [role.name for role in current_user.roles]

        if not user_roles:
            raise HTTPException(status_code=403, detail="Access Denied: No roles.")

        # --- REFINED LOGIC ---
        # 1. Check for permission on the specific resource first, if it exists.
        # Example: Can user 'read' 'conversation:123'?
        if resource:
            # e.g., obj = "company:1:conversation:123"
            obj_specific = f"company:{current_user.company_id}:{resource.__tablename__[:-1]}:{resource.id}"
            for role in user_roles:
                if enforcer.enforce(role, obj_specific, action):
                    return # SUCCESS: Specific permission granted

        # 2. If no specific permission, check for a general permission on the resource type.
        # Example: Can user 'create' 'conversations'?
        resource_type = request.url.path.split('/')[3] # /api/v1/users -> users
        obj_general = f"company:{current_user.company_id}:{resource_type}"
        for role in user_roles:
            if enforcer.enforce(role, obj_general, action):
                return # SUCCESS: General permission granted
        
        # 3. If all checks fail, deny access.
        raise HTTPException(status_code=403, detail="Permission Denied")

    return permission_checker

# Convenience dependencies for common roles
def require_admin():
    """Convenience dependency for Admin role"""
    return require_role("Admin")

def require_agent():
    """Convenience dependency for Agent role"""
    return require_role("Agent")

def require_role(required_role: str):
    """
    Dependency generator that checks if the current user has a specific role.
    This is the new, flexible way to protect endpoints.

    Usage:
        @router.get("/admin-only", dependencies=[Depends(require_role("Admin"))])
        or
        @router.get("/admin-only")
        async def admin_endpoint(current_user: User = Depends(require_role("Admin"))):
    """
    async def role_checker(current_user: User = Depends(get_current_user)) -> User:
        if not current_user.has_role(required_role):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Access denied. Required role: '{required_role}'",
            )
        return current_user
    return role_checker

def require_any_role(*required_roles: str):
    """
    Dependency generator that checks if the current user has ANY of the specified roles.

    Usage:
        @router.get("/admin-only")
        async def endpoint(current_user: User = Depends(require_any_role("Admin"))):
    """
    async def role_checker(current_user: User = Depends(get_current_user)) -> User:
        user_roles = current_user.get_role_names
        if not any(role in user_roles for role in required_roles):
            roles_str = "', '".join(required_roles)
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Access denied. Required roles: '{roles_str}'",
            )
        return current_user
    return role_checker

def require_all_roles(*required_roles: str):
    """
    Dependency generator that checks if the current user has ALL of the specified roles.

    Usage:
        @router.get("/super-admin")
        async def endpoint(current_user: User = Depends(require_all_roles("Admin", "Agent"))):
    """
    async def role_checker(current_user: User = Depends(get_current_user)) -> User:
        user_roles = current_user.get_role_names
        if not all(role in user_roles for role in required_roles):
            roles_str = "', '".join(required_roles)
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Access denied. Required roles: '{roles_str}'",
            )
        return current_user
    return role_checker
