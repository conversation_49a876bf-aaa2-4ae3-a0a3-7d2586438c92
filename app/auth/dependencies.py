from fastapi import Depends, HTTPException, status, <PERSON>ie, Path
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload
from itsdangerous import URLSafeTimedSerializer, SignatureExpired, BadTimeSignature
from typing import Optional, Union, List, Dict, Callable
import logging

from app.core.config import settings
from app.db.session import get_async_db
from app.crud import crud_user
from app.models.user import User
from app.core.casbin import get_enforcer, check_permission

SESSION_COOKIE_NAME = settings.SESSION_COOKIE_NAME
serializer = URLSafeTimedSerializer(settings.SECRET_KEY)

async def get_current_user(
    session_cookie: Optional[str] = <PERSON><PERSON>(None, alias=SESSION_COOKIE_NAME),
    db: AsyncSession = Depends(get_async_db),
) -> User:
    """
    Dependency to get the current, active, non-deleted user from the session cookie.
    This is the single source of truth for user authentication.
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    if session_cookie is None:
        raise credentials_exception
        
    try:
        user_id = int(serializer.loads(session_cookie, max_age=86400))
    except (SignatureExpired, BadTimeSignature, ValueError):
        raise credentials_exception

    # Fetch user with roles, including deleted ones, to provide specific feedback
    user = await crud_user.get_user_with_roles(db, user_id=user_id, include_deleted=True)

    if user is None:
        raise credentials_exception

    # Additional checks for active and non-deleted status
    if not user.is_active:
        raise HTTPException(status_code=400, detail="Inactive user")
    if user.is_deleted:
        raise HTTPException(status_code=400, detail="User account has been deleted")

    return user

# REMOVED: All legacy role-based dependencies.
# Authorization is now handled entirely by Casbin-based require_permission() dependencies.

async def get_current_user_from_websocket_token(
    token: str,
    db: AsyncSession
) -> Optional[User]:
    """Get current user from WebSocket token parameter"""
    try:
        # Decode the session token
        user_id = serializer.loads(token, max_age=86400)  # 24 hours

        # Get user from database
        user = await crud_user.get_user(db, user_id=user_id)

        if not user:
            return None

        if not user.is_active:
            return None

        if user.is_deleted:
            return None

        return user

    except Exception:
        return None

# ============================================================================
# CASBIN-BASED ATTRIBUTE-BASED ACCESS CONTROL (ABAC) DEPENDENCIES
# ============================================================================

logger = logging.getLogger(__name__)

def require_permission(action: str, resource_resolver: Callable):
    """
    The ultimate RBAC/ABAC dependency using Casbin.

    This replaces role-based checks with permission-based checks.
    Instead of checking "is user an admin?", we ask "can user perform action X on resource Y?"

    Args:
        action (str): The action to be performed (e.g., 'read', 'edit', 'delete').
        resource_resolver (callable): A function that takes route parameters
                                      and resolves them into a resource string
                                      (e.g., "organization:1").

    Usage:
        @router.get("/organizations/{organization_id}")
        async def get_org(
            current_user: User = Depends(require_permission("read", organization_resource_resolver))
        ):
    """
    async def permission_checker(
        request_params: Dict = Depends(resource_resolver),
        current_user: User = Depends(get_current_user),
    ) -> User:
        # Sync user roles with Casbin on each request
        await sync_user_roles_with_casbin(current_user)

        # The resource string, e.g., "organization:1"
        resource = request_params['resource']

        # Check permission using Casbin
        has_permission = check_permission(current_user.email, resource, action)

        if not has_permission:
            logger.warning(f"Permission denied: {current_user.email} cannot {action} on {resource}")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Access denied: Not enough permissions for action '{action}' on resource '{resource}'"
            )

        logger.debug(f"Permission granted: {current_user.email} can {action} on {resource}")
        return current_user

    return permission_checker

async def sync_user_roles_with_casbin(user: User):
    """Sync user roles from Casbin policy with enforcer."""
    try:
        # NOTE: User roles are now managed entirely in the Casbin policy file.
        # This function is kept for compatibility but doesn't need to do anything
        # since roles are already defined in the policy.csv file.
        pass
    except Exception as e:
        logger.error(f"Failed to sync user roles with Casbin: {e}")

# ============================================================================
# RESOURCE RESOLVERS
# ============================================================================
# These functions extract path parameters to build the resource string for Casbin.

async def organization_resource_resolver(organization_id: int = Path(...)):
    """Resolves the resource string for an organization."""
    return {"resource": f"organization:{organization_id}"}

async def team_resource_resolver(team_id: int = Path(...)):
    """Resolves the resource string for a team."""
    return {"resource": f"team:{team_id}"}

async def conversation_resource_resolver(conversation_id: int = Path(...)):
    """Resolves the resource string for a conversation."""
    return {"resource": f"conversation:{conversation_id}"}

async def user_resource_resolver(user_id: int = Path(...)):
    """Resolves the resource string for a user."""
    return {"resource": f"user:{user_id}"}

async def canned_response_resource_resolver(response_id: int = Path(...)):
    """Resolves the resource string for a canned response."""
    return {"resource": f"canned-response:{response_id}"}

# Generic resource resolvers
async def users_resource_resolver():
    """Resolves the resource string for users collection."""
    return {"resource": "users"}

async def teams_resource_resolver():
    """Resolves the resource string for teams collection."""
    return {"resource": "teams"}

async def organizations_resource_resolver():
    """Resolves the resource string for organizations collection."""
    return {"resource": "organizations"}

async def conversations_resource_resolver():
    """Resolves the resource string for conversations collection."""
    return {"resource": "conversations"}

async def customers_resource_resolver():
    """Resolves the resource string for customers collection."""
    return {"resource": "customers"}

async def canned_responses_resource_resolver():
    """Resolves the resource string for canned responses collection."""
    return {"resource": "canned-responses"}

async def profile_resource_resolver():
    """Resolves the resource string for user profile."""
    return {"resource": "profile"}

# ============================================================================
# CONVENIENCE FUNCTIONS FOR COMMON PERMISSIONS
# ============================================================================

def require_read_users():
    """Convenience function for reading users collection."""
    return require_permission("read", users_resource_resolver)

def require_edit_users():
    """Convenience function for editing users collection."""
    return require_permission("edit", users_resource_resolver)

def require_create_users():
    """Convenience function for creating users."""
    return require_permission("create", users_resource_resolver)

def require_delete_users():
    """Convenience function for deleting users."""
    return require_permission("delete", users_resource_resolver)

def require_read_organizations():
    """Convenience function for reading organizations."""
    return require_permission("read", organizations_resource_resolver)

def require_edit_organizations():
    """Convenience function for editing organizations."""
    return require_permission("edit", organizations_resource_resolver)

def require_read_teams():
    """Convenience function for reading teams."""
    return require_permission("read", teams_resource_resolver)

def require_edit_teams():
    """Convenience function for editing teams."""
    return require_permission("edit", teams_resource_resolver)

def require_read_conversations():
    """Convenience function for reading conversations."""
    return require_permission("read", conversations_resource_resolver)

def require_edit_conversations():
    """Convenience function for editing conversations."""
    return require_permission("edit", conversations_resource_resolver)

def require_read_canned_responses():
    """Convenience function for reading canned responses."""
    return require_permission("read", canned_responses_resource_resolver)

def require_edit_canned_responses():
    """Convenience function for editing canned responses."""
    return require_permission("edit", canned_responses_resource_resolver)