from fastapi import Depends, HTTPException, status, <PERSON>ie, Path
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload
from itsdangerous import URLSafeTimedSerializer, SignatureExpired, BadTimeSignature
from typing import Optional, Union, List, Dict, Callable
import logging

from app.core.config import settings
from app.db.session import get_async_db
from app.crud import crud_user
from app.models.user import User
from app.core.casbin import get_enforcer, check_permission, add_user_roles

SESSION_COOKIE_NAME = settings.SESSION_COOKIE_NAME
serializer = URLSafeTimedSerializer(settings.SECRET_KEY)

async def get_current_user(
    session_cookie: Optional[str] = <PERSON>ie(None, alias=SESSION_COOKIE_NAME),
    db: AsyncSession = Depends(get_async_db),
) -> User:
    """
    Dependency to get the current, active, non-deleted user from the session cookie.
    This is the single source of truth for user authentication.
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    if session_cookie is None:
        raise credentials_exception
        
    try:
        user_id = int(serializer.loads(session_cookie, max_age=86400))
    except (SignatureExpired, BadTimeSignature, ValueError):
        raise credentials_exception

    # Fetch user with roles, including deleted ones, to provide specific feedback
    user = await crud_user.get_user_with_roles(db, user_id=user_id, include_deleted=True)

    if user is None:
        raise credentials_exception

    # Additional checks for active and non-deleted status
    if not user.is_active:
        raise HTTPException(status_code=400, detail="Inactive user")
    if user.is_deleted:
        raise HTTPException(status_code=400, detail="User account has been deleted")

    return user

# Legacy dependencies for backward compatibility
def get_current_active_admin(current_user: User = Depends(get_current_user)) -> User:
    """Dependency to ensure the user has admin privileges (legacy)."""
    if not current_user.has_role("Admin"):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="The user doesn't have enough privileges (admin required)",
        )
    return current_user

def get_current_active_agent(current_user: User = Depends(get_current_user)) -> User:
    """Dependency to ensure the user is an active agent (legacy)."""
    if not current_user.has_role("Agent"):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="The user doesn't have enough privileges (agent required)",
        )
    return current_user

# NEW: Dynamic RBAC Dependencies
def require_role(required_role: str):
    """
    Dependency generator that checks if the current user has a specific role.
    This is the new, flexible way to protect endpoints.

    Usage:
        @router.get("/admin-only", dependencies=[Depends(require_role("Admin"))])
        or
        @router.get("/admin-only")
        async def admin_endpoint(current_user: User = Depends(require_role("Admin"))):
    """
    async def role_checker(current_user: User = Depends(get_current_user)) -> User:
        if not current_user.has_role(required_role):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Access denied. Required role: '{required_role}'",
            )
        return current_user
    return role_checker

def require_any_role(*required_roles: str):
    """
    Dependency generator that checks if the current user has ANY of the specified roles.

    Usage:
        @router.get("/admin-only")
        async def endpoint(current_user: User = Depends(require_any_role("Admin"))):
    """
    async def role_checker(current_user: User = Depends(get_current_user)) -> User:
        user_roles = current_user.get_role_names()
        if not any(role in user_roles for role in required_roles):
            roles_str = "', '".join(required_roles)
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Access denied. Required roles: '{roles_str}'",
            )
        return current_user
    return role_checker

def require_all_roles(*required_roles: str):
    """
    Dependency generator that checks if the current user has ALL of the specified roles.

    Usage:
        @router.get("/super-admin")
        async def endpoint(current_user: User = Depends(require_all_roles("Admin", "Agent"))):
    """
    async def role_checker(current_user: User = Depends(get_current_user)) -> User:
        user_roles = current_user.get_role_names()
        if not all(role in user_roles for role in required_roles):
            roles_str = "', '".join(required_roles)
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Access denied. Required roles: '{roles_str}'",
            )
        return current_user
    return role_checker

# Convenience dependencies for common roles
def require_admin():
    """Convenience dependency for Admin role"""
    return require_role("Admin")

def require_agent():
    """Convenience dependency for Agent role"""
    return require_role("Agent")

# Removed hardcoded role dependencies for Manager, Supervisor, HR
# Only Admin and Agent are core system roles

async def get_current_user_from_websocket_token(
    token: str,
    db: AsyncSession
) -> Optional[User]:
    """Get current user from WebSocket token parameter"""
    try:
        # Decode the session token
        user_id = serializer.loads(token, max_age=86400)  # 24 hours

        # Get user from database
        user = await crud_user.get_user(db, user_id=user_id)

        if not user:
            return None

        if not user.is_active:
            return None

        if user.is_deleted:
            return None

        return user

    except Exception:
        return None

# ============================================================================
# CASBIN-BASED ATTRIBUTE-BASED ACCESS CONTROL (ABAC) DEPENDENCIES
# ============================================================================

logger = logging.getLogger(__name__)

def require_permission(action: str, resource_resolver: Callable):
    """
    The ultimate RBAC/ABAC dependency using Casbin.

    This replaces role-based checks with permission-based checks.
    Instead of checking "is user an admin?", we ask "can user perform action X on resource Y?"

    Args:
        action (str): The action to be performed (e.g., 'read', 'edit', 'delete').
        resource_resolver (callable): A function that takes route parameters
                                      and resolves them into a resource string
                                      (e.g., "organization:1").

    Usage:
        @router.get("/organizations/{organization_id}")
        async def get_org(
            current_user: User = Depends(require_permission("read", organization_resource_resolver))
        ):
    """
    async def permission_checker(
        request_params: Dict = Depends(resource_resolver),
        current_user: User = Depends(get_current_user),
    ) -> User:
        # Sync user roles with Casbin on each request
        await sync_user_roles_with_casbin(current_user)

        # The resource string, e.g., "organization:1"
        resource = request_params['resource']

        # Check permission using Casbin
        has_permission = check_permission(current_user.email, resource, action)

        if not has_permission:
            logger.warning(f"Permission denied: {current_user.email} cannot {action} on {resource}")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Access denied: Not enough permissions for action '{action}' on resource '{resource}'"
            )

        logger.debug(f"Permission granted: {current_user.email} can {action} on {resource}")
        return current_user

    return permission_checker

async def sync_user_roles_with_casbin(user: User):
    """Sync user roles from database with Casbin enforcer."""
    try:
        user_roles = [role.name for role in user.roles] if user.roles else []
        add_user_roles(user.email, user_roles)
    except Exception as e:
        logger.error(f"Failed to sync user roles with Casbin: {e}")

# ============================================================================
# RESOURCE RESOLVERS
# ============================================================================
# These functions extract path parameters to build the resource string for Casbin.

async def organization_resource_resolver(organization_id: int = Path(...)):
    """Resolves the resource string for an organization."""
    return {"resource": f"organization:{organization_id}"}

async def team_resource_resolver(team_id: int = Path(...)):
    """Resolves the resource string for a team."""
    return {"resource": f"team:{team_id}"}

async def conversation_resource_resolver(conversation_id: int = Path(...)):
    """Resolves the resource string for a conversation."""
    return {"resource": f"conversation:{conversation_id}"}

async def user_resource_resolver(user_id: int = Path(...)):
    """Resolves the resource string for a user."""
    return {"resource": f"user:{user_id}"}

async def canned_response_resource_resolver(response_id: int = Path(...)):
    """Resolves the resource string for a canned response."""
    return {"resource": f"canned-response:{response_id}"}

# Generic resource resolvers
async def users_resource_resolver():
    """Resolves the resource string for users collection."""
    return {"resource": "users"}

async def teams_resource_resolver():
    """Resolves the resource string for teams collection."""
    return {"resource": "teams"}

async def organizations_resource_resolver():
    """Resolves the resource string for organizations collection."""
    return {"resource": "organizations"}

async def conversations_resource_resolver():
    """Resolves the resource string for conversations collection."""
    return {"resource": "conversations"}

async def customers_resource_resolver():
    """Resolves the resource string for customers collection."""
    return {"resource": "customers"}

async def canned_responses_resource_resolver():
    """Resolves the resource string for canned responses collection."""
    return {"resource": "canned-responses"}

async def profile_resource_resolver():
    """Resolves the resource string for user profile."""
    return {"resource": "profile"}

# ============================================================================
# CONVENIENCE FUNCTIONS FOR COMMON PERMISSIONS
# ============================================================================

def require_read_users():
    """Convenience function for reading users collection."""
    return require_permission("read", users_resource_resolver)

def require_edit_users():
    """Convenience function for editing users collection."""
    return require_permission("edit", users_resource_resolver)

def require_create_users():
    """Convenience function for creating users."""
    return require_permission("create", users_resource_resolver)

def require_delete_users():
    """Convenience function for deleting users."""
    return require_permission("delete", users_resource_resolver)

def require_read_organizations():
    """Convenience function for reading organizations."""
    return require_permission("read", organizations_resource_resolver)

def require_edit_organizations():
    """Convenience function for editing organizations."""
    return require_permission("edit", organizations_resource_resolver)

def require_read_teams():
    """Convenience function for reading teams."""
    return require_permission("read", teams_resource_resolver)

def require_edit_teams():
    """Convenience function for editing teams."""
    return require_permission("edit", teams_resource_resolver)

def require_read_conversations():
    """Convenience function for reading conversations."""
    return require_permission("read", conversations_resource_resolver)

def require_edit_conversations():
    """Convenience function for editing conversations."""
    return require_permission("edit", conversations_resource_resolver)

def require_read_canned_responses():
    """Convenience function for reading canned responses."""
    return require_permission("read", canned_responses_resource_resolver)

def require_edit_canned_responses():
    """Convenience function for editing canned responses."""
    return require_permission("edit", canned_responses_resource_resolver)