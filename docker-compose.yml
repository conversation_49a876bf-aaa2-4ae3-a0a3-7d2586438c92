version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: yupcha_postgres
    environment:
      POSTGRES_DB: yupcha_chatbot
      POSTGRES_USER: yupcha_user
      POSTGRES_PASSWORD: yupcha_password
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init.sql:/docker-entrypoint-initdb.d/init.sql
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U yupcha_user -d yupcha_chatbot"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    container_name: yupcha_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Uncomment the following section to run the FastAPI app in Docker
  # app:
  #   build: .
  #   container_name: yupcha_app
  #   ports:
  #     - "8000:8000"
  #   environment:
  #     - DATABASE_URL=******************************************************/yupcha_chatbot
  #     - REDIS_URL=redis://redis:6379/0
  #   depends_on:
  #     postgres:
  #       condition: service_healthy
  #     redis:
  #       condition: service_healthy
  #   restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
