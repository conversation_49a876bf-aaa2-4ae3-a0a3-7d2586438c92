# yupcha-customerbot-ai/tests/test_interactive_agent.py

import asyncio
import websockets
import json
import aioconsole
import aiohttp

# --- Configuration ---
WS_BASE_URL = "ws://localhost:8000/api/ws"
API_BASE_URL = "http://localhost:8000/api"

async def agent_login(session):
    """Logs in the agent and returns the session cookie."""
    email = await aioconsole.ainput("Enter agent email (e.g., <EMAIL>): ")
    password = await aioconsole.ainput("Enter agent password (e.g., agentpassword): ")
    
    login_payload = {'username': email, 'password': password}
    
    async with session.post(f"{API_BASE_URL}/auth/login", data=login_payload) as resp:
        if resp.status != 200:
            print(f"❌ Login failed: {await resp.text()}")
            return None, None
        
        # FastAPI-Mail session cookie is named 'yupcha_session'
        session_cookie = resp.cookies.get('yupcha_session')
        if not session_cookie:
            print("❌ Login succeeded but no session cookie was found.")
            return None, None
            
        print("✅ Login successful!")
        return session_cookie.value, email

async def run_chat_client(websocket):
    """Generic function to handle sending and receiving messages."""
    async def receive_messages():
        try:
            async for message in websocket:
                data = json.loads(message)
                sender = data.get('sender', 'system')
                content = data.get('content', data.get('detail', 'No content'))
                
                # Blue for received messages
                print(f"\n\033[94m>> [{sender.upper()}]: {content}\033[0m")
                print(">> Your message: ", end="", flush=True) # Reprint prompt
        except websockets.ConnectionClosed:
            print("\n👂 Listener: Connection closed by server.")

    async def send_messages():
        while True:
            message_to_send = await aioconsole.ainput(">> Your message: ")
            if message_to_send.lower() in ['quit', 'exit', 'q']:
                break
            if not message_to_send:
                continue
            
            payload = {"content": message_to_send}
            await websocket.send(json.dumps(payload))
    
    receiver_task = asyncio.create_task(receive_messages())
    sender_task = asyncio.create_task(send_messages())
    
    await sender_task # Wait for sender to finish (on 'quit')
    receiver_task.cancel() # Clean up listener

async def main():
    print("\n" + "="*50)
    print("🚀 Yupcha Interactive Agent Chat Client")
    print("="*50)

    async with aiohttp.ClientSession() as http_session:
        session_cookie, agent_email = await agent_login(http_session)
        if not session_cookie:
            return

    conversation_id = await aioconsole.ainput("Enter Conversation ID to join: ")
    if not conversation_id.isdigit():
        print("❌ Invalid Conversation ID.")
        return

    uri = f"{WS_BASE_URL}/chat/{conversation_id}"
    headers = {'Cookie': f'yupcha_session={session_cookie}'}

    print("\n" + "-"*50)
    print(f"👨‍💼 Agent: {agent_email}")
    print(f"💬 Joining Conversation ID: {conversation_id}")
    print("--------------------------------------------------")
    print("Type a message and press Enter. Type 'quit' to exit.")
    print("="*50 + "\n")
    
    try:
        async with websockets.connect(uri, extra_headers=headers) as websocket:
            await run_chat_client(websocket)
    except Exception as e:
        print(f"\n❌ An error occurred: {e}")
    finally:
        print("\n⏹️ Agent client finished.")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Disconnecting...")