import asyncio
import random
from datetime import datetime, timedelta
from faker import Faker
from sqlalchemy import select

from app.db.session import get_async_db
from app.models.user import User
from app.models.role import Role
from app.models.permission import Permission
from app.models.team import Team
from app.models.organization import Organization
from app.models.customer import Customer
from app.models.associations import user_role_association, role_permission_association
from sqlalchemy import insert

fake = Faker()

async def create_dummy_data():
    async for db in get_async_db():
        # Organizations
        orgs = [Organization(
            name=fake.company(),
            description=fake.catch_phrase(),
            website=fake.url(),
            email=fake.company_email(),
            phone=fake.phone_number(),
            address=fake.address(),
            is_active=True,
            created_at=fake.date_time_this_decade()
        ) for _ in range(50)]
        db.add_all(orgs)
        await db.flush()

        # Teams
        teams = [Team(
            name=fake.bs().title(),
            description=fake.sentence(),
            organization_id=random.choice(orgs).id,
            is_active=True,
            created_at=fake.date_time_this_decade()
        ) for _ in range(80)]
        db.add_all(teams)
        await db.flush()

        # Roles - Check if they already exist
        role_names = ["Admin", "Agent", "Manager", "Supervisor", "HR", "Support", "Viewer", "Editor", "Operator", "Lead", "QA", "DevOps"]
        
        # Get existing roles
        existing_roles_result = await db.execute(select(Role.name))
        existing_role_names = {row[0] for row in existing_roles_result.fetchall()}
        
        # Only create roles that don't exist
        roles_to_create = []
        for role_name in role_names:
            if role_name not in existing_role_names:
                roles_to_create.append(Role(
                    name=role_name,
                    description=f"{role_name} role",
                    is_active=True,
                    is_system_role=role_name in ["Admin", "Agent"],
                    company_id=None if role_name in ["Admin", "Agent"] else random.choice(orgs).id,
                    created_at=fake.date_time_this_decade()
                ))
        
        if roles_to_create:
            db.add_all(roles_to_create)
            await db.flush()
        
        # Get all roles (existing + newly created)
        all_roles_result = await db.execute(select(Role))
        roles = all_roles_result.scalars().all()

        # Permissions - Check if they already exist
        perm_names = [
            ("company:users", "read"), ("company:users", "write"), ("company:users", "delete"),
            ("company:teams", "read"), ("company:teams", "write"), ("company:teams", "delete"),
            ("company:organizations", "read"), ("company:organizations", "write"), ("company:organizations", "delete"),
            ("company:customers", "read"), ("company:customers", "write"), ("company:customers", "delete"),
            ("company:roles", "read"), ("company:roles", "write"), ("company:roles", "delete"),
            ("company:permissions", "read"), ("company:permissions", "write"), ("company:permissions", "delete"),
            ("company:canned_responses", "read"), ("company:canned_responses", "write"), ("company:canned_responses", "delete"),
            ("company:assets", "read"), ("company:assets", "write"), ("company:assets", "delete"),
            ("company:conversations", "read"), ("company:conversations", "write"), ("company:conversations", "delete"),
            ("company:messages", "read"), ("company:messages", "write"), ("company:messages", "delete"),
            ("company:notifications", "read"), ("company:notifications", "write"), ("company:notifications", "delete"),
            ("roles", "create"), ("roles", "read"), ("roles", "update"), ("roles", "delete"),
            ("permissions", "read"), ("permissions", "write"),
            ("user_roles", "assign"), ("user_roles", "unassign"), ("user_roles", "read"),
        ]
        
        # Get existing permissions
        existing_perms_result = await db.execute(select(Permission.name))
        existing_perm_names = {row[0] for row in existing_perms_result.fetchall()}
        
        # Only create permissions that don't exist
        permissions_to_create = []
        for res, act in perm_names:
            perm_name = f"{res}.{act}"
            if perm_name not in existing_perm_names:
                permissions_to_create.append(Permission(
                    name=perm_name,
                    resource=res,
                    action=act,
                    description=f"Can {act} {res}",
                    is_system_permission=True,
                    created_at=fake.date_time_this_decade()
                ))
        
        if permissions_to_create:
            db.add_all(permissions_to_create)
            await db.flush()
        
        # Get all permissions (existing + newly created)
        all_perms_result = await db.execute(select(Permission))
        permissions = all_perms_result.scalars().all()

        # Assign permissions to roles (skip if already assigned)
        for role in roles:
            perm_sample = random.sample(permissions, k=random.randint(4, 8))
            for perm in perm_sample:
                # Check if this role-permission combination already exists
                existing_assoc = await db.execute(
                    select(role_permission_association).where(
                        role_permission_association.c.role_id == role.id,
                        role_permission_association.c.permission_id == perm.id
                    )
                )
                if not existing_assoc.fetchone():
                    await db.execute(insert(role_permission_association).values(role_id=role.id, permission_id=perm.id))

        # Users
        users = [User(
            full_name=fake.name(),
            email=fake.unique.email(),
            hashed_password="fakehashedpassword",
            company_id=random.choice(orgs).id,
            team_id=random.choice(teams).id,
            is_active=True,
            is_deleted=False,
            created_at=fake.date_time_this_decade()
        ) for _ in range(200)]
        db.add_all(users)
        await db.flush()

        # Assign roles to users (skip if already assigned)
        for user in users:
            role_sample = random.sample(roles, k=random.randint(1, 3))
            for role in role_sample:
                # Check if this user-role combination already exists
                existing_assoc = await db.execute(
                    select(user_role_association).where(
                        user_role_association.c.user_id == user.id,
                        user_role_association.c.role_id == role.id
                    )
                )
                if not existing_assoc.fetchone():
                    await db.execute(insert(user_role_association).values(user_id=user.id, role_id=role.id))

        # Customers
        customers = [Customer(
            customer_id=fake.uuid4(),
            name=fake.name(),
            email=fake.email(),
            phone=fake.phone_number(),
            ip_address=fake.ipv4(),
            location=fake.city(),
            user_agent=fake.user_agent(),
            customer_metadata=fake.text(max_nb_chars=50),
            is_active=True,
            organization_id=random.choice(orgs).id,
            created_at=fake.date_time_this_decade()
        ) for _ in range(150)]
        db.add_all(customers)
        await db.commit()
        print("Dummy data created!")

if __name__ == "__main__":
    asyncio.run(create_dummy_data())