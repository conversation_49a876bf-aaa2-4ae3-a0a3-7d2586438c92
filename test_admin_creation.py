#!/usr/bin/env python3
"""
Test script to create admin user
"""
import asyncio
import sys
import os

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def test_admin_creation():
    try:
        from app.db.session import get_async_db
        from app.crud import crud_user, crud_organization, crud_team
        from app.crud.crud_role import crud_role
        from app.schemas.user import UserCreate
        from app.core.security import get_password_hash
        
        print("🧪 Testing admin user creation...")
        
        async for db in get_async_db():
            # Get the first organization
            org = await crud_organization.get_organization_by_name(db, "Yupcha Inc.")
            if not org:
                print("❌ Organization 'Yupcha Inc.' not found")
                return
                
            print(f"✅ Found organization: {org.name} (ID: {org.id})")
            
            # Get the first team for this organization
            teams = await crud_team.get_teams_by_organization(db, org.id)
            if not teams:
                print("❌ No teams found for organization")
                return
                
            team = teams[0]
            print(f"✅ Found team: {team.name} (ID: {team.id})")
            
            # Get Admin role for this organization
            admin_role = await crud_role.get_role_by_name(db, "Admin", org.id)
            if not admin_role:
                print("❌ Admin role not found")
                return
                
            print(f"✅ Found Admin role: {admin_role.name} (ID: {admin_role.id})")
            
            # Check if admin user already exists
            existing_admin = await crud_user.get_by_email(db, "<EMAIL>")
            if existing_admin:
                print(f"⚠️  Admin user already exists: {existing_admin.email}")
                return
            
            # Create admin user
            admin_data = UserCreate(
                full_name="Admin User",
                email="<EMAIL>",
                password="adminpassword",
                organization_id=org.id,
                team_id=team.id,
                role_id=admin_role.id
            )
            
            admin_user = await crud_user.create_user(db, admin_data)
            await db.commit()
            
            print(f"✅ Admin user created successfully!")
            print(f"   - Name: {admin_user.full_name}")
            print(f"   - Email: {admin_user.email}")
            print(f"   - ID: {admin_user.id}")
            print(f"   - Organization: {org.name}")
            print(f"   - Team: {team.name}")
            print(f"   - Role: {admin_role.name}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_admin_creation())
