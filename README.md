# Yu<PERSON>cha Customer Bot AI

A FastAPI-based customer chatbot with WebSocket support and PostgreSQL database.

## Features

- **FastAPI Backend**: Modern, fast web framework for building APIs
- **WebSocket Support**: Real-time chat functionality
- **PostgreSQL Database**: Robust database with async support
- **Agent Dashboard**: Interface for customer service agents
- **Customer Widget**: Embeddable chat widget for customers
- **Database Migrations**: Alembic for database schema management
- **Environment Configuration**: Flexible configuration with .env files
- **Docker Support**: Easy deployment with Docker Compose

## Project Structure

```
yupcha-customerbot-ai/
├── FEATURES_CHECKLIST.md
├── IMPLEMENTATION_SUMMARY.md
├── MEDIA_SYSTEM.md
├── README.md
├── alembic
│   ├── __pycache__
│   │   └── env.cpython-313.pyc
│   ├── env.py
│   ├── script.py.mako
│   └── versions
│       ├── 0ab0653408e2_add_deleted_field_to_messages_table.py
│       ├── 1cb751c10ce7_fix_model_relationships.py
│       ├── 46061e647721_add_asset_table_for_media_files.py
│       ├── 641a446df1da_add_default_team_id_to_organization.py
│       ├── 7962a498c8a7_add_user_model_and_agent_assignment_to_.py
│       ├── 8e48c03e38bc_create_initial_chat_tables.py
│       ├── 921e3625655c_major_refactor_add_organization_.py
├── alembic.ini
├── app
│   ├── __init__.py
│   ├── api
│   │   ├── __init__.py
│   │   ├── api.py
│   │   └── endpoints
│   │       ├── __init__.py
│   │       ├── auth.py
│   │       ├── conversations.py
│   │       ├── customers.py
│   │       ├── media.py
│   │       ├── messages.py
│   │       ├── organizations.py
│   │       ├── teams.py
│   │       ├── users.py
│   │       └── websocket.py
│   ├── auth
│   │   ├── __init__.py
│   │   └── dependencies.py
│   ├── core
│   │   ├── __init__.py
│   │   ├── config.py
│   │   ├── s3.py
│   │   ├── security.py
│   │   └── websocket_manager.py
│   ├── crud
│   │   ├── __init__.py
│   │   ├── crud_asset.py
│   │   ├── crud_chat.py
│   │   ├── crud_customer.py
│   │   ├── crud_organization.py
│   │   ├── crud_team.py
│   │   └── crud_user.py
│   ├── db
│   │   ├── __init__.py
│   │   ├── base.py
│   │   └── session.py
│   ├── main.py
│   ├── models
│   │   ├── __init__.py
│   │   ├── asset.py
│   │   ├── chat.py
│   │   ├── customer.py
│   │   ├── organization.py
│   │   ├── team.py
│   │   └── user.py
│   └── schemas
│       ├── __init__.py
│       ├── asset.py
│       ├── chat.py
│       ├── customer.py
│       ├── organization.py
│       ├── team.py
│       └── user.py
├── docker-compose.yml
├── pyproject.toml
├── scripts
│   ├── init.sql
│   ├── setup_database.py
│   └── update_passwords.py
├── test_cookies.txt
├── tests
│   ├── __init__.py
│   ├── curl_workflow_test.sh
│   ├── debug_websocket.py
│   ├── media_demo.html
│   ├── test_admin_agent_workflow.py
│   ├── test_advanced_features.py
│   ├── test_agent_reply.sh
│   ├── test_api.py
│   ├── test_complete_system.sh
│   ├── test_enhanced_system.sh
│   ├── test_fixed_media_system.py
│   ├── test_media_system.sh
│   ├── test_setup.py
│   ├── test_websocket_agent_reply.py
│   └── websocket_demo.html
└── uv.lock              # Project dependencies
```

## Quick Start

### Option 1: Development with Frontend (Recommended)

**🚀 One-Command Startup:**
```bash
# Linux/macOS
./start-dev.sh

# Windows
start-dev.bat
```

This will automatically start both:
- **Backend API** on `http://0.0.0.0:8000`
- **Vue.js Frontend** on `http://localhost:5173`

**Manual Startup:**
```bash
# Terminal 1: Start Backend
uv run uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# Terminal 2: Start Frontend
cd frontend-vue
npm install  # First time only
npm run dev
```

**Access the Application:**
- **Frontend Dashboard**: http://localhost:5173
- **API Documentation**: http://0.0.0.0:8000/docs
- **Vue DevTools**: http://localhost:5173/__devtools__/

**Demo Credentials:**
- **Admin**: `<EMAIL>` / `adminpassword`
- **Agent**: `<EMAIL>` / `agentpassword`

### Option 2: Using Docker

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd yupcha-customerbot-ai
   ```

2. **Start PostgreSQL and Redis with Docker**
   ```bash
   docker-compose up -d postgres redis
   ```

3. **Install dependencies**
   ```bash
   uv sync
   ```

4. **Run database migrations**
   ```bash
   alembic upgrade head
   ```

5. **Start the application**
   ```bash
   python main.py
   ```

### Option 3: Manual Setup

1. **Install PostgreSQL**
   - Install PostgreSQL on your system
   - Create a database and user as specified in `.env`

2. **Install dependencies**
   ```bash
   uv sync
   ```

3. **Configure environment**
   ```bash
   cp .env.example .env
   # Edit .env with your database credentials
   ```

4. **Setup database**
   ```bash
   python scripts/setup_database.py
   ```

5. **Run migrations**
   ```bash
   alembic upgrade head
   ```

6. **Start the application**
   ```bash
   python main.py
   ```

## Configuration

The application uses environment variables for configuration. Copy `.env.example` to `.env` and modify as needed:

```bash
# Database Configuration
DATABASE_URL=postgresql://yupcha_user:yupcha_password@localhost:5432/yupcha_chatbot
DB_HOST=localhost
DB_PORT=5432
DB_NAME=yupcha_chatbot
DB_USER=yupcha_user
DB_PASSWORD=yupcha_password

# Application Settings
APP_NAME=Yupcha Customer Bot AI
DEBUG=True
SECRET_KEY=your-secret-key-change-this-in-production

# Server Settings
HOST=0.0.0.0
PORT=8000
```

## API Endpoints

- **GET /**: Welcome message
- **GET /api/conversations/**: List all conversations
- **POST /api/conversations/**: Create a new conversation
- **GET /api/conversations/{id}**: Get a specific conversation
- **GET /api/conversations/{id}/messages**: Get messages for a conversation
- **WebSocket /api/ws/chat/{conversation_id}**: Real-time chat
- **WebSocket /api/ws/agent/{agent_id}**: Agent dashboard connection

## Frontend Applications

### Vue.js Administration Dashboard (Recommended)
- **Location**: `frontend-vue/`
- **Technology**: Vue.js 3 + Vite + npm
- **URL**: http://localhost:5173
- **Features**:
  - Modern admin interface with hot reload
  - Role-based access control
  - Real-time dashboard and chat
  - Complete CRUD operations
  - Mobile-responsive design

### Pure Client-Side Dashboard (Alternative)
- **Location**: `frontend/`
- **Technology**: Pure Vue.js 3 with CDN
- **URL**: Open `frontend/index.html` in browser
- **Features**:
  - No build process required
  - Same functionality as npm version
  - Easy deployment to any static hosting

### Legacy Static Files
- **Customer Widget**: `/static/user_widget.html`
- **Agent Dashboard**: `/static/agent.html`

## Database Migrations

Create a new migration:
```bash
alembic revision --autogenerate -m "Description of changes"
```

Apply migrations:
```bash
alembic upgrade head
```

## Development

1. **Install development dependencies**
   ```bash
   uv sync --dev
   ```

2. **Run with auto-reload**
   ```bash
   uv run uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
   ```

3. **Start frontend development server**
   ```bash
   cd frontend-vue
   npm run dev
   ```

4. **Access the application**
   - **Vue.js Dashboard**: http://localhost:5173
   - **API Documentation**: http://0.0.0.0:8000/docs
   - **Legacy Customer Widget**: http://0.0.0.0:8000/static/user_widget.html
   - **Legacy Agent Dashboard**: http://0.0.0.0:8000/static/agent.html

## Docker Deployment

Build and run the entire stack:
```bash
docker-compose up --build
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests
5. Submit a pull request

## License

This project is licensed under the MIT License.
