# 🚀 Yu<PERSON>cha Customer Bot AI

A production-ready, enterprise-level customer support system with real-time chat, advanced file handling, and intelligent conversation management.

## ✨ **Key Features**

### 🔐 **Authentication & Security**
- **Cookie-based session authentication** with secure password hashing
- **Role-based access control** (Admin, Agent, Customer)
- **Multi-tenant architecture** with organization isolation
- **Session management** with automatic cleanup

### 💬 **Advanced Real-time Chat**
- **WebSocket-based messaging** with instant delivery
- **Multiple message types**: text, image, video, audio, gif, document
- **Message soft deletion** with real-time broadcasting
- **Message restoration** (admin only)
- **Typing indicators** and presence tracking

### 📁 **Production File Management**
- **S3/MinIO cloud storage** integration
- **Multiple file types** with validation and processing
- **Public upload endpoints** for customer widgets
- **Image processing** with automatic dimension extraction
- **Asset management** with complete metadata tracking

### 🔔 **Real-time Notifications**
- **Organization-wide notification channels** for agents
- **Instant alerts** for new conversations and assignments
- **Dual WebSocket system**: Chat + notifications
- **Background task processing** for email notifications

### 🏢 **Enterprise Features**
- **Multi-organization support** with data isolation
- **Team-based conversation routing** with default assignments
- **Admin dashboard** with FastAPI Admin integration
- **Comprehensive audit trails** for all operations

## 🏗️ **Technical Architecture**

### **Backend Stack**
- **FastAPI** - Modern, fast web framework with async support
- **PostgreSQL** - Robust relational database with async SQLAlchemy
- **WebSocket** - Real-time bidirectional communication
- **S3/MinIO** - Scalable cloud file storage
- **Alembic** - Database migration management

### **Database Schema**
```sql
organizations  → Multi-tenant organization support
teams         → Team-based conversation routing  
users         → Admin, agent, customer management
customers     → Customer profiles and conversation history
conversations → Chat conversation tracking with status
messages      → Messages with deletion support and media
assets        → Media file metadata and S3 storage links
```

## 🚀 **Quick Start**

### **Prerequisites**
- Python 3.11+
- PostgreSQL database
- S3/MinIO storage service
- uv (Python package manager)

### **1. Installation**
```bash
# Clone repository
git clone <repository-url>
cd yupcha-customerbot-ai

# Install dependencies
uv install
```

### **2. Environment Configuration**
```bash
# Copy environment template
cp .env.example .env
```

Update `.env` with your configuration:
```env
# Database (REQUIRED)
DATABASE_URL=postgresql://user:password@host:port/database
ASYNC_DATABASE_URL=postgresql+asyncpg://user:password@host:port/database

# S3/MinIO Storage (REQUIRED)
YUPCHA_S3__ENDPOINT_URL="https://your-s3-endpoint.com"
YUPCHA_S3__ACCESS_KEY="your-access-key"
YUPCHA_S3__SECRET_KEY="your-secret-key"
YUPCHA_S3__BUCKET_NAME="yupcha-media"

# Application Settings
YUPCHA_APP_NAME="Yupcha Customer Bot AI"
YUPCHA_SECRET_KEY="your-super-secret-key"
DEBUG=True
```

### **3. Database Setup**
```bash
# Run database migrations
uv run alembic upgrade head
```

### **4. Start the Server**
```bash
# Development server with auto-reload
uv run uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### **5. Access the Application**
- **🌐 API Documentation**: http://localhost:8000/docs
- **❤️ Health Check**: http://localhost:8000/health
- **📊 Admin Panel**: http://localhost:8000/admin

## 📚 **Complete API Reference**

### **🔐 Authentication**
```bash
POST /api/auth/login     # User login with credentials
POST /api/auth/logout    # User logout and session cleanup
GET  /api/auth/me        # Get current authenticated user
```

### **👥 User Management**
```bash
GET  /api/users/         # List all users with pagination
POST /api/users/         # Create new user account
GET  /api/users/{id}     # Get specific user details
GET  /api/users/agents   # List all agent users
GET  /api/users/admins   # List all admin users
```

### **🏢 Organization & Teams**
```bash
GET  /api/organizations/     # List organizations
POST /api/organizations/     # Create new organization
GET  /api/organizations/{id} # Get organization details

GET  /api/teams/         # List teams
POST /api/teams/         # Create new team
GET  /api/teams/{id}     # Get team details
```

### **👤 Customer Management**
```bash
GET  /api/customers/     # List customers with search
POST /api/customers/     # Create customer profile
GET  /api/customers/{id} # Get customer details
```

### **💬 Conversation Management**
```bash
GET  /api/conversations/              # List conversations with filters
POST /api/conversations/              # Create new conversation
GET  /api/conversations/{id}          # Get conversation details
GET  /api/conversations/{id}/messages # Get conversation messages
POST /api/conversations/{id}/messages # Send new message
POST /api/conversations/{id}/assign/{team_id} # Assign to team
GET  /api/conversations/unassigned    # Get unassigned conversations
GET  /api/conversations/team/{team_id} # Get team conversations
```

### **🗑️ Advanced Message Operations**
```bash
GET    /api/messages/{id}         # Get message details
DELETE /api/messages/{id}         # Soft delete message (sender/admin only)
PATCH  /api/messages/{id}/restore # Restore deleted message (admin only)
```

### **📁 File Upload & Management**
```bash
POST   /api/media/upload        # Authenticated file upload (agents/admins)
POST   /api/media/upload/public # Public file upload (customers)
GET    /api/media/assets        # List uploaded assets with filters
GET    /api/media/assets/{id}   # Get asset details and metadata
DELETE /api/media/assets/{id}   # Delete asset from storage and database
```

### **🔌 WebSocket Real-time Endpoints**
```bash
WS /api/ws/chat/{conversation_id}    # Real-time chat messaging
WS /api/ws/agent-notifications      # Organization-wide agent notifications
```

## 🧪 **Testing & Validation**

### **Automated Test Scripts**
```bash
# Test all advanced features
uv run python tests/test_advanced_features.py

# Test media upload system
uv run python tests/test_fixed_media_system.py

# Test database and API health
curl http://localhost:8000/health
```

### **Interactive Testing**
1. **📖 API Documentation**: http://localhost:8000/docs
2. **🎮 Media Demo**: Open `tests/media_demo.html` in browser
3. **🔐 Default Login**: <EMAIL> / adminpassword

### **File Upload Testing**
```bash
# Login and get session cookie
curl -X POST http://localhost:8000/api/auth/login \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=<EMAIL>&password=adminpassword" \
  -c cookies.txt

# Test file upload
curl -X POST http://localhost:8000/api/media/upload \
  -H "Content-Type: multipart/form-data" \
  -F "file=@your-test-file.jpg" \
  -b cookies.txt
```

## 🔧 **Configuration Details**

### **Environment Variables**
```env
# Database Configuration
DATABASE_URL=postgresql://postgres:password@host:port/database
ASYNC_DATABASE_URL=postgresql+asyncpg://postgres:password@host:port/database
DB_HOST=your-db-host
DB_PORT=5432
DB_NAME=your-database
DB_USER=postgres
DB_PASSWORD=your-password

# Application Settings
YUPCHA_APP_NAME="Yupcha Customer Bot AI"
YUPCHA_SECRET_KEY="your-super-secret-key-for-sessions"
DEBUG=True

# S3/MinIO Storage
YUPCHA_S3__ENDPOINT_URL="https://your-s3-endpoint.com"
YUPCHA_S3__ACCESS_KEY="your-access-key"
YUPCHA_S3__SECRET_KEY="your-secret-key"
YUPCHA_S3__BUCKET_NAME="yupcha-media"
YUPCHA_S3__REGION="us-east-1"

# Server Settings
HOST=0.0.0.0
PORT=8000

# CORS Settings
ALLOWED_ORIGINS=["http://localhost:3000", "http://127.0.0.1:3000"]
```

### **Default Users**
The system automatically creates default users on startup:
- **Admin**: <EMAIL> / adminpassword
- **Agent**: <EMAIL> / agentpassword

## 📖 **Documentation Files**

- **FEATURES_CHECKLIST.md** - Complete feature implementation status
- **DATABASE_MIGRATION_SUMMARY.md** - Database setup and migration guide
- **S3_CONFIGURATION_SUMMARY.md** - File storage configuration guide
- **MINIO_SETUP.md** - MinIO setup instructions

## 🚀 **Production Deployment**

### **Environment Setup**
1. Set `DEBUG=False` in production
2. Use strong `YUPCHA_SECRET_KEY`
3. Configure production PostgreSQL database
4. Set up production S3/MinIO service
5. Configure CORS for your domain
6. Set up HTTPS with proper SSL certificates

### **Security Considerations**
- Change default user passwords immediately
- Use HTTPS in production environments
- Set up proper CORS origins for your domain
- Configure rate limiting for API endpoints
- Set up monitoring and logging
- Regular security updates and patches

## 🎯 **Project Structure**

```
yupcha-customerbot-ai/
├── app/
│   ├── api/endpoints/     # API route handlers
│   ├── auth/             # Authentication logic
│   ├── core/             # Core configuration and utilities
│   ├── crud/             # Database operations
│   ├── db/               # Database configuration
│   ├── models/           # SQLAlchemy models
│   ├── schemas/          # Pydantic schemas
│   └── main.py           # Application entry point
├── alembic/              # Database migrations
├── tests/                # Test scripts and demos
├── .env                  # Environment configuration
├── alembic.ini           # Migration configuration
└── pyproject.toml        # Project dependencies
```

## 🤝 **Contributing**

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Make your changes
4. Add tests for new features
5. Commit your changes (`git commit -m 'Add amazing feature'`)
6. Push to the branch (`git push origin feature/amazing-feature`)
7. Open a Pull Request

## 📄 **License**

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 **Support & Troubleshooting**

### **Common Issues**
1. **Database Connection**: Check DATABASE_URL and ensure PostgreSQL is running
2. **File Upload Errors**: Verify S3/MinIO configuration and credentials
3. **WebSocket Issues**: Check CORS settings and firewall configuration
4. **Authentication Problems**: Verify SECRET_KEY and session configuration

### **Getting Help**
1. Check the documentation files in the repository
2. Review the API documentation at `/docs`
3. Test with the provided demo scripts
4. Check server logs for detailed error messages
5. Verify environment configuration

---

## 🎉 **Production Ready Features**

✅ **Real-time chat with WebSocket**
✅ **Advanced file upload system**  
✅ **Message deletion and management**
✅ **Organization-wide notifications**
✅ **Multi-tenant architecture**
✅ **Role-based security**
✅ **Production S3 storage**
✅ **Complete API documentation**
✅ **Database migrations**
✅ **Comprehensive testing**

**Your enterprise-level customer support system is ready for production!** 🚀
