<div align="center">

# YUPCHA-CUSTOMERBOT-AI

### Empowering Support with Intelligent, Seamless Customer Interactions

<p>
  <a href="https://github.com/your-username/yupcha-customerbot-ai/actions/workflows/ci.yml">
    <img src="https://img.shields.io/github/actions/workflow/status/your-username/yupcha-customerbot-ai/ci.yml?style=for-the-badge&logo=githubactions&logoColor=white&label=CI" alt="Build Status">
  </a>
  <a href="https://github.com/your-username/yupcha-customerbot-ai/commits/main">
    <img src="https://img.shields.io/github/last-commit/your-username/yupcha-customerbot-ai?style=for-the-badge&logo=git&logoColor=white" alt="Last Commit">
  </a>
  <a href="https://github.com/your-username/yupcha-customerbot-ai">
    <img src="https://img.shields.io/github/languages/top/your-username/yupcha-customerbot-ai?style=for-the-badge" alt="Top Language">
  </a>
  <a href="https://github.com/your-username/yupcha-customerbot-ai">
    <img src="https://img.shields.io/github/languages/count/your-username/yupcha-customerbot-ai?style=for-the-badge" alt="Language Count">
  </a>
</p>

<br>

A production-ready, enterprise-level customer support system built with a modern Python stack, featuring real-time chat, advanced file handling, and intelligent conversation management.

</div>

---
<div align="center">

## ✨ Key Features

| Category | Feature | Status |
| :--- | :--- | :---: |
| 🔐 **Security & Auth** | Cookie-based Sessions & Secure Hashing | ✅ |
| | Single Role Per User System (Admin/HR/Manager/Agent) | ✅ |
| | Dynamic RBAC/ABAC with Casbin | ✅ |
| | Multi-Tenant Architecture (Organizations & Teams) | ✅ |
| | Comprehensive Audit Logging | ✅ |
| | Password Reset Flow | ✅ |
| 💬 **Real-Time Chat** | Scalable WebSocket Messaging (Redis Pub/Sub) | ✅ |
| | Typing Indicators & User Presence | ✅ |
| | Media Attachments (Image, Video, Docs) | ✅ |
| | Message Soft Deletion & Restoration | ✅ |
| 🤖 **AI & Automation** | Rule-Based Chatbot (Agentless Response) | ✅ |
| | AI-Powered Reply Suggestions & Summaries | ✅ |
| | Canned Responses (Macros) | ✅ |
| 📁 **Infrastructure** | S3/MinIO Cloud Storage Integration | ✅ |
| | Database Migrations with Alembic | ✅ |
| | Scheduled & Background Tasks (APScheduler) | ✅ |
| | Docker & Docker Compose for Easy Setup | ✅ |

---
</div>

## 🛠️ Built with the tools and technologies:

<div align="center">
  <p>
    <img src="https://img.shields.io/badge/Python-3776AB?style=for-the-badge&logo=python&logoColor=white" alt="Python">
    <img src="https://img.shields.io/badge/FastAPI-009688?style=for-the-badge&logo=fastapi&logoColor=white" alt="FastAPI">
    <img src="https://img.shields.io/badge/SQLAlchemy-D71F00?style=for-the-badge&logo=sqlalchemy&logoColor=white" alt="SQLAlchemy">
    <img src="https://img.shields.io/badge/PostgreSQL-4169E1?style=for-the-badge&logo=postgresql&logoColor=white" alt="PostgreSQL">
    <img src="https://img.shields.io/badge/Redis-DC382D?style=for-the-badge&logo=redis&logoColor=white" alt="Redis">
    <img src="https://img.shields.io/badge/Docker-2496ED?style=for-the-badge&logo=docker&logoColor=white" alt="Docker">
    <img src="https://img.shields.io/badge/MinIO-C7453B?style=for-the-badge&logo=minio&logoColor=white" alt="MinIO">
  </p>
  <p>
    <img src="https://img.shields.io/badge/Pydantic-E92063?style=for-the-badge&logo=pydantic&logoColor=white" alt="Pydantic">
    <img src="https://img.shields.io/badge/Casbin-4F4F4F?style=for-the-badge" alt="Casbin">
    <img src="https://img.shields.io/badge/Typer-00B8D4?style=for-the-badge&logo=typer&logoColor=white" alt="Typer">
    <img src="https://img.shields.io/badge/AIOHTTP-2C5282?style=for-the-badge&logo=aiohttp&logoColor=white" alt="AIOHTTP">
    <img src="https://img.shields.io/badge/uv-953599?style=for-the-badge" alt="uv">
    <img src="https://img.shields.io/badge/Uvicorn-F76D3B?style=for-the-badge&logo=uvicorn&logoColor=white" alt="Uvicorn">
  </p>
</div>

---

## 🚀 **Quick Start**

### **Prerequisites**
- Python 3.11+
- PostgreSQL database
- S3/MinIO storage service
- uv (Python package manager)

### **1. Installation**
```bash
# Clone repository
git clone <repository-url>
cd yupcha-customerbot-ai

# Install dependencies
uv install
```

### **2. Environment Configuration**
```bash
# Copy environment template
cp .env.example .env
```

Update `.env` with your configuration:
```env
# Database (REQUIRED)
DATABASE_URL=postgresql://user:password@host:port/database
ASYNC_DATABASE_URL=postgresql+asyncpg://user:password@host:port/database

# S3/MinIO Storage (REQUIRED)
YUPCHA_S3__ENDPOINT_URL="https://your-s3-endpoint.com"
YUPCHA_S3__ACCESS_KEY="your-access-key"
YUPCHA_S3__SECRET_KEY="your-secret-key"
YUPCHA_S3__BUCKET_NAME="yupcha-media"

# Application Settings
YUPCHA_APP_NAME="Yupcha Customer Bot AI"
YUPCHA_SECRET_KEY="your-super-secret-key"
DEBUG=True
```

### **Database Setup**
```bash
# 1. Apply database migrations (creates all tables and applies schema changes)
alembic upgrade head

# 2. Seed initial application data (e.g., default organization, admin user, permissions)
# You will be prompted for values, or you can provide them as arguments:
uv run python manage.py setup-defaults --org-name "Default Organization" --team-name "Default Team" --full-name "Admin User" --email "<EMAIL>" --password "adminpassword"
```

### **4. Start the Server**
```bash
# Development server with auto-reload
uv run uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### **5. Access the Application**
- **🌐 API Documentation**: http://localhost:8000/docs
- **🌐 Ccalar Check**: http://localhost:8000/scalar

## 📚 **Complete API Reference**

### **🔐 Authentication & Role System**

#### **Single Role Per User System**
Each user has exactly **ONE role** (no more multiple roles confusion):

| Role | Description | Customer Chat | Admin Access |
|------|-------------|---------------|--------------|
| **Admin** | Full system access | ✅ | ✅ |
| **HR** | Human resources management | ✅ | ❌ |
| **Manager** | Team management | ✅ | ❌ |
| **Agent** | Customer support (default) | ✅ | ❌ |

**Key Features:**
- 🎯 **Any authenticated user can chat with customers** (not just agents!)
- 🔄 **Default role**: Users without assigned roles default to "Agent"
- 🔒 **Casbin-based permissions**: Dynamic, file-based access control
- 🏢 **Company-scoped**: Roles are specific to organizations

#### **Authentication Endpoints**
```bash
POST /api/v1/auth/login     # User login with credentials
POST /api/v1/auth/logout    # User logout and session cleanup
GET  /api/v1/auth/me        # Get current authenticated user with role
```

**Example Response:**
```json
{
  "id": 1,
  "email": "<EMAIL>",
  "full_name": "Admin User",
  "role": {
    "name": "Admin",
    "description": "System Administrator"
  },
  "is_admin": true
}
```

### **👥 User Management**
```bash
GET  /api/v1/users/         # List all users with pagination and single roles
POST /api/v1/users/         # Create new user account with role assignment
GET  /api/v1/users/{id}     # Get specific user details with role
PUT  /api/v1/users/{id}     # Update user details and role
DELETE /api/v1/users/{id}   # Soft delete user account
```

### **🏢 Organization & Teams**
```bash
GET  /api/v1/organizations/     # List organizations
POST /api/v1/organizations/     # Create new organization
GET  /api/v1/organizations/{id} # Get organization details
PUT  /api/v1/organizations/{id} # Update organization

GET  /api/v1/teams/         # List teams
POST /api/v1/teams/         # Create new team
GET  /api/v1/teams/{id}     # Get team details
PUT  /api/v1/teams/{id}     # Update team
```

### **👤 Customer Management**
```bash
GET  /api/v1/customers/     # List customers with search
POST /api/v1/customers/     # Create customer profile
GET  /api/v1/customers/{id} # Get customer details
PUT  /api/v1/customers/{id} # Update customer profile
```

### **💬 Conversation Management**
```bash
GET  /api/v1/conversations/              # List conversations with filters
POST /api/v1/conversations/              # Create new conversation
GET  /api/v1/conversations/{id}          # Get conversation details
GET  /api/v1/conversations/{id}/messages # Get conversation messages
POST /api/v1/conversations/{id}/messages # Send new message
PUT  /api/v1/conversations/{id}/assign/{team_id} # Assign to team
GET  /api/v1/conversations/unassigned    # Get unassigned conversations
GET  /api/v1/conversations/team/{team_id} # Get team conversations
```

### **📝 Notes & Labels Management**
```bash
# Customer Notes
GET  /api/v1/notes/                    # List customer notes
POST /api/v1/notes/                    # Create customer note
GET  /api/v1/notes/{id}                # Get note details
PUT  /api/v1/notes/{id}                # Update note
DELETE /api/v1/notes/{id}              # Delete note

# Conversation Notes (Internal)
GET  /api/v1/conversation-notes/       # List conversation notes
POST /api/v1/conversation-notes/       # Create internal note
GET  /api/v1/conversation-notes/{id}   # Get note details
PUT  /api/v1/conversation-notes/{id}   # Update note
DELETE /api/v1/conversation-notes/{id} # Delete note

# Labels & Tagging
GET  /api/v1/labels/                   # List conversation labels
POST /api/v1/labels/                   # Create new label
GET  /api/v1/labels/{id}               # Get label details
PUT  /api/v1/labels/{id}               # Update label
DELETE /api/v1/labels/{id}             # Delete label
```

### **🤖 Canned Responses**
```bash
GET  /api/v1/canned-responses/         # List template responses
POST /api/v1/canned-responses/         # Create new template
GET  /api/v1/canned-responses/{id}     # Get template details
PUT  /api/v1/canned-responses/{id}     # Update template
DELETE /api/v1/canned-responses/{id}   # Delete template
```

### **🔐 Role & Permission Management**
```bash
GET  /api/v1/roles/                    # List available roles
POST /api/v1/roles/                    # Create new role
GET  /api/v1/roles/{id}                # Get role details
PUT  /api/v1/roles/{id}                # Update role
DELETE /api/v1/roles/{id}              # Delete role

POST /api/v1/permissions/check         # Check user permissions
GET  /api/v1/permissions/roles         # Get user's roles
```

### **🗑️ Advanced Message Operations**
```bash
GET    /api/v1/messages/{id}         # Get message details
DELETE /api/v1/messages/{id}         # Soft delete message (sender/admin only)
PATCH  /api/v1/messages/{id}/restore # Restore deleted message (admin only)
```

### **📁 File Upload & Management**
```bash
POST   /api/v1/media/upload        # Authenticated file upload (any user)
POST   /api/v1/media/upload/public # Public file upload (customers)
GET    /api/v1/media/assets        # List uploaded assets with filters
GET    /api/v1/media/assets/{id}   # Get asset details and metadata
DELETE /api/v1/media/assets/{id}   # Delete asset from storage and database
```

### **🔌 WebSocket Real-time Endpoints**
```bash
WS /api/v1/ws/chat/{conversation_id}    # Unified real-time chat (ANY authenticated user + customers)
WS /api/v1/ws/user-notifications        # Organization-wide user notifications
```

**WebSocket Features:**
- 🎯 **Universal Access**: Any authenticated user (Admin/HR/Manager/Agent) can chat with customers
- 🚀 **Redis-based Scaling**: Horizontal scaling across multiple servers
- 🛡️ **Rate Limiting**: Spam prevention with Redis backend
- 📱 **Real-time Broadcasting**: Instant message delivery
- 🔒 **Session Authentication**: Secure cookie-based auth for WebSocket

## 🧪 **Testing & Validation**

### **Automated Test Scripts**
```bash
# Run all tests
pytest

# Run specific test file
pytest tests/test_auth.py

# Run with coverage
pytest --cov=app tests/
```

### **Interactive Testing**
1. **📖 API Documentation**: http://localhost:8000/docs
2. **🎮 Media Demo**: Open `tests/media_demo.html` in browser
3. **🔐 Default Login**: <EMAIL> / adminpassword

### **File Upload Testing**
```bash
# Login and get session cookie
curl -X POST http://localhost:8000/api/auth/login \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=<EMAIL>&password=adminpassword" \
  -c cookies.txt

# Test file upload
curl -X POST http://localhost:8000/api/media/upload \
  -H "Content-Type: multipart/form-data" \
  -F "file=@your-test-file.jpg" \
  -b cookies.txt
```

## 🔧 **Configuration Details**

### **Environment Variables**
```env
# Database Configuration
DATABASE_URL=postgresql://postgres:password@host:port/database
ASYNC_DATABASE_URL=postgresql+asyncpg://postgres:password@host:port/database
DB_HOST=your-db-host
DB_PORT=5432
DB_NAME=your-database
DB_USER=postgres
DB_PASSWORD=your-password

# Application Settings
YUPCHA_APP_NAME="Yupcha Customer Bot AI"
YUPCHA_SECRET_KEY="your-super-secret-key-for-sessions"
DEBUG=True

# S3/MinIO Storage
YUPCHA_S3__ENDPOINT_URL="https://your-s3-endpoint.com"
YUPCHA_S3__ACCESS_KEY="your-access-key"
YUPCHA_S3__SECRET_KEY="your-secret-key"
YUPCHA_S3__BUCKET_NAME="yupcha-media"
YUPCHA_S3__REGION="us-east-1"

# Server Settings
HOST=0.0.0.0
PORT=8000

# CORS Settings
ALLOWED_ORIGINS=["http://localhost:3000", "http://127.0.0.1:3000"]
```

### **Default Users**
When you run the `setup-defaults` command, the system creates default users:
- **Admin**: <EMAIL> / adminpassword
- **Agent**: <EMAIL> / agentpassword

## 📖 **Documentation Files**

- **FEATURES_CHECKLIST.md** - Complete feature implementation status
- **DATABASE_MANAGEMENT_REPORT.md** - Database setup and management guide

## 🚀 **Production Deployment**

### **Environment Setup**
1. Set `DEBUG=False` in production
2. Use strong `YUPCHA_SECRET_KEY`
3. Configure production PostgreSQL database
4. Set up production S3/MinIO service
5. Configure CORS for your domain
6. Set up HTTPS with proper SSL certificates

### **Security Considerations**
- Change default user passwords immediately
- Use HTTPS in production environments
- Set up proper CORS origins for your domain
- Configure rate limiting for API endpoints
- Set up monitoring and logging
- Regular security updates and patches

## 🎯 **Project Structure**

```
yupcha-customerbot-ai/
├── app/
│   ├── api/endpoints/     # API route handlers (including permissions and role management)
│   ├── auth/             # Authentication logic
│   ├── core/             # Core configuration and utilities (including Casbin enforcer)
│   ├── crud/             # Database operations (including user, role, organization, team CRUD)
│   ├── db/               # Database configuration
│   ├── models/           # SQLAlchemy models
│   ├── schemas/          # Pydantic schemas
│   └── main.py           # Application entry point
├── tests/                # Test scripts and demos
├── .env                  # Environment configuration
└── pyproject.toml        # Project dependencies
```

## 🤝 **Contributing**

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Make your changes
4. Add tests for new features
5. Commit your changes (`git commit -m 'Add amazing feature'`)
6. Push to the branch (`git push origin feature/amazing-feature`)
7. Open a Pull Request

## 📄 **License**

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 **Support & Troubleshooting**

### **Common Issues**
1. **Database Connection**: Check DATABASE_URL and ensure PostgreSQL is running
2. **File Upload Errors**: Verify S3/MinIO configuration and credentials
3. **WebSocket Issues**: Check CORS settings and firewall configuration
4. **Authentication Problems**: Verify SECRET_KEY and session configuration

### **Getting Help**
1. Check the documentation files in the repository
2. Review the API documentation at `/docs`
3. Test with the provided demo scripts
4. Check server logs for detailed error messages
5. Verify environment configuration

---

## 🎉 **Production Ready Features**

✅ **Real-time chat with WebSocket**
✅ **Advanced file upload system**  
✅ **Message deletion and management**
✅ **Organization-wide notifications**
✅ **Multi-tenant architecture**
✅ **Role-based security**
✅ **Production S3 storage**
✅ **Complete API documentation**
✅ **Database migrations**
✅ **Comprehensive testing**

**Your enterprise-level customer support system is ready for production!** 🚀
