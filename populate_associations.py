#!/usr/bin/env python3
"""
Script to populate user-organization and user-team association tables
with existing data from the database.
"""

import asyncio
import sys
import os

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import text
from app.db.session import get_async_db
from app.core.config import settings

async def populate_associations():
    """Populate the association tables with existing data"""
    
    async for db in get_async_db():
        try:
            print("Starting to populate association tables...")
            
            # First, let's see what users exist and their current organization/team assignments
            result = await db.execute(text("""
                SELECT u.id as user_id, u.email, u.full_name,
                       o.id as org_id, o.name as org_name,
                       t.id as team_id, t.name as team_name
                FROM users u
                LEFT JOIN organizations o ON true  -- Get all organizations for now
                LEFT JOIN teams t ON t.organization_id = o.id
                WHERE u.is_deleted = false
                ORDER BY u.email, o.name, t.name
            """))
            
            users_data = result.fetchall()
            print(f"Found {len(users_data)} user-org-team combinations")
            
            # For now, let's assign each user to the first organization and its first team
            processed_users = set()
            
            for row in users_data:
                user_id = row.user_id
                org_id = row.org_id
                team_id = row.team_id
                
                if user_id in processed_users:
                    continue
                    
                if org_id and user_id:
                    # Insert user-organization association
                    await db.execute(text("""
                        INSERT INTO user_organization_association (user_id, organization_id)
                        VALUES (:user_id, :org_id)
                        ON CONFLICT DO NOTHING
                    """), {"user_id": user_id, "org_id": org_id})
                    
                    print(f"Added user {row.email} to organization {row.org_name}")
                    
                    if team_id:
                        # Insert user-team association
                        await db.execute(text("""
                            INSERT INTO user_team_association (user_id, team_id)
                            VALUES (:user_id, :team_id)
                            ON CONFLICT DO NOTHING
                        """), {"user_id": user_id, "team_id": team_id})
                        
                        print(f"Added user {row.email} to team {row.team_name}")
                    
                    processed_users.add(user_id)
                    break  # Only assign to first org/team for now
            
            await db.commit()
            print(f"Successfully processed {len(processed_users)} users")
            
        except Exception as e:
            print(f"Error: {e}")
            await db.rollback()
            raise
        finally:
            await db.close()

if __name__ == "__main__":
    asyncio.run(populate_associations())
