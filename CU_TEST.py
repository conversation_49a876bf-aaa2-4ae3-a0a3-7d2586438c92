# yupcha-customerbot-ai/tests/demo_customer_chat.py

import asyncio
import websockets
import json
import uuid
import aioconsole
import aiohttp

# --- Configuration ---
WS_BASE_URL = "ws://localhost:8000/api/v1/ws"
API_BASE_URL = "http://localhost:8000/api/v1"
CUSTOMER_ID = 'f7b03d72-7282-4080-b932-646483e88b32' #f"demo-customer-{uuid.uuid4().hex[:6]}"
ORGANIZATION_ID = 1 # Assume default organization with ID 1

async def setup_conversation():
    """Creates a new customer and conversation for the test."""
    print("Setting up a new customer and conversation...")
    async with aiohttp.ClientSession() as session:
        # Create a new customer
        customer_payload = {"customer_id": CUSTOMER_ID, "name": "Demo Customer", "organization_id": ORGANIZATION_ID}
        async with session.post(f"{API_BASE_URL}/customers/", json=customer_payload) as resp:
            if resp.status != 200:
                print(f"❌ Failed to create customer: {await resp.text()}")
                return None
            customer = await resp.json()

        # Create a new conversation
        conv_payload = {"customer_id": customer['id'], "organization_id": ORGANIZATION_ID}
        async with session.post(f"{API_BASE_URL}/conversations/public/initiate", json=conv_payload) as resp:
            if resp.status != 200:
                print(f"❌ Failed to create conversation: {await resp.text()}")
                return None
            conversation = await resp.json()
            print(f"✅ Setup complete. Conversation ID: {conversation['id']}")
            return conversation['id']

async def run_chat_client(websocket):
    """Handles sending and receiving messages for a connected client."""
    async def receive_messages():
        try:
            async for message in websocket:
                data = json.loads(message)
                sender = data.get('sender', 'system')
                content = data.get('content', data.get('detail', 'No content'))
                
                # Green for received messages
                print(f"\n\033[92m>> [{sender.upper()}]: {content}\033[0m")
                print(">> Your message: ", end="", flush=True)
        except websockets.ConnectionClosed:
            print("\nConnection closed.")

    async def send_messages():
        while True:
            message_to_send = await aioconsole.ainput(">> Your message: ")
            if message_to_send.lower() in ['quit', 'exit', 'q']:
                break
            await websocket.send(json.dumps({"type": "text", "content": message_to_send}))
    
    receiver_task = asyncio.create_task(receive_messages())
    await send_messages()
    receiver_task.cancel()

async def main():
    conversation_id = await setup_conversation()
    if not conversation_id:
        return

    uri = f"{WS_BASE_URL}/chat/{conversation_id}?customer_id={CUSTOMER_ID}"
    
    print("\n" + "="*50)
    print("🚀 Yupcha Interactive Customer Chat Client")
    print(f"👤 Customer ID: {CUSTOMER_ID}")
    print(f"💬 Conversation ID: {conversation_id}")
    print("--------------------------------------------------")
    print("Type a message and press Enter. Type 'quit' to exit.")
    print("="*50 + "\n")

    try:
        async with websockets.connect(uri) as websocket:
            await run_chat_client(websocket)
    except Exception as e:
        print(f"\n❌ An error occurred: {e}")
    finally:
        print("\n⏹️ Customer client finished.")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Disconnecting...")