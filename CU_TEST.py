# yupcha-customerbot-ai/tests/test_interactive_customer.py

import asyncio
import websockets
import json
import uuid
import aioconsole

# --- Configuration ---
WS_BASE_URL = "ws://localhost:8000/api/ws"
# IMPORTANT: The script will now create a new conversation for each run.
# You can hardcode an ID here if you want to connect to an existing one.
CONVERSATION_ID = 1  # Default, will be updated by the setup function
CUSTOMER_ID = f"interactive-customer-{uuid.uuid4().hex[:6]}"
API_BASE_URL = "http://localhost:8000/api"

async def setup_conversation():
    """Creates a new customer and conversation for the test."""
    import aiohttp
    global CONVERSATION_ID
    
    async with aiohttp.ClientSession() as session:
        # Create a new customer
        customer_payload = {"customer_id": CUSTOMER_ID, "name": "Interactive Customer", "organization_id": 1}
        async with session.post(f"{API_BASE_URL}/customers/", json=customer_payload) as resp:
            if resp.status != 200:
                print(f"❌ Failed to create customer: {await resp.text()}")
                return False
            customer = await resp.json()

        # Create a new conversation
        conv_payload = {"customer_id": customer['id'], "organization_id": 1}
        async with session.post(f"{API_BASE_URL}/conversations/", json=conv_payload) as resp:
            if resp.status != 200:
                print(f"❌ Failed to create conversation: {await resp.text()}")
                return False
            conversation = await resp.json()
            CONVERSATION_ID = conversation['id']
            print(f"✅ Setup complete. Using Conversation ID: {CONVERSATION_ID}")
            return True

async def run_chat_client(websocket):
    """Generic function to handle sending and receiving messages."""
    async def receive_messages():
        try:
            async for message in websocket:
                data = json.loads(message)
                sender = data.get('sender', 'system')
                content = data.get('content', data.get('detail', 'No content'))
                
                # Green for received messages
                print(f"\n\033[92m>> [{sender.upper()}]: {content}\033[0m")
                print(">> Your message: ", end="", flush=True) # Reprint prompt
        except websockets.ConnectionClosed:
            print("\n👂 Listener: Connection closed by server.")

    async def send_messages():
        while True:
            message_to_send = await aioconsole.ainput(">> Your message: ")
            if message_to_send.lower() in ['quit', 'exit', 'q']:
                break
            if not message_to_send:
                continue
            
            payload = {"content": message_to_send}
            await websocket.send(json.dumps(payload))
    
    receiver_task = asyncio.create_task(receive_messages())
    sender_task = asyncio.create_task(send_messages())
    
    await sender_task # Wait for sender to finish (on 'quit')
    receiver_task.cancel() # Clean up listener

async def main():
    if not await setup_conversation():
        return

    uri = f"{WS_BASE_URL}/chat/{CONVERSATION_ID}?customer_id={CUSTOMER_ID}"
    
    print("\n" + "="*50)
    print("🚀 Yupcha Interactive Customer Chat Client")
    print(f"👤 Customer ID: {CUSTOMER_ID}")
    print(f"💬 Conversation ID: {CONVERSATION_ID}")
    print("--------------------------------------------------")
    print("Type a message and press Enter. Type 'quit' to exit.")
    print("="*50 + "\n")

    try:
        async with websockets.connect(uri) as websocket:
            await run_chat_client(websocket)
    except Exception as e:
        print(f"\n❌ An error occurred: {e}")
    finally:
        print("\n⏹️ Customer client finished.")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Disconnecting...")