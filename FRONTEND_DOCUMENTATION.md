# 🌐 Yu<PERSON>cha Customer Bot AI - Frontend Documentation

## 📋 **Table of Contents**
1. [Frontend Overview](#frontend-overview)
2. [Authentication System](#authentication-system)
3. [Navigation & Routes](#navigation--routes)
4. [CRM Features](#crm-features)
5. [Chat System](#chat-system)
6. [WebSocket Integration](#websocket-integration)
7. [User Interface](#user-interface)
8. [Troubleshooting](#troubleshooting)

---

## 🏗️ **Frontend Overview**

### **Technology Stack**
- **Framework**: Vue.js 3 with Composition API
- **Styling**: Custom CSS with CSS Variables (Dark Theme)
- **HTTP Client**: Axios with session cookies
- **Router**: Vue Router with route guards
- **Real-time**: WebSocket connections
- **Icons**: Font Awesome

### **Access Information**
- **URL**: http://localhost:5173/
- **Development Server**: Vite dev server
- **API Backend**: http://localhost:8000

---

## 🔐 **Authentication System**

### **Login Credentials**
```
Admin User (Full Access):
- Email: <EMAIL>
- Password: adminpassword
- Role: agent
- Admin Privileges: true

Agent User (Limited Access):
- Email: <EMAIL>
- Password: agentpassword
- Role: agent
- Admin Privileges: false
```

### **Authentication Flow**
1. **Login**: POST to `/api/auth/login` with form data
2. **Session**: Server sets session cookie (not tokens)
3. **Route Guards**: Check authentication on protected routes
4. **Auto-redirect**: Authenticated users redirect to dashboard
5. **Logout**: POST to `/api/auth/logout` clears session

### **Role-Based Access**
- **All Users**: Dashboard, Conversations, Chat, Customers
- **Admin Only**: Teams, Organizations, Users (CRM features)

---

## 🧭 **Navigation & Routes**

### **Public Routes**
| Route | Component | Description |
|-------|-----------|-------------|
| `/` | Redirect | Redirects to `/login` |
| `/login` | Login.vue | Authentication page |

### **Protected Routes (Requires Login)**
| Route | Component | Access Level | Description |
|-------|-----------|--------------|-------------|
| `/dashboard` | Dashboard.vue | All Users | Main dashboard with stats |
| `/conversations` | Conversations.vue | All Users | List all conversations |
| `/conversations/:id` | Chat.vue | All Users | Single conversation chat |
| `/chat` | Chat.vue | All Users | General chat interface |
| `/customers` | Customers.vue | All Users | Customer management |

### **Admin-Only Routes (Requires is_admin=true)**
| Route | Component | Access Level | Description |
|-------|-----------|--------------|-------------|
| `/teams` | Teams.vue | Admin Only | Team management (CRM) |
| `/organizations` | Organizations.vue | Admin Only | Organization management (CRM) |
| `/users` | Users.vue | Admin Only | User management (CRM) |

### **Route Guards**
- **Authentication Check**: Validates session with `/api/auth/me`
- **Admin Check**: Verifies `user.is_admin === true`
- **Auto-redirect**: Non-admin users redirected to dashboard
- **Guest Protection**: Authenticated users can't access login page

---

## 🏢 **CRM Features (Admin Only)**

### **1. Teams Management (`/teams`)**
**Purpose**: Manage support teams and agent assignments

**Features**:
- ✅ **View Teams**: Grid layout with team cards
- ✅ **Create Team**: Modal form with name, description, organization
- ✅ **Edit Team**: Update team information
- ✅ **Delete Team**: Remove teams with confirmation
- ✅ **Team Stats**: Members count, conversations, organization

**API Endpoints**:
- `GET /api/teams/` - List all teams
- `POST /api/teams/` - Create new team
- `PUT /api/teams/{id}` - Update team
- `DELETE /api/teams/{id}` - Delete team

**Form Fields**:
- Team Name (required)
- Description (optional)
- Organization (required, dropdown)

### **2. Organizations Management (`/organizations`)**
**Purpose**: Manage organizations and their settings

**Features**:
- ✅ **View Organizations**: Grid layout with organization cards
- ✅ **Create Organization**: Comprehensive form
- ✅ **Edit Organization**: Update organization details
- ✅ **Delete Organization**: Remove with confirmation
- ✅ **Organization Stats**: Teams, conversations, users count
- ✅ **Contact Info**: Website links, email display

**API Endpoints**:
- `GET /api/organizations/` - List all organizations
- `POST /api/organizations/` - Create new organization
- `PUT /api/organizations/{id}` - Update organization
- `DELETE /api/organizations/{id}` - Delete organization

**Form Fields**:
- Organization Name (required)
- Description (optional)
- Website (URL, optional)
- Contact Email (email, optional)
- Phone (tel, optional)
- Address (textarea, optional)

### **3. Users Management (`/users`)**
**Purpose**: Manage system users and their permissions

**Features**:
- ✅ **View Users**: Table layout with user information
- ✅ **Create User**: Complete user creation form
- ✅ **Edit User**: Update user details (no password change)
- ✅ **Toggle Status**: Activate/deactivate users
- ✅ **Delete User**: Remove users with confirmation
- ✅ **Role Display**: Shows agent role + admin badge
- ✅ **Team Assignment**: Assign users to teams

**API Endpoints**:
- `GET /api/users/` - List all users
- `POST /api/users/` - Create new user
- `PUT /api/users/{id}` - Update user
- `PATCH /api/users/{id}` - Update user status
- `DELETE /api/users/{id}` - Delete user

**Form Fields**:
- Full Name (required)
- Email Address (required, disabled in edit mode)
- Initial Password (required for new users only)
- Role (always "agent")
- Admin Privileges (boolean: Regular Agent / Admin Agent)
- Organization (required, dropdown)
- Team (optional, dropdown)
- Status (Active/Inactive)

**New Role System**:
- All users have `role: "agent"`
- Admin privileges controlled by `is_admin: true/false`
- UI shows "Agent" + "Admin" badge for admin users

---

## 💬 **Chat System**

### **1. Conversations List (`/conversations`)**
**Purpose**: View and manage all customer conversations

**Features**:
- ✅ **Conversation List**: Sorted by most recent activity (`updated_at`)
- ✅ **Customer Info**: Name, email, conversation status
- ✅ **Team Assignment**: Shows assigned team or "Unassigned"
- ✅ **Status Badges**: New, Active, Pending, Resolved, Closed
- ✅ **Time Display**: Shows last activity time (updated_at)
- ✅ **Click to Chat**: Opens conversation in chat interface
- ✅ **Refresh**: Manual refresh button
- ✅ **Create Conversation**: New conversation button

**API Endpoints**:
- `GET /api/conversations/` - List conversations (admin)
- `GET /api/conversations/team/{id}` - Team conversations
- `GET /api/conversations/unassigned` - Unassigned conversations
- `GET /api/conversations/customer/{id}` - Customer conversations

### **2. Single Chat (`/conversations/:id` or `/chat`)**
**Purpose**: Real-time chat interface for agent-customer communication

**Features**:
- ✅ **Message History**: Loads complete conversation history
- ✅ **Real-time Messaging**: WebSocket-based instant communication
- ✅ **Message Types**: Customer, Agent, Bot, System messages
- ✅ **Visual Distinction**: Different styling for each sender type
- ✅ **Connection Status**: Shows WebSocket connection state
- ✅ **Auto-scroll**: Automatically scrolls to latest messages
- ✅ **Send Messages**: Text input with send button
- ✅ **Conversation Info**: Customer details in header

**Message Flow**:
1. **Load History**: GET `/api/conversations/{id}/messages`
2. **WebSocket Connect**: `ws://localhost:8000/api/ws/chat/{id}`
3. **Send Message**: Via WebSocket or POST `/api/conversations/{id}/messages`
4. **Receive Messages**: Real-time via WebSocket
5. **Agent Presence**: Marks agent as active (stops bot responses)

**Message Types & Styling**:
- **Customer Messages**: Left-aligned, gray background
- **Agent Messages**: Right-aligned, blue background
- **Bot Messages**: Left-aligned, secondary color background
- **System Messages**: Center-aligned, italic, muted

---

## 🔌 **WebSocket Integration**

### **Connection Types**
1. **Agent WebSocket**: `ws://localhost:8000/api/ws/chat/{conversation_id}`
   - Requires session cookie authentication
   - Marks agent as active in conversation
   - Stops bot from responding

2. **Customer WebSocket**: `ws://localhost:8000/api/ws/chat/{conversation_id}?customer_id={id}`
   - No authentication required
   - Creates customer if doesn't exist

### **Message Format**
```json
{
  "content": "Hello!",
  "sender": "agent|customer|bot|system",
  "message_type": "text",
  "conversation_id": 1,
  "created_at": "2025-06-19T15:30:00Z"
}
```

### **WebSocket Events**
- **Connection**: Establishes real-time communication
- **Message Send**: Sends message to conversation
- **Message Receive**: Receives messages from other participants
- **Agent Presence**: Tracks agent activity for bot logic
- **Disconnection**: Cleans up agent presence

### **Bot Intelligence**
- **Bot Responds**: When no agents connected to conversation
- **Bot Stops**: When any agent connects to conversation
- **Seamless Handoff**: Automatic transition between bot and human

---

## 🎨 **User Interface**

### **Design System**
- **Theme**: Complete dark theme implementation
- **Layout**: Sidebar navigation (collapsible)
- **Colors**: CSS variables for consistent theming
- **Typography**: Clean, readable fonts
- **Icons**: Font Awesome icons throughout
- **Responsive**: Mobile-friendly design

### **Navigation Structure**
```
Sidebar Navigation:
├── Dashboard (📊)
├── Conversations (💬)
├── Live Chat (💭)
├── Customers (👥)
└── Administration (⚙️) [Admin Only]
    ├── Teams (👥)
    ├── Organizations (🏢)
    └── Users (👤)
```

### **Key UI Components**
- **Sidebar**: Collapsible navigation with icons
- **Cards**: Consistent card layout for data display
- **Tables**: Responsive tables with hover effects
- **Modals**: Form modals for create/edit operations
- **Toasts**: Success/error notifications
- **Badges**: Status indicators and role badges
- **Loading States**: Spinners and loading indicators

### **Dark Theme Features**
- **Background**: Dark backgrounds throughout
- **Text**: High contrast text colors
- **Borders**: Subtle border colors
- **Hover Effects**: Smooth transitions
- **Form Elements**: Dark-themed inputs and selects
- **Buttons**: Consistent button styling

---

## 🔧 **Troubleshooting**

### **Common Issues**

**1. Admin Navigation Missing**
- **Problem**: Teams/Organizations/Users not showing in sidebar
- **Solution**: Check user has `is_admin: true` (not `role: "admin"`)
- **Fix**: <NAME_EMAIL> or update user in database

**2. WebSocket Connection Failed**
- **Problem**: Chat not working, connection status shows disconnected
- **Solution**: Check backend is running on port 8000
- **Fix**: Start backend with `uv run uvicorn app.main:app --reload --host 0.0.0.0 --port 8000`

**3. API Calls Failing**
- **Problem**: 401 Unauthorized errors
- **Solution**: Session cookie expired or not set
- **Fix**: Re-login to refresh session

**4. Route Access Denied**
- **Problem**: Redirected to dashboard when accessing admin routes
- **Solution**: User doesn't have admin privileges
- **Fix**: Update user `is_admin` field to `true`

### **Development Commands**
```bash
# Start frontend development server
cd frontend
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview
```

### **Browser Developer Tools**
- **Console**: Check for JavaScript errors
- **Network**: Monitor API calls and WebSocket connections
- **Application**: Check session cookies
- **Vue DevTools**: Debug Vue components and state

---

## 🎯 **Summary**

The frontend provides a complete CRM and chat system with:
- ✅ **Authentication**: Session-based login with role-based access
- ✅ **CRM Features**: Teams, Organizations, Users management (admin only)
- ✅ **Chat System**: Real-time messaging with WebSocket integration
- ✅ **Dark Theme**: Complete dark mode implementation
- ✅ **Responsive Design**: Works on desktop and mobile
- ✅ **Route Protection**: Proper authentication and authorization
- ✅ **Real-time Updates**: WebSocket-based live communication

**Ready for production use with all requested features implemented!** 🚀
