# 🚀 Yu<PERSON>cha Customer Bot AI - Complete Chat System Documentation

## 📋 **Table of Contents**
1. [System Overview](#system-overview)
2. [User Role System](#user-role-system)
3. [Customer Chat (Headless)](#customer-chat-headless)
4. [Agent/Admin Chat (Frontend)](#agentadmin-chat-frontend)
5. [API Endpoints](#api-endpoints)
6. [WebSocket Communication](#websocket-communication)
7. [Testing Guide](#testing-guide)
8. [Troubleshooting](#troubleshooting)

---

## 🏗️ **System Overview**

### **Architecture**
- **Backend**: FastAPI with PostgreSQL database
- **Frontend**: Vue.js with dark theme
- **Real-time**: WebSocket connections with Redis pub/sub
- **Authentication**: Session cookies (not tokens)
- **Bot Integration**: AI bot responds when no agents are active

### **Key Features**
- ✅ **Unified Role System**: All users are agents, some have admin privileges
- ✅ **Real-time Chat**: WebSocket connections for instant messaging
- ✅ **Bot Intelligence**: Automatic responses when no human agents available
- ✅ **Conversation Tracking**: Updated timestamps for proper sorting
- ✅ **Message History**: Complete chat history loading
- ✅ **Agent Presence**: Bot stops responding when agents are active

---

## 👥 **User Role System**

### **New Unified System**
```json
{
  "role": "agent",        // All users are agents
  "is_admin": true/false  // Some have admin privileges
}
```

### **User Types**
1. **Agent**: `role="agent"`, `is_admin=false`
   - Can chat with customers
   - Can view assigned conversations
   - Limited admin access

2. **Admin**: `role="agent"`, `is_admin=true`
   - Can chat with customers (like agents)
   - Full admin dashboard access
   - Can manage users, teams, organizations
   - Can view all conversations

### **Migration Applied**
- ✅ All existing users converted to `role="agent"`
- ✅ Previous admins now have `is_admin=true`
- ✅ Previous agents now have `is_admin=false`

---

## 💬 **Customer Chat (Headless)**

### **How to Use**
```bash
# Start the customer chat client
cd /path/to/yupcha-customerbot-ai
uv run python tests/test_customer_chat_client.py
```

### **Customer Experience**
1. **Auto-connect**: Automatically connects to conversation ID 1
2. **Message History**: Loads all previous messages on connect
3. **Real-time**: Receives messages instantly via WebSocket
4. **Bot Responses**: AI bot responds when no agents are active
5. **Agent Handoff**: Human agents can take over seamlessly

### **Customer Chat Features**
- ✅ **Automatic Customer Creation**: Creates customer if doesn't exist
- ✅ **Message History Loading**: Shows all previous conversation
- ✅ **Real-time Messaging**: WebSocket-based instant communication
- ✅ **Bot Integration**: AI responses when no agents available
- ✅ **Agent Detection**: Bot stops when human agents join

### **Example Customer Session**
```
==================================================
🚀 Yupcha Interactive Customer Chat Client
==================================================
👤 Acting as Customer ID: interactive-customer-abc123
💬 Connecting to Conversation ID: 1
--------------------------------------------------
✅ Connection successful! You can start chatting.

[CUSTOMER]: Hello, I need help with my order
[BOT]: Thank you for contacting us. How can I help you today?

[CUSTOMER]: My order #12345 is late
[AGENT]: Hello! I'm here to help with your order. Let me check that for you.
```

---

## 🖥️ **Agent/Admin Chat (Frontend)**

### **Access URLs**
- **Frontend**: http://localhost:5173/
- **Login Page**: http://localhost:5173/login
- **Conversations**: http://localhost:5173/conversations
- **Single Chat**: http://localhost:5173/chat/:id

### **Login Credentials**
```
Admin User:
- Email: <EMAIL>
- Password: adminpassword
- Role: agent (with is_admin=true)

Agent User:
- Email: <EMAIL>  
- Password: agentpassword
- Role: agent (with is_admin=false)
```

### **Frontend Features**
- ✅ **Dark Theme**: Complete dark mode implementation
- ✅ **Conversation List**: Shows all conversations sorted by latest activity
- ✅ **Real-time Chat**: WebSocket-based messaging
- ✅ **Message History**: Loads complete conversation history
- ✅ **Agent Presence**: Marks agent as active when connected
- ✅ **Responsive Design**: Works on desktop and mobile

### **How Agents Chat**
1. **Login**: Use admin/agent credentials
2. **View Conversations**: See list sorted by most recent activity
3. **Open Chat**: Click on conversation to open chat window
4. **Send Messages**: Type and send messages in real-time
5. **Bot Handoff**: Bot automatically stops responding when agent joins

---

## 🔌 **API Endpoints**

### **Authentication**
```bash
# Login (creates session cookie)
POST /api/auth/login
Content-Type: application/x-www-form-urlencoded
Body: username=<EMAIL>&password=adminpassword

# Get current user info
GET /api/auth/me
Cookie: session_cookie
```

### **Conversations**
```bash
# Get all conversations (admin only)
GET /api/conversations/
Cookie: session_cookie

# Get specific conversation
GET /api/conversations/{id}
Cookie: session_cookie

# Get conversation messages
GET /api/conversations/{id}/messages
Cookie: session_cookie

# Create message (NEW ENDPOINT)
POST /api/conversations/{id}/messages
Cookie: session_cookie
Content-Type: application/json
Body: {
  "content": "Hello customer!",
  "sender": "agent",
  "message_type": "text"
}
```

### **WebSocket**
```bash
# Customer WebSocket
ws://localhost:8000/api/ws/chat/{conversation_id}?customer_id={customer_id}

# Agent WebSocket  
ws://localhost:8000/api/ws/chat/{conversation_id}
Cookie: session_cookie
```

---

## 🌐 **WebSocket Communication**

### **Message Format**
```json
{
  "content": "Hello!",
  "sender": "customer|agent|bot",
  "message_type": "text",
  "conversation_id": 1,
  "created_at": "2025-06-19T15:30:00Z"
}
```

### **Connection Types**
1. **Customer Connection**: 
   - URL: `ws://localhost:8000/api/ws/chat/1?customer_id=customer-123`
   - No authentication required
   - Automatically creates customer if needed

2. **Agent Connection**:
   - URL: `ws://localhost:8000/api/ws/chat/1`
   - Requires session cookie authentication
   - Marks agent as active (stops bot responses)

### **Bot Logic**
- **Bot Responds**: When no agents are connected to conversation
- **Bot Stops**: When any agent connects to conversation
- **Agent Presence**: Tracked in Redis with conversation-specific keys
- **Automatic Handoff**: Seamless transition between bot and human

---

## 🧪 **Testing Guide**

### **1. Start Backend**
```bash
cd /path/to/yupcha-customerbot-ai
uv run uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### **2. Start Frontend**
```bash
cd /path/to/yupcha-customerbot-ai/frontend
npm run dev
```

### **3. Test Customer Chat**
```bash
cd /path/to/yupcha-customerbot-ai
uv run python tests/test_customer_chat_client.py
```

### **4. Test Admin Response**
1. Open browser: http://localhost:5173/
2. <NAME_EMAIL> / adminpassword
3. Go to Conversations
4. Click on conversation to open chat
5. Send message to customer

### **5. Test API Endpoints**
```bash
# Login as admin
curl -X POST "http://localhost:8000/api/auth/login" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=<EMAIL>&password=adminpassword" \
  -c cookies.txt

# Send message as agent
curl -X POST "http://localhost:8000/api/conversations/1/messages" \
  -H "Content-Type: application/json" \
  -b cookies.txt \
  -d '{
    "content": "Hello from API!",
    "sender": "agent", 
    "message_type": "text"
  }'
```

---

## ⚡ **Key Improvements Made**

### **1. User Role System Fixed**
- ✅ All users are now agents (`role="agent"`)
- ✅ Admin privileges via `is_admin` field
- ✅ Admins can chat like agents
- ✅ Database migration applied successfully

### **2. Conversation Updates Fixed**
- ✅ `updated_at` field updates on new messages
- ✅ Conversations sorted by most recent activity
- ✅ Frontend shows correct timestamps
- ✅ API returns properly ordered conversations

### **3. Frontend Improvements**
- ✅ Shows `updated_at` instead of `created_at`
- ✅ Real-time WebSocket connections
- ✅ Complete message history loading
- ✅ Dark theme implementation

### **4. New API Endpoint**
- ✅ `POST /api/conversations/{id}/messages`
- ✅ Allows agents to send messages via API
- ✅ Proper authentication required
- ✅ Updates conversation timestamps

---

## 🔧 **System Status**

### **✅ Working Features**
- Backend API (http://localhost:8000)
- Frontend UI (http://localhost:5173)
- Customer chat client
- Admin/Agent authentication
- WebSocket real-time messaging
- Bot intelligence and handoff
- Conversation timestamp updates
- Message history loading
- Agent presence detection

### **🎯 Ready for Production**
The system is fully functional with:
- Unified role system
- Real-time chat capabilities
- Intelligent bot integration
- Complete message history
- Proper conversation sorting
- WebSocket-based architecture
- Session-based authentication

---

## 🚨 **Known Issues & Solutions**

### **Issue: REST API Messages Not Broadcasting**
**Problem**: Messages sent via `POST /api/conversations/{id}/messages` don't broadcast to WebSocket connections in real-time.

**Solution**: The REST API endpoint creates messages in the database but doesn't broadcast to WebSocket connections. For real-time messaging, use WebSocket connections directly.

**Workaround**:
1. Use WebSocket for real-time agent responses
2. Use REST API for programmatic message creation
3. Frontend can refresh message history after API calls

### **WebSocket vs REST API**
- **WebSocket**: Real-time bidirectional communication, broadcasts to all connected clients
- **REST API**: Database operations only, no real-time broadcasting
- **Best Practice**: Use WebSocket for chat, REST API for bulk operations

---

## 🔄 **Message Flow**

### **Customer → Agent (Real-time)**
1. Customer sends via WebSocket
2. Message saved to database
3. Broadcast to all connected agents
4. Agent receives instantly

### **Agent → Customer (WebSocket)**
1. Agent sends via WebSocket (frontend)
2. Message saved to database
3. Broadcast to customer WebSocket
4. Customer receives instantly

### **Agent → Customer (REST API)**
1. Agent sends via REST API
2. Message saved to database
3. ⚠️ No real-time broadcast
4. Customer sees on next page refresh

---

## 📱 **Frontend Status**

### **✅ Working Features**
- Login/logout functionality
- Conversation list with proper sorting
- Real-time chat interface
- Message history loading
- WebSocket connections
- Dark theme UI

### **🔧 Potential Improvements**
- Add message broadcasting from REST API
- Implement typing indicators
- Add file upload support
- Enhanced error handling
- Mobile responsiveness improvements
