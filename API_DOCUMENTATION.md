# 🚀 Yupcha Customer Bot AI - Complete API Documentation

## 📋 Table of Contents
- [Overview](#overview)
- [Authentication](#authentication)
- [Base URLs](#base-urls)
- [API Endpoints](#api-endpoints)
  - [Authentication Endpoints](#authentication-endpoints)
  - [User Management](#user-management)
  - [Organization Management](#organization-management)
  - [Team Management](#team-management)
  - [Customer Management](#customer-management)
  - [Conversation Management](#conversation-management)
  - [Message Management](#message-management)
  - [WebSocket Endpoints](#websocket-endpoints)
  - [Notification System](#notification-system)
  - [Media/File Upload](#media-file-upload)
  - [Admin Endpoints](#admin-endpoints)
- [Error Handling](#error-handling)
- [Rate Limiting](#rate-limiting)

## 🌟 Overview

Yupcha Customer Bot AI is a scalable, Redis-based customer support chat system with real-time messaging, intelligent bot responses, and comprehensive notification system.

### Key Features
- ✅ **Session-based Authentication** (no tokens)
- ✅ **Real-time WebSocket Communication** 
- ✅ **Redis-based Horizontal Scaling**
- ✅ **Intelligent Bot Responses**
- ✅ **Email Notifications**
- ✅ **File Upload Support**
- ✅ **Role-based Access Control**

## 🔐 Authentication

The API uses **session-based authentication** with HTTP-only cookies. No JWT tokens are used.

### Authentication Flow
1. **Login** → Receive session cookie
2. **Include cookie** in all subsequent requests
3. **Logout** → Session invalidated

### Roles
- **`admin`**: Full system access
- **`agent`**: Customer support access
- **`customer`**: Limited access (via WebSocket)

## 🌐 Base URLs

- **Backend API**: `http://localhost:8000`
- **Frontend**: `http://localhost:5173`
- **WebSocket**: `ws://localhost:8000`

## 📡 API Endpoints

### 🔑 Authentication Endpoints

#### POST `/api/auth/login`
**Purpose**: Authenticate user and create session

**Request Body**:
```json
{
  "email": "<EMAIL>",
  "password": "adminpassword"
}
```

**Response**:
```json
{
  "message": "Login successful",
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "role": "admin",
    "is_active": true
  }
}
```

**Usage Example**:
```bash
curl -X POST "http://localhost:8000/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "adminpassword"}' \
  -c cookies.txt
```

#### POST `/api/auth/logout`
**Purpose**: Invalidate current session

**Response**:
```json
{
  "message": "Logout successful"
}
```

#### GET `/api/auth/me`
**Purpose**: Get current user information

**Response**:
```json
{
  "id": 1,
  "email": "<EMAIL>",
  "role": "admin",
  "is_active": true,
  "organization_id": 1
}
```

### 👥 User Management

#### GET `/api/users/`
**Purpose**: List all users (Admin only)
**Access**: Admin required

**Query Parameters**:
- `skip`: Number of records to skip (default: 0)
- `limit`: Maximum records to return (default: 100)

**Response**:
```json
[
  {
    "id": 1,
    "email": "<EMAIL>",
    "role": "admin",
    "is_active": true,
    "organization_id": 1,
    "team_id": null
  }
]
```

#### POST `/api/users/`
**Purpose**: Create new user (Admin only)
**Access**: Admin required

**Request Body**:
```json
{
  "email": "<EMAIL>",
  "password": "agentpassword",
  "role": "agent",
  "organization_id": 1,
  "team_id": 1
}
```

#### GET `/api/users/{user_id}`
**Purpose**: Get specific user details
**Access**: Admin or own profile

#### PUT `/api/users/{user_id}`
**Purpose**: Update user information
**Access**: Admin or own profile

#### DELETE `/api/users/{user_id}`
**Purpose**: Delete user (Admin only)
**Access**: Admin required

### 🏢 Organization Management

#### GET `/api/organizations/`
**Purpose**: List all organizations (Admin only)

#### POST `/api/organizations/`
**Purpose**: Create new organization (Admin only)

**Request Body**:
```json
{
  "name": "Acme Corp",
  "description": "Customer support for Acme Corp"
}
```

#### GET `/api/organizations/{org_id}`
**Purpose**: Get organization details

#### PUT `/api/organizations/{org_id}`
**Purpose**: Update organization

### 👨‍👩‍👧‍👦 Team Management

#### GET `/api/teams/`
**Purpose**: List teams in organization

#### POST `/api/teams/`
**Purpose**: Create new team (Admin only)

**Request Body**:
```json
{
  "name": "Support Team Alpha",
  "description": "Primary customer support team",
  "organization_id": 1
}
```

#### GET `/api/teams/{team_id}/members`
**Purpose**: Get team members

### 👤 Customer Management

#### GET `/api/customers/`
**Purpose**: List customers (Agent/Admin access)

#### POST `/api/customers/`
**Purpose**: Create new customer

**Request Body**:
```json
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "phone": "+1234567890",
  "organization_id": 1
}
```

#### GET `/api/customers/{customer_id}`
**Purpose**: Get customer details

#### PUT `/api/customers/{customer_id}`
**Purpose**: Update customer information

### 💬 Conversation Management

#### GET `/api/conversations/`
**Purpose**: List conversations with pagination
**Access**: Admin required

**Query Parameters**:
- `skip`: Records to skip (default: 0)
- `limit`: Max records (default: 100)

**Response**:
```json
[
  {
    "id": 1,
    "customer_id": 1,
    "organization_id": 1,
    "assigned_team_id": 1,
    "status": "active",
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-15T10:35:00Z"
  }
]
```

#### POST `/api/conversations/`
**Purpose**: Create new conversation

**Request Body**:
```json
{
  "customer_id": 1,
  "organization_id": 1,
  "assigned_team_id": 1
}
```

**What happens**:
1. ✅ Conversation created in database
2. ✅ Real-time notification sent to agents via WebSocket
3. ✅ Email notification sent to assigned team
4. ✅ Auto-assignment to default team if not specified

#### GET `/api/conversations/{conversation_id}`
**Purpose**: Get specific conversation details

#### GET `/api/conversations/{conversation_id}/messages`
**Purpose**: Get all messages in conversation

**Response**:
```json
[
  {
    "id": 1,
    "conversation_id": 1,
    "sender_type": "customer",
    "sender_id": "customer-123",
    "content": "Hello, I need help!",
    "message_type": "text",
    "created_at": "2024-01-15T10:30:00Z"
  }
]
```

#### GET `/api/conversations/unassigned`
**Purpose**: Get unassigned conversations
**Access**: Agent/Admin required

#### GET `/api/conversations/team/{team_id}`
**Purpose**: Get conversations for specific team
**Access**: Team members or Admin

#### GET `/api/conversations/customer/{customer_id}`
**Purpose**: Get all conversations for a customer

#### POST `/api/conversations/{conversation_id}/assign/{team_id}`
**Purpose**: Assign conversation to team (Admin only)

### 📨 Message Management

#### GET `/api/messages/conversation/{conversation_id}`
**Purpose**: Get messages for conversation (same as above)

#### POST `/api/messages/`
**Purpose**: Send new message

**Request Body**:
```json
{
  "conversation_id": 1,
  "content": "Hello! How can I help you?",
  "sender_type": "agent",
  "message_type": "text"
}
```

**Message Types**:
- `text`: Plain text message
- `image`: Image attachment
- `file`: File attachment
- `system`: System notification

### 🔌 WebSocket Endpoints

#### Customer Chat WebSocket
**URL**: `ws://localhost:8000/api/ws/chat/{conversation_id}?customer_id={customer_id}`

**Purpose**: Real-time chat for customers

**Connection Flow**:
1. Customer connects with conversation_id and customer_id
2. Receives all message history
3. Can send/receive messages in real-time
4. Bot responds when no agents are active

**Message Format**:
```json
{
  "type": "message",
  "data": {
    "content": "Hello!",
    "sender_type": "customer",
    "message_type": "text"
  }
}
```

#### Agent Notifications WebSocket  
**URL**: `ws://localhost:8000/api/ws/notifications?token={session_token}`

**Purpose**: Real-time notifications for agents

**Receives**:
- New conversation alerts
- New message notifications  
- System notifications

#### Agent Chat WebSocket
**URL**: `ws://localhost:8000/api/ws/agent/chat/{conversation_id}?token={session_token}`

**Purpose**: Real-time chat for agents

**Features**:
- Send/receive messages
- See typing indicators
- Real-time message updates

### 🔔 Notification System

#### GET `/api/notifications/`
**Purpose**: Get recent notifications for current user

**Query Parameters**:
- `limit`: Max notifications (default: 50)

#### POST `/api/notifications/mark-read/{notification_id}`
**Purpose**: Mark notification as read

#### POST `/api/notifications/test`
**Purpose**: Send test notification (Development only)

### 📎 Media/File Upload

#### POST `/api/media/upload`
**Purpose**: Upload files (images, documents)

**Request**: Multipart form data
```
Content-Type: multipart/form-data
file: [binary file data]
```

**Response**:
```json
{
  "file_id": "uuid-here",
  "filename": "document.pdf",
  "file_url": "http://localhost:9000/yupcha-assets/uuid-here",
  "file_type": "application/pdf",
  "file_size": 1024000
}
```

#### GET `/api/media/{file_id}`
**Purpose**: Get file metadata

#### GET `/api/media/{file_id}/download`
**Purpose**: Download file

### ⚙️ Admin Endpoints

#### GET `/api/admin/stats`
**Purpose**: Get system statistics (Admin only)

**Response**:
```json
{
  "total_conversations": 150,
  "active_conversations": 25,
  "total_customers": 89,
  "total_agents": 12,
  "messages_today": 450
}
```

#### GET `/api/admin/health`
**Purpose**: System health check

**Response**:
```json
{
  "status": "healthy",
  "database": "connected",
  "redis": "connected",
  "s3": "connected"
}
```

## ⚠️ Error Handling

### Standard Error Response
```json
{
  "detail": "Error message here"
}
```

### Common HTTP Status Codes
- `200`: Success
- `201`: Created
- `400`: Bad Request
- `401`: Unauthorized
- `403`: Forbidden
- `404`: Not Found
- `422`: Validation Error
- `500`: Internal Server Error

### Validation Errors
```json
{
  "detail": [
    {
      "loc": ["body", "email"],
      "msg": "field required",
      "type": "value_error.missing"
    }
  ]
}
```

## 🚦 Rate Limiting

Currently no rate limiting is implemented, but can be added using:
- `slowapi` for FastAPI
- Redis-based rate limiting
- Per-user/IP limits

## 🧪 Testing the API

### Using cURL

1. **Login and save session**:
```bash
curl -X POST "http://localhost:8000/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "adminpassword"}' \
  -c cookies.txt
```

2. **Use session for authenticated requests**:
```bash
curl -X GET "http://localhost:8000/api/conversations/" \
  -b cookies.txt
```

3. **Create a conversation**:
```bash
curl -X POST "http://localhost:8000/api/conversations/" \
  -H "Content-Type: application/json" \
  -d '{"customer_id": 1, "organization_id": 1}' \
  -b cookies.txt
```

### Using the Frontend

1. **Open**: http://localhost:5173/
2. **Login**: <EMAIL> / adminpassword
3. **Navigate**: Use the sidebar to access different sections
4. **Chat**: Click on conversations to start chatting

### Using WebSocket (Customer)

```bash
# Run the customer chat client
cd /home/<USER>/Documents/Devlopment/yupcha-customerbot-ai
uv run python tests/test_customer_chat_client.py
```

## 🔧 Configuration

### Environment Variables (.env)
```bash
# Database
DATABASE_URL=postgresql+asyncpg://user:pass@localhost:5432/db

# Redis  
REDIS_URL=redis://localhost:6379/0

# Email
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-password
MAIL_FROM=<EMAIL>
MAIL_SERVER=smtp.gmail.com

# S3/MinIO
S3_ENDPOINT_URL=http://localhost:9000
S3_ACCESS_KEY_ID=minioadmin
S3_SECRET_ACCESS_KEY=minioadmin
```

## 🚀 Production Deployment

### Docker Compose
```yaml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=postgresql+asyncpg://...
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - postgres
      - redis
      
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: yupcha_db
      
  redis:
    image: redis:7-alpine
```

### Scaling
- Run multiple app instances behind load balancer
- Use Redis cluster for high availability
- Separate read/write database replicas

## 🤖 Bot Intelligence

### How the Bot Works
1. **Agent Presence Detection**: Bot only responds when no agents are active
2. **Keyword Recognition**: Responds based on message content
3. **Redis-based Tracking**: Agent activity tracked in Redis with TTL
4. **Fallback Responses**: Random helpful responses when no keywords match

### Bot Response Examples
- **"help"** or **"support"** → "I'm here to help! What specific issue are you facing?"
- **"agent"** or **"human"** → "I'll connect you with a human agent right away."
- **"price"** or **"cost"** → "For pricing information, please visit our website or speak with an agent."

## 📧 Email Notification System

### Notification Types
1. **New Conversation**: Sent to assigned team when conversation is created
2. **New Message**: Can be configured for urgent messages
3. **System Alerts**: Admin notifications for system events

### Email Templates
- **Location**: `templates/new_conversation.html`
- **Engine**: Jinja2 templating
- **Styling**: Responsive HTML with inline CSS
- **Customizable**: Easy to modify for different notification types

### Email Configuration
```python
# In .env file
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_FROM=<EMAIL>
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
ENABLE_EMAIL_NOTIFICATIONS=true
```

## 🔄 Real-time Architecture

### Redis Pub/Sub Channels
- **`conversation:{id}`**: Messages for specific conversation
- **`organization:{id}:notifications`**: Org-wide notifications
- **`agent:{id}:presence`**: Agent activity tracking

### WebSocket Connection Flow
```
1. Client connects → Authentication check
2. Subscribe to Redis channels → Real-time updates
3. Send/receive messages → Broadcast via Redis
4. Disconnect → Clean up subscriptions
```

### Horizontal Scaling
- Multiple backend instances can run simultaneously
- Redis handles message distribution
- Load balancer distributes WebSocket connections
- Session affinity not required

## 🛠️ Development Workflow

### Starting the System
```bash
# 1. Start Redis
sudo systemctl start redis-server

# 2. Start Backend
cd /home/<USER>/Documents/Devlopment/yupcha-customerbot-ai
uv run uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# 3. Start Frontend
cd frontend
npm run dev

# 4. Test Customer Chat
uv run python tests/test_customer_chat_client.py
```

### Database Migrations
```bash
# Create migration
alembic revision --autogenerate -m "Add new feature"

# Apply migration
alembic upgrade head
```

### Adding New Endpoints
1. Create endpoint in `app/api/endpoints/`
2. Add to router in `app/api/api.py`
3. Update this documentation
4. Add tests

## 🧪 Testing Guide

### Manual Testing Checklist
- [ ] Login/logout works
- [ ] Create conversation triggers notifications
- [ ] Customer can send messages via WebSocket
- [ ] Agent receives real-time notifications
- [ ] Bot responds when no agents active
- [ ] Email notifications sent to teams
- [ ] File upload/download works
- [ ] All CRUD operations work

### Automated Testing
```bash
# Run all tests
pytest

# Run specific test file
pytest tests/test_auth.py

# Run with coverage
pytest --cov=app tests/
```

## 🔒 Security Considerations

### Authentication Security
- Session cookies are HTTP-only
- CSRF protection via SameSite cookies
- Password hashing with bcrypt
- Session timeout after inactivity

### API Security
- Input validation with Pydantic
- SQL injection protection via SQLAlchemy ORM
- File upload restrictions
- Rate limiting (can be added)

### WebSocket Security
- Session-based authentication
- Connection validation
- Message sanitization
- Automatic cleanup on disconnect

## 📊 Monitoring & Logging

### Log Levels
- **INFO**: Normal operations
- **WARNING**: Potential issues
- **ERROR**: Actual problems
- **DEBUG**: Detailed debugging info

### Key Metrics to Monitor
- Active WebSocket connections
- Message throughput
- Database connection pool
- Redis memory usage
- Email delivery success rate

### Health Checks
```bash
# Check backend health
curl http://localhost:8000/api/admin/health

# Check Redis
redis-cli ping

# Check database
psql -h localhost -U yupcha_user -d yupcha_db -c "SELECT 1;"
```

## 🚀 Production Deployment

### Environment Setup
1. **Database**: PostgreSQL with connection pooling
2. **Cache**: Redis cluster for high availability
3. **Storage**: S3-compatible object storage
4. **Email**: Production SMTP service (SendGrid, AWS SES)
5. **Load Balancer**: Nginx or AWS ALB
6. **Monitoring**: Prometheus + Grafana

### Docker Deployment
```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8000

CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### Kubernetes Deployment
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: yupcha-api
spec:
  replicas: 3
  selector:
    matchLabels:
      app: yupcha-api
  template:
    metadata:
      labels:
        app: yupcha-api
    spec:
      containers:
      - name: api
        image: yupcha/api:latest
        ports:
        - containerPort: 8000
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: yupcha-secrets
              key: database-url
```

## 🔧 Troubleshooting

### Common Issues

#### WebSocket Connection Failed
```bash
# Check if backend is running
curl http://localhost:8000/api/admin/health

# Check Redis connection
redis-cli ping

# Check browser console for errors
```

#### Email Not Sending
```bash
# Check email configuration
grep MAIL_ .env

# Check logs for email errors
tail -f logs/app.log | grep email

# Test email service
curl -X POST http://localhost:8000/api/notifications/test
```

#### Database Connection Issues
```bash
# Check database status
systemctl status postgresql

# Test connection
psql -h localhost -U yupcha_user -d yupcha_db

# Check connection pool
# Look for "pool" in application logs
```

### Performance Optimization
- Use database indexes on frequently queried columns
- Implement Redis caching for read-heavy operations
- Use connection pooling for database
- Compress WebSocket messages for large payloads
- Implement pagination for large result sets

---

**🎯 This comprehensive API documentation covers everything you need to understand, use, and deploy the Yupcha Customer Bot AI system!**

**📞 For support or questions, contact the development team or check the GitHub repository.**
