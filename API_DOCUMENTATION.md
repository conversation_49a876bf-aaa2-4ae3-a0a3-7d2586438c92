# 🚀 Yupcha Customer Bot AI - Complete API Documentation

## 📋 Table of Contents
- [Overview](#overview)
- [Authentication](#authentication)
- [Base URLs](#base-urls)
- [API Endpoints](#api-endpoints)
  - [Authentication Endpoints](#authentication-endpoints)
  - [User Management](#user-management)
  - [Organization Management](#organization-management)
  - [Permission Management](#permission-management)
  - [Team Management](#team-management)
  - [Customer Management](#customer-management)
  - [Conversation Management](#conversation-management)
  - [Conversation Notes Management](#conversation-notes-management)
  - [Message Management](#message-management)
  - [WebSocket Endpoints](#websocket-endpoints)
  - [Notification System](#notification-system)
  - [Media/File Upload](#media-file-upload)
  - [Admin Endpoints](#admin-endpoints)
- [Error Handling](#error-handling)
- [Rate Limiting](#rate-limiting)

## 🌟 Overview

Yupcha Customer Bot AI is a scalable, Redis-based customer support chat system with real-time messaging, intelligent bot responses, and comprehensive notification system.

### Key Features
- ✅ **Session-based Authentication** (no tokens)
- ✅ **Single Role Per User System** (simplified permissions)
- ✅ **Universal Customer Chat Access** (any user can help customers)
- ✅ **Real-time WebSocket Communication**
- ✅ **Redis-based Horizontal Scaling**
- ✅ **Intelligent Bot Responses**
- ✅ **Web Push Notifications** (NEW!)
- ✅ **Email Notifications**
- ✅ **File Upload Support**
- ✅ **Casbin-based Access Control**

## 🔐 Authentication

The API uses **session-based authentication** with HTTP-only cookies. No JWT tokens are used.

### Authentication Flow
1. **Login** → Receive session cookie
2. **Include cookie** in all subsequent requests
3. **Logout** → Session invalidated

### Single Role System
Each user has exactly **ONE role** (no more multiple roles confusion):

| Role | Description | Customer Chat | Admin Access |
|------|-------------|---------------|--------------|
| **Admin** | Full system access | ✅ | ✅ |
| **HR** | Human resources management | ✅ | ❌ |
| **Manager** | Team management | ✅ | ❌ |
| **Agent** | Customer support (default) | ✅ | ❌ |

**Key Benefits:**
- 🎯 **Any authenticated user can chat with customers** (not just agents!)
- 🔄 **Default role**: Users without assigned roles default to "Agent"
- 🔒 **Casbin-based permissions**: Dynamic, file-based access control
- 🏢 **Company-scoped**: Roles are specific to organizations

## 🌐 Base URLs

- **Backend API**: `http://localhost:8000/api/v1`
- **WebSocket**: `ws://localhost:8000/api/v1/ws`
- **API Documentation**: `http://localhost:8000/docs`
- **Scalar API Explorer**: `http://localhost:8000/scalar`

## 📡 API Endpoints

### 🔑 Authentication Endpoints

#### POST `/api/v1/auth/login`
**Purpose**: Authenticate user and create session

**Request Body**:
```json
{
  "email": "<EMAIL>",
  "password": "adminpassword"
}
```

**Response**:
```json
{
  "message": "Login successful",
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "role": "admin",
    "is_active": true
  }
}
```

**Usage Example**:
```bash
curl -X POST "http://localhost:8000/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '''{"email": "<EMAIL>", "password": "adminpassword"}''' \
  -c cookies.txt
```

#### POST `/api/v1/auth/logout`
**Purpose**: Invalidate current session

**Response**:
```json
{
  "message": "Logout successful"
}
```

#### GET `/api/v1/auth/me`
**Purpose**: Get current user information

**Response**:
```json
{
  "id": 1,
  "email": "<EMAIL>",
  "is_active": true,
  "organization_id": 1,
  "roles": [
    {
      "id": 1,
      "name": "Admin",
      "description": "Admin role",
      "is_active": true,
      "is_system_role": true,
      "company_id": null,
      "created_at": "2025-07-03T09:00:00Z",
      "updated_at": "2025-07-03T09:00:00Z"
    }
  ]
}
```

### 👥 User Management

#### GET `/api/v1/user-management/users/`
**Purpose**: List all users
**Access**: Admin required

**Query Parameters**:
- `skip`: Number of records to skip (default: 0)
- `limit`: Maximum records to return (default: 100)

**Response**:
```json
[
  {
    "id": 1,
    "full_name": "Admin User",
    "email": "<EMAIL>",
    "is_active": true,
    "organization_id": null,
    "team_id": null,
    "roles": [
      {
        "id": 1,
        "name": "Admin",
        "description": "Admin role",
        "is_active": true,
        "is_system_role": true,
        "company_id": null,
        "created_at": "2025-07-03T09:00:00Z",
        "updated_at": "2025-07-03T09:00:00Z"
      }
    ],
    "created_at": "2025-07-03T09:00:00Z",
    "updated_at": "2025-07-03T09:00:00Z",
    "profile_image_url": "http://localhost:9000/bucket/path/to/image.webp" 
  }
]
```

#### POST `/api/v1/user-management/users/`
**Purpose**: Create new user (Admin only)
**Access**: Admin required

**Request Body**:
```json
{
  "email": "<EMAIL>",
  "password": "agentpassword",
  "full_name": "Agent Name",
  "organization_id": 1,
  "team_id": 1
}
```
*Note: User roles are assigned separately via role management endpoints, not directly during user creation.*

#### GET `/api/v1/user-management/users/{user_id}`
**Purpose**: Get specific user details
**Access**: Admin or own profile

#### PUT `/api/v1/user-management/users/{user_id}`
**Purpose**: Update user information
**Access**: Admin or own profile

#### DELETE `/api/v1/user-management/users/{user_id}`
**Purpose**: Delete user (Admin only)
**Access**: Admin required

#### GET `/api/v1/user-management/users/agents`
**Purpose**: List all users with the 'agent' role.
**Access**: Agent or Admin access required.

**Response**:
```json
[
  {
    "id": 2,
    "email": "<EMAIL>",
    "full_name": "Agent Jane Doe",
    "is_active": true,
    "organization_id": 1,
    "team_id": 1,
    "roles": [
      {
        "id": 2,
        "name": "Agent",
        "description": "Agent role",
        "is_active": true,
        "is_system_role": true,
        "company_id": null,
        "created_at": "2025-07-03T09:00:00Z",
        "updated_at": "2025-07-03T09:00:00Z"
      }
    ],
    "profile_image_url": null
  }
]
```

#### GET `/api/v1/user-management/users/admins`
**Purpose**: List all users with the 'admin' role.
**Access**: Admin access required.

**Response**:
```json
[
  {
    "id": 1,
    "email": "<EMAIL>",
    "full_name": "Admin John Doe",
    "is_active": true,
    "organization_id": null,
    "team_id": null,
    "roles": [
      {
        "id": 1,
        "name": "Admin",
        "description": "Admin role",
        "is_active": true,
        "is_system_role": true,
        "company_id": null,
        "created_at": "2025-07-03T09:00:00Z",
        "updated_at": "2025-07-03T09:00:00Z"
      }
    ],
    "profile_image_url": "http://localhost:9000/bucket/path/to/admin_image.webp"
  }
]
```

#### PATCH `/api/v1/user-management/users/{user_id}`
**Purpose**: Partially update a user's information.
**Access**: Admin required

**Request Body**:
```json
{
  "full_name": "Updated Name",
  "is_active": false,
  "profile_image_id": 123 // Optional: ID of an uploaded asset to use as profile image
}
```
**Profile Image Update Flow**: (Same as PUT)

#### PATCH `/api/v1/user-management/users/{user_id}/restore`
**Purpose**: Restore a soft-deleted user.
**Access**: Admin required

**Response**:
```json
{
  "id": 1,
  "email": "<EMAIL>",
  "full_name": "Deleted User",
  "is_active": true,
  "organization_id": 1,
  "team_id": null,
  "roles": [],
  "created_at": "2025-07-01T10:00:00Z",
  "updated_at": "2025-07-03T10:00:00Z",
  "profile_image_url": null
}
```

### 🏢 Organization Management

#### GET `/api/v1/organizations/`
**Purpose**: List all organizations (Admin only)

#### POST `/api/v1/organizations/`
**Purpose**: Create new organization (Admin only)

**Request Body**:
```json
{
  "name": "Acme Corp",
  "description": "Customer support for Acme Corp"
}
```

#### GET `/api/v1/organizations/{org_id}`
**Purpose**: Get organization details

#### PUT `/api/v1/organizations/{org_id}`
**Purpose**: Update organization

#### DELETE `/api/v1/organizations/{organization_id}`
**Purpose**: Soft-delete an organization.
**Access**: Admin required

#### PATCH `/api/v1/organizations/{organization_id}/restore`
**Purpose**: Restore a soft-deleted organization.
**Access**: Admin required

**Response**:
```json
{
  "id": 1,
  "name": "Restored Org",
  "description": "Description of restored organization",
  "is_active": true,
  "is_deleted": false,
  "created_at": "2024-01-01T10:00:00Z",
  "updated_at": "2024-01-01T11:00:00Z"
}
```

### 🔑 Permission Management

#### GET `/api/v1/user-management/permissions/`
**Purpose**: List all permissions
**Access**: Admin required

**Query Parameters**:
- `skip`: Number of records to skip (default: 0)
- `limit`: Maximum records to return (default: 100)

**Response**:
```json
[
  {
    "id": 1,
    "name": "company:*:users.read",
    "resource": "company:*:users",
    "action": "read",
    "description": "Can read users across all companies",
    "is_system_permission": true,
    "created_at": "2025-07-03T09:00:00Z"
  }
]
```



### 👨‍👩‍👧‍👦 Team Management

#### GET `/api/v1/user-management/teams/`
**Purpose**: List all teams.
**Access**: Agent or Admin access required.

**Query Parameters**:
- `skip`: Number of records to skip (default: 0)
- `limit`: Maximum records to return (default: 100)

**Response**:
```json
[
  {
    "id": 1,
    "name": "Support Team Alpha",
    "description": "Primary customer support team",
    "organization_id": 1,
    "is_active": true,
    "is_deleted": false,
    "created_at": "2025-07-03T10:00:00Z",
    "updated_at": "2025-07-03T10:00:00Z"
  }
]
```

#### POST `/api/v1/user-management/teams/`
**Purpose**: Create a new team.
**Access**: Admin required

**Request Body**:
```json
{
  "name": "New Team Name",
  "description": "Description of the new team",
  "organization_id": 1
}
```

#### GET `/api/v1/user-management/teams/organization/{organization_id}`
**Purpose**: List teams belonging to a specific organization.
**Access**: Agent (for own organization) or Admin required.

**Path Parameters**:
- `organization_id`: The ID of the organization.

**Query Parameters**:
- `skip`: Number of records to skip (default: 0)
- `limit`: Maximum records to return (default: 100)

**Response**:
```json
[
  {
    "id": 1,
    "name": "Support Team Alpha",
    "description": "Primary customer support team",
    "organization_id": 1,
    "is_active": true,
    "is_deleted": false,
    "created_at": "2025-07-03T10:00:00Z",
    "updated_at": "2025-07-03T10:00:00Z"
  }
]
```

#### GET `/api/v1/user-management/teams/{team_id}`
**Purpose**: Get a specific team by ID.
**Access**: Agent or Admin required.

**Path Parameters**:
- `team_id`: The ID of the team.

**Response**:
```json
{
  "id": 1,
  "name": "Support Team Alpha",
  "description": "Primary customer support team",
  "organization_id": 1,
  "is_active": true,
  "is_deleted": false,
  "created_at": "2025-07-03T10:00:00Z",
  "updated_at": "2025-07-03T10:00:00Z"
}
```

#### PUT `/api/v1/user-management/teams/{team_id}`
**Purpose**: Update an existing team.
**Access**: Admin required.

**Path Parameters**:
- `team_id`: The ID of the team to update.

**Request Body**:
```json
{
  "name": "Updated Team Name",
  "description": "New description for the team",
  "is_active": false
}
```

#### DELETE `/api/v1/user-management/teams/{team_id}`
**Purpose**: Soft-delete a team.
**Access**: Admin required.

**Path Parameters**:
- `team_id`: The ID of the team to soft-delete.

#### PATCH `/api/v1/user-management/teams/{team_id}/restore`
**Purpose**: Restore a soft-deleted team.
**Access**: Admin required.

**Path Parameters**:
- `team_id`: The ID of the team to restore.

**Response**:
```json
{
  "id": 1,
  "name": "Restored Team",
  "description": "Description of restored team",
  "organization_id": 1,
  "is_active": true,
  "is_deleted": false,
  "created_at": "2025-07-03T10:00:00Z",
  "updated_at": "2025-07-03T10:00:00Z"
}
```

### 🗄️ Role Management

#### POST `/api/v1/user-management/roles/`
**Purpose**: Create a new role
**Access**: Admin required

**Request Body**:
```json
{
  "name": "New Role Name",
  "description": "Description of the new role",
  "is_active": true,
  "is_system_role": false,
  "company_id": null
}
```
*Note: `company_id` can be `null` for system-wide roles (e.g., Admin, Agent) or an organization ID for organization-specific roles.*

**Response**:
```json
{
  "id": 1,
  "name": "New Role Name",
  "description": "Description of the new role",
  "is_active": true,
  "is_system_role": false,
  "company_id": null,
  "created_at": "2025-07-03T10:00:00Z",
  "updated_at": "2025-07-03T10:00:00Z"
}
```

#### GET `/api/v1/user-management/roles/`
**Purpose**: List all roles
**Access**: Admin required

**Query Parameters**:
- `skip`: Number of records to skip (default: 0)
- `limit`: Maximum records to return (default: 100)
- `active_only`: Boolean to filter for active roles (default: true)
- `include_system`: Boolean to include system roles (default: true)

**Response**:
```json
[
  {
    "id": 1,
    "name": "Admin",
    "description": "Admin role",
    "is_active": true,
    "is_system_role": true,
    "company_id": null,
    "created_at": "2025-07-03T09:00:00Z",
    "updated_at": "2025-07-03T09:00:00Z"
  },
  {
    "id": 2,
    "name": "Agent",
    "description": "Agent role",
    "is_active": true,
    "is_system_role": true,
    "company_id": null,
    "created_at": "2025-07-03T09:00:00Z",
    "updated_at": "2025-07-03T09:00:00Z"
  }
]
```

#### GET `/api/v1/user-management/roles/{role_id}`
**Purpose**: Get a specific role by ID
**Access**: Admin required

**Response**:
```json
{
  "id": 1,
  "name": "Admin",
  "description": "Admin role",
  "is_active": true,
  "is_system_role": true,
  "company_id": null,
  "created_at": "2025-07-03T09:00:00Z",
  "updated_at": "2025-07-03T09:00:00Z"
}
```

#### PUT `/api/v1/user-management/roles/{role_id}`
**Purpose**: Update an existing role
**Access**: Admin required

**Request Body**:
```json
{
  "name": "Updated Role Name",
  "description": "New description for the role",
  "is_active": false
}
```

#### DELETE `/api/v1/user-management/roles/{role_id}`
**Purpose**: Delete a role
**Access**: Admin required
*Note: System roles cannot be deleted.*

### 🔐 Permission Management

#### POST `/api/v1/user-management/permissions/check`
**Purpose**: Check if current user has permission for a specific resource and action
**Access**: Any authenticated user

**Request Body**:
```json
{
  "resource": "users",
  "action": "create"
}
```

**Response**:
```json
{
  "allowed": true,
  "user_role": "Admin",
  "resource": "users",
  "action": "create"
}
```

#### GET `/api/v1/user-management/permissions/roles`
**Purpose**: Get current user's roles and their permissions
**Access**: Any authenticated user

**Response**:
```json
[
  {
    "role": "Admin",
    "permissions": [
      {
        "resource": "users",
        "action": "create",
        "effect": "allow"
      },
      {
        "resource": "roles",
        "action": "update",
        "effect": "allow"
      }
    ]
  }
]
```

#### POST `/api/v1/user-management/permissions/add`
**Purpose**: Add a permission to a role
**Access**: Admin only

**Request Body**:
```json
{
  "role": "Manager",
  "resource": "teams",
  "action": "create"
}
```

#### DELETE `/api/v1/user-management/permissions/remove`
**Purpose**: Remove a permission from a role
**Access**: Admin only

**Request Body**:
```json
{
  "role": "Manager",
  "resource": "teams",
  "action": "delete"
}
```

#### GET `/api/v1/user-management/permissions/resources`
**Purpose**: Get list of available resources and actions
**Access**: Admin only

**Response**:
```json
{
  "users": ["create", "read", "update", "delete"],
  "roles": ["create", "read", "update", "delete"],
  "conversations": ["create", "read", "update", "delete", "assign"],
  "messages": ["create", "read", "update", "delete"],
  "customers": ["create", "read", "update", "delete"],
  "teams": ["create", "read", "update", "delete"],
  "organizations": ["create", "read", "update", "delete"],
  "admin": ["access", "manage_users", "manage_roles", "view_analytics"]
}
```

### 👤 Customer Management

#### GET `/api/v1/customers/`
**Purpose**: List customers (Agent/Admin access)

#### POST `/api/v1/customers/`
**Purpose**: Create new customer
**Access**: Public (no authentication required)

**Request Body**:
```json
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "phone": "+1234567890",
  "organization_id": 1
}
```

#### GET `/api/v1/customers/{customer_id}`
**Purpose**: Get customer details

#### PUT `/api/v1/customers/{customer_id}`
**Purpose**: Update customer information

#### GET `/api/v1/customers/by-customer-id/{customer_id}`
**Purpose**: Get a customer by their external `customer_id`.
**Access**: Agent or Admin access required.

**Path Parameters**:
- `customer_id`: The external ID of the customer.

**Response**:
```json
{
  "id": 1,
  "customer_id": "ext-customer-123",
  "name": "John Doe",
  "email": "<EMAIL>",
  "phone": "+1234567890",
  "organization_id": 1,
  "is_active": true,
  "is_deleted": false,
  "created_at": "2025-07-03T10:00:00Z",
  "updated_at": "2025-07-03T10:00:00Z"
}
```

#### DELETE `/api/v1/customers/{customer_id}`
**Purpose**: Soft-delete a customer.
**Access**: Agent or Admin access required.

**Path Parameters**:
- `customer_id`: The ID of the customer to soft-delete.

#### PATCH `/api/v1/customers/{customer_id}/restore`
**Purpose**: Restore a soft-deleted customer.
**Access**: Admin required.

**Path Parameters**:
- `customer_id`: The ID of the customer to restore.

**Response**:
```json
{
  "id": 1,
  "customer_id": "ext-customer-123",
  "name": "John Doe",
  "email": "<EMAIL>",
  "phone": "+1234567890",
  "organization_id": 1,
  "is_active": true,
  "is_deleted": false,
  "created_at": "2025-07-03T10:00:00Z",
  "updated_at": "2025-07-03T10:00:00Z"
}
```

### 💬 Conversation Management

#### GET `/api/v1/chat/conversations/`
**Purpose**: List conversations with pagination
**Access**: Admin required

**Query Parameters**:
- `skip`: Records to skip (default: 0)
- `limit`: Max records (default: 100)

**Response**:
```json
[
  {
    "id": 1,
    "customer_id": 1,
    "organization_id": 1,
    "assigned_team_id": 1,
    "status": "active",
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-15T10:35:00Z"
  }
]
```

#### POST `/api/v1/chat/conversations/`
**Purpose**: Create new conversation

**Request Body**:
```json
{
  "customer_id": 1,
  "organization_id": 1
}
```

**What happens**:
1. ✅ Conversation created in database
2. ✅ Real-time notification sent to agents via WebSocket
3. ✅ Email notification sent to assigned team
4. ✅ Auto-assignment to default team if not specified

#### GET `/api/v1/chat/conversations/{conversation_id}`
**Purpose**: Get specific conversation details
**Access**: Agent or Admin required

#### GET `/api/v1/chat/conversations/{conversation_id}/messages`
**Purpose**: Get all messages in conversation

**Response**:
```json
[
  {
    "id": 1,
    "conversation_id": 1,
    "sender_type": "customer",
    "sender_id": "customer-123",
    "content": "Hello, I need help!",
    "message_type": "text",
    "created_at": "2024-01-15T10:30:00Z"
  }
]
```

#### POST `/api/v1/chat/conversations/{conversation_id}/messages`
**Purpose**: Create a new message in a conversation.
**Access**: Agent or Admin access required.

**Path Parameters**:
- `conversation_id`: The ID of the conversation.

**Request Body**:
```json
{
  "content": "Hello! How can I help you?",
  "sender": "agent",
  "message_type": "text"
}
```

#### GET `/api/v1/chat/conversations/unassigned`
**Purpose**: Get unassigned conversations
**Access**: Agent or Admin access required.

**Response**:
```json
[
  {
    "id": 1,
    "customer_id": 1,
    "organization_id": 1,
    "assigned_team_id": null,
    "status": "new",
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-15T10:30:00Z"
  }
]
```

#### GET `/api/v1/chat/conversations/team/{team_id}`
**Purpose**: Get conversations for a specific team.
**Access**: Team members or Admin.

**Path Parameters**:
- `team_id`: The ID of the team.

**Response**:
```json
[
  {
    "id": 1,
    "customer_id": 1,
    "organization_id": 1,
    "assigned_team_id": 1,
    "status": "open",
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-15T10:35:00Z"
  }
]
```

#### GET `/api/v1/chat/conversations/customer/{customer_id}`
**Purpose**: Get all conversations for a specific customer.
**Access**: Agent or Admin access required.

**Path Parameters**:
- `customer_id`: The ID of the customer.

**Response**:
```json
[
  {
    "id": 1,
    "customer_id": 1,
    "organization_id": 1,
    "assigned_team_id": 1,
    "status": "open",
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-15T10:35:00Z"
  }
]
```

#### POST `/api/v1/chat/conversations/{conversation_id}/assign/{team_id}`
**Purpose**: Assign conversation to team (Admin only)

#### POST `/api/v1/chat/conversations/{conversation_id}/labels`
**Purpose**: Attach one or more labels to a conversation.
**Access**: Agent or Admin access required.

**Path Parameters**:
- `conversation_id`: The ID of the conversation.

**Request Body**:
```json
{
  "label_ids": [1, 2, 3]
}
```

**Response**:
```json
{
  "id": 1,
  "customer_id": 1,
  "organization_id": 1,
  "assigned_team_id": 1,
  "status": "open",
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T10:35:00Z",
  "labels": [
    {
      "id": 1,
      "name": "Support",
      "description": "General support inquiries"
    }
  ]
}
```

#### POST `/api/v1/chat/conversations/{conversation_id}/suggest-replies`
**Purpose**: Get AI-generated reply suggestions for a conversation.
**Access**: Agent or Admin access required.

**Path Parameters**:
- `conversation_id`: The ID of the conversation.

**Response**:
```json
{
  "suggestions": [
    "How can I help you today?",
    "Please provide more details about your issue."
  ]
}
```

#### GET `/api/v1/chat/conversations/{conversation_id}/summary`
**Purpose**: Get an AI-generated summary of the entire conversation.
**Access**: Agent or Admin access required.

**Path Parameters**:
- `conversation_id`: The ID of the conversation.

**Response**:
```json
{
  "summary": "The customer is experiencing issues with their account login and needs assistance resetting their password."
}
```

### 📨 Message Management

#### GET `/api/v1/chat/messages/conversation/{conversation_id}`
**Purpose**: Get messages for conversation (same as above)

#### POST `/api/v1/chat/messages/`
**Purpose**: Send new message

**Request Body**:
```json
{
  "conversation_id": 1,
  "content": "Hello! How can I help you?",
  "sender_type": "agent",
  "message_type": "text"
}
```

**Message Types**:
- `text`: Plain text message
- `image`: Image attachment
- `file`: File attachment
- `system`: System notification

#### GET `/api/v1/chat/messages/{message_id}`
**Purpose**: Get a specific message by ID.
**Access**: Sender, Admin, or Agent with access to conversation.

**Path Parameters**:
- `message_id`: The ID of the message.

**Response**:
```json
{
  "id": 1,
  "conversation_id": 1,
  "sender_type": "agent",
  "sender_id": 1,
  "content": "Hello! How can I help you?",
  "message_type": "text",
  "created_at": "2024-01-15T10:30:00Z"
}
```

#### DELETE `/api/v1/chat/messages/{message_id}`
**Purpose**: Soft-delete a message.
**Access**: Sender or Admin.

**Path Parameters**:
- `message_id`: The ID of the message to soft-delete.

#### PATCH `/api/v1/chat/messages/{message_id}/restore`
**Purpose**: Restore a soft-deleted message.
**Access**: Admin only.

**Path Parameters**:
- `message_id`: The ID of the message to restore.

**Response**:
```json
{
  "message": "Message restored successfully"
}
```

### 📝 Conversation Notes Management

#### POST `/api/v1/chat/conversation-notes/`
**Purpose**: Create a new conversation note.
**Access**: Authenticated user (can only create notes for themselves).

**Request Body**:
```json
{
  "conversation_id": 1,
  "user_id": 1,
  "content": "This is a note about the conversation."
}
```

**Response**:
```json
{
  "id": 1,
  "conversation_id": 1,
  "user_id": 1,
  "content": "This is a note about the conversation.",
  "created_at": "2025-07-03T10:00:00Z",
  "updated_at": "2025-07-03T10:00:00Z"
}
```

#### GET `/api/v1/chat/conversation-notes/conversation/{conversation_id}`
**Purpose**: Get all notes for a specific conversation.
**Access**: Authenticated user (can only view notes for conversations they have access to).

**Path Parameters**:
- `conversation_id`: The ID of the conversation.

**Query Parameters**:
- `skip`: Number of records to skip (default: 0)
- `limit`: Maximum records to return (default: 100)

**Response**:
```json
[
  {
    "id": 1,
    "conversation_id": 1,
    "user_id": 1,
    "content": "This is a note about the conversation.",
    "created_at": "2025-07-03T10:00:00Z",
    "updated_at": "2025-07-03T10:00:00Z"
  }
]
```

#### PUT `/api/v1/chat/conversation-notes/{note_id}`
**Purpose**: Update an existing conversation note.
**Access**: Only the creator of the note.

**Path Parameters**:
- `note_id`: The ID of the note to update.

**Request Body**:
```json
{
  "content": "Updated content for the note."
}
```

#### DELETE `/api/v1/chat/conversation-notes/{note_id}`
**Purpose**: Delete a conversation note.
**Access**: Only the creator of the note.

**Path Parameters**:
- `note_id`: The ID of the note to delete.

### 📝 Customer Notes Management

#### POST `/api/v1/chat/notes/`
**Purpose**: Create a new private note for a customer.
**Access**: Authenticated user.

**Request Body**:
```json
{
  "customer_id": 1,
  "user_id": 1,
  "content": "This is a private note about the customer."
}
```

**Response**:
```json
{
  "id": 1,
  "customer_id": 1,
  "user_id": 1,
  "content": "This is a private note about the customer.",
  "created_at": "2025-07-03T10:00:00Z",
  "updated_at": "2025-07-03T10:00:00Z"
}
```

#### GET `/api/v1/chat/notes/customer/{customer_id}`
**Purpose**: Get all private notes for a specific customer.
**Access**: Authenticated user.

**Path Parameters**:
- `customer_id`: The ID of the customer.

**Query Parameters**:
- `skip`: Number of records to skip (default: 0)
- `limit`: Maximum records to return (default: 100)

**Response**:
```json
[
  {
    "id": 1,
    "customer_id": 1,
    "user_id": 1,
    "content": "This is a private note about the customer.",
    "created_at": "2025-07-03T10:00:00Z",
    "updated_at": "2025-07-03T10:00:00Z"
  }
]
```

#### PUT `/api/v1/chat/notes/{note_id}`
**Purpose**: Update an existing private note.
**Access**: Only the creator of the note.

**Path Parameters**:
- `note_id`: The ID of the note to update.

**Request Body**:
```json
{
  "content": "Updated content for the private note."
}
```

#### DELETE `/api/v1/chat/notes/{note_id}`
**Purpose**: Delete a private note.
**Access**: Only the creator of the note.

**Path Parameters**:
- `note_id`: The ID of the note to delete.

### 🔌 WebSocket Endpoints

#### Unified Chat WebSocket
**URL**: `ws://localhost:8000/api/v1/ws/chat/{conversation_id}`

**Purpose**: Real-time chat for both customers and authenticated users (agents/admins).

**Authentication**:
- **Customers**: Connect using `customer_id` as a query parameter: `ws://localhost:8000/api/v1/ws/chat/{conversation_id}?customer_id={customer_id}`
- **Authenticated Users (Agents/Admins)**: Connect with their session cookie (automatically sent by browser for same-origin requests). No query parameter needed for authentication.

**Connection Flow**:
1. Client connects with `conversation_id` (and `customer_id` for customers).
2. Server authenticates the client.
3. Client receives recent message history for the conversation.
4. Clients can send/receive messages in real-time.
5. Bot responds when no agents are active in the conversation.

**Incoming Message Formats (from client to server)**:

**1. Text Message**:
```json
{
  "type": "text",
  "content": "Hello, I need help!"
}
```

**2. Media Message**:
```json
{
  "type": "media",
  "asset_id": 123,
  "content": "Here's a screenshot of the issue." // Optional caption
}
```

**3. Typing Indicator**:
```json
{
  "type": "typing",
  "is_typing": true // or false
}
```

**4. Ping Message**:
```json
{
  "type": "ping"
}
```

**Outgoing Message Formats (from server to client)**:

**1. Message (Text or Media)**:
```json
{
  "id": 123,
  "type": "message",
  "content": "Hello, how can I help you?",
  "sender": "agent" | "customer" | "bot",
  "message_type": "text" | "image" | "file",
  "asset_id": 456, // Only for media messages
  "asset_url": "http://example.com/asset.jpg", // Only for media messages
  "timestamp": "2025-07-10T12:30:00Z",
  "user_info": { // Information about the sender
    "name": "Agent Name" | "Customer Name" | "Yupcha Bot"
  }
}
```

**2. Typing Indicator**:
```json
{
  "type": "typing",
  "is_typing": true, // or false
  "user_info": {
    "name": "Agent Name" | "Customer Name"
  }
}
```

**3. System Message**:
```json
{
  "type": "system",
  "detail": "Connection successful."
}
```

#### User Notifications WebSocket  
**URL**: `ws://localhost:8000/api/v1/ws/user-notifications`

**Purpose**: Real-time notifications for authenticated company users (agents, admins, etc.) relevant to their organization and team.
**Access**: Authenticated user (via HTTP-only session cookie).

**Incoming Message Formats (from client to server)**:

**1. Ping Message**:
```json
{
  "type": "ping"
}
```

**Outgoing Message Formats (from server to client)**:

**1. New Conversation Notification**:
```json
{
  "type": "new_conversation",
  "payload": {
    "conversation_id": 123,
    "customer_name": "John Doe",
    "message": "New conversation started by John Doe"
  }
}
```

**2. Notification Channel Connected**:
```json
{
  "type": "notification_channel_connected",
  "message": "Connected successfully to notifications.",
  "organization_id": 1, // Only if connected to an organization channel
  "user_id": 1 // Only if connected to an organization channel
}
```

**3. Pong Message**:
```json
{
  "type": "pong"
}
```

**4. Error Message**:
```json
{
  "type": "error",
  "detail": "Error message here",
  "code": "RATE_LIMIT_EXCEEDED" // Optional error code
}
```





### 📎 Media/File Upload

#### POST `/api/v1/media/upload`
**Purpose**: Upload files (images, documents)

**Request**: Multipart form data
```
Content-Type: multipart/form-data
file: [binary file data]
```

**Response**:
```json
{
  "file_id": "uuid-here",
  "filename": "document.pdf",
  "file_url": "http://localhost:9000/yupcha-assets/uuid-here",
  "file_type": "application/pdf",
  "file_size": 1024000
}
```

#### GET `/api/v1/media/{file_id}`
**Purpose**: Get file metadata

#### GET `/api/v1/media/{file_id}/download`
**Purpose**: Download file
**Access**: Authenticated user

### ⚙️ Admin Endpoints

#### GET `/api/v1/admin/stats`
**Purpose**: Get system statistics (Admin only)

**Response**:
```json
{
  "total_conversations": 150,
  "active_conversations": 25,
  "total_customers": 89,
  "total_agents": 12,
  "messages_today": 450
}
```

#### GET `/api/v1/admin/health`
**Purpose**: System health check

**Response**:
```json
{
  "status": "healthy",
  "database": "connected",
  "redis": "connected",
  "s3": "connected"
}
```

## ⚠️ Error Handling

### Standard Error Response
```json
{
  "detail": "Error message here"
}
```

### Common HTTP Status Codes
- `200`: Success
- `201`: Created
- `400`: Bad Request
- `401`: Unauthorized
- `403`: Forbidden
- `404`: Not Found
- `422`: Validation Error
- `500`: Internal Server Error

### Validation Errors
```json
{
  "detail": [
    {
      "loc": ["body", "email"],
      "msg": "field required",
      "type": "value_error.missing"
    }
  ]
}
```

## 🚦 Rate Limiting

Rate limiting is implemented using Redis to protect WebSocket endpoints from abuse and spam.

- **WebSocket Endpoints:** Rate limited based on `customer_id` for customers and `user_id` for authenticated users.
- **Configuration:** Limits are configurable via environment variables (`YUPCHA_RATE_LIMIT_MESSAGES`, `YUPCHA_RATE_LIMIT_WINDOW_SECONDS`).

## 🧪 Testing the API

### Using cURL

1. **Login and save session**:
```bash
curl -X POST "http://localhost:8000/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '''{"email": "<EMAIL>", "password": "adminpassword"}''' \
  -c cookies.txt
```

2. **Use session for authenticated requests**:
```bash
curl -X GET "http://localhost:8000/api/v1/chat/conversations/" \
  -b cookies.txt
```

3. **Create a conversation**:
```bash
curl -X POST "http://localhost:8000/api/v1/chat/conversations/" \
  -H "Content-Type: application/json" \
  -d '''{"customer_id": 1, "organization_id": 1}''' \
  -b cookies.txt
```

### Using the Frontend

1. **Open**: http://localhost:5173/
2. **Login**: <EMAIL> / adminpassword
3. **Navigate**: Use the sidebar to access different sections
4. **Chat**: Click on conversations to start chatting

### Using WebSocket (Customer)

```bash
# Run the customer chat client
cd /home/<USER>/Documents/Devlopment/yupcha-customerbot-ai
uv run python tests/test_customer_chat_client.py
```

## 🔧 Configuration

### Environment Variables (.env)
```bash
# Database
DATABASE_URL=postgresql+asyncpg://user:pass@localhost:5432/db

# Redis  
REDIS_URL=redis://localhost:6379/0

# Email
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-password
MAIL_FROM=<EMAIL>
MAIL_SERVER=smtp.gmail.com

# S3/MinIO
S3_ENDPOINT_URL=http://localhost:9000
S3_ACCESS_KEY_ID=minioadmin
S3_SECRET_ACCESS_KEY=minioadmin
```

## 🚀 Production Deployment

### Docker Compose
```yaml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=postgresql+asyncpg://...
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - postgres
      - redis
      
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: yupcha_db
      
  redis:
    image: redis:7-alpine
```

### Scaling
- Run multiple app instances behind load balancer
- Use Redis cluster for high availability
- Separate read/write database replicas

## 🤖 Bot Intelligence

### How the Bot Works
1. **Agent Presence Detection**: Bot only responds when no agents are active
2. **Keyword Recognition**: Responds based on message content
3. **Redis-based Tracking**: Agent activity tracked in Redis with TTL
4. **Fallback Responses**: Random helpful responses when no keywords match

### Bot Response Examples
- **"help"** or **"support"** → "I'm here to help! What specific issue are you facing?"
- **"agent"** or **"human"** → "I'll connect you with a human agent right away."
- **"price"** or **"cost"** → "For pricing information, please visit our website or speak with an agent."

## 📧 Notification System

Yupcha supports multiple notification channels to ensure agents never miss important customer interactions.

### Notification Channels

#### 1. 🔔 Web Push Notifications (NEW!)
Browser-based push notifications that work even when the app is closed.

**Push Notification Endpoints:**

```http
# Subscribe to push notifications
POST /api/v1/notifications/subscribe
Content-Type: application/json
{
  "subscription_info": {
    "endpoint": "https://fcm.googleapis.com/fcm/send/...",
    "keys": {
      "p256dh": "...",
      "auth": "..."
    }
  }
}

# Get VAPID public key for frontend
GET /api/v1/notifications/vapid-public-key

# Get user's subscriptions
GET /api/v1/notifications/subscriptions

# Unsubscribe by ID
DELETE /api/v1/notifications/unsubscribe/{subscription_id}

# Unsubscribe by endpoint
DELETE /api/v1/notifications/unsubscribe?endpoint=https://...

# Send test notification
POST /api/v1/notifications/test
{
  "title": "Test Notification",
  "body": "This is a test push notification",
  "icon": "/icon.png",
  "data": {"url": "/dashboard"}
}
```

**Push Notification Triggers:**
- ✅ New conversation assigned to team
- ✅ New message from customer
- ✅ Welcome notification on subscription
- ✅ Custom test notifications

#### 2. 🌐 WebSocket Notifications (Real-time)
In-app notifications for users with the application open.

```javascript
// Connect to notifications WebSocket
const ws = new WebSocket('ws://localhost:8000/api/v1/ws/user-notifications');

// Receive real-time notifications
ws.onmessage = (event) => {
  const notification = JSON.parse(event.data);
  console.log('New notification:', notification);
};
```

#### 3. 📧 Email Notifications
Traditional email notifications for important events.

**Email Configuration:**
```python
# In .env file
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_FROM=<EMAIL>
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
ENABLE_EMAIL_NOTIFICATIONS=true
```

### Frontend Integration Example

```javascript
// Service Worker for Push Notifications
// Register service worker
navigator.serviceWorker.register('/sw.js');

// Request notification permission
const permission = await Notification.requestPermission();

// Subscribe to push notifications
if ('serviceWorker' in navigator && 'PushManager' in window) {
  const registration = await navigator.serviceWorker.ready;

  // Get VAPID public key
  const response = await fetch('/api/v1/notifications/vapid-public-key');
  const { publicKey } = await response.json();

  // Subscribe
  const subscription = await registration.pushManager.subscribe({
    userVisibleOnly: true,
    applicationServerKey: publicKey
  });

  // Send subscription to server
  await fetch('/api/v1/notifications/subscribe', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ subscription_info: subscription })
  });
}
```

### VAPID Configuration
```python
# Generate VAPID keys (one-time setup)
vapid --gen

# Add to .env file
YUPCHA_VAPID_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\n..."
YUPCHA_VAPID_PUBLIC_KEY="BLnNP3DXBPgYNo85Ni0DYy70WbMl..."
YUPCHA_VAPID_ADMIN_EMAIL="<EMAIL>"
```

## 🔄 Real-time Architecture

### Redis Pub/Sub Channels
- **`conversation:{id}`**: Messages for specific conversation
- **`organization:{id}:notifications`**: Org-wide notifications
- **`agent:{id}:presence`**: Agent activity tracking

### WebSocket Connection Flow
```
1. Client connects → Authentication check
2. Subscribe to Redis channels → Real-time updates
3. Send/receive messages → Broadcast via Redis
4. Disconnect → Clean up subscriptions
```

### Horizontal Scaling
- Multiple backend instances can run simultaneously
- Redis handles message distribution
- Load balancer distributes WebSocket connections
- Session affinity not required

## 🛠️ Development Workflow

### Starting the System
```bash
# 1. Start Redis
sudo systemctl start redis-server

# 2. Start Backend
cd /home/<USER>/Documents/Devlopment/yupcha-customerbot-ai
uv run uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# 3. Start Frontend
cd frontend
npm run dev

# 4. Test Customer Chat
uv run python tests/test_customer_chat_client.py
```

### Database Migrations
```bash
# Create migration
alembic revision --autogenerate -m "Add new feature"

# Apply migration
alembic upgrade head
```

### Adding New Endpoints
1. Create endpoint in `app/api/endpoints/`
2. Add to router in `app/api/api.py`
3. Update this documentation
4. Add tests

## 🧪 Testing Guide

### Manual Testing Checklist
- [ ] Login/logout works
- [ ] Create conversation triggers notifications
- [ ] Customer can send messages via WebSocket
- [ ] Agent receives real-time notifications
- [ ] Bot responds when no agents active
- [ ] Email notifications sent to teams
- [ ] File upload/download works
- [ ] All CRUD operations work

### Automated Testing
```bash
# Run all tests
pytest

# Run specific test file
pytest tests/test_auth.py

# Run with coverage
pytest --cov=app tests/
```

## 🔒 Security Considerations

### Authentication Security
- Session cookies are HTTP-only
- CSRF protection via SameSite cookies
- Password hashing with bcrypt
- Session timeout after inactivity

### API Security
- Input validation with Pydantic
- SQL injection protection via SQLAlchemy ORM
- File upload restrictions
- Rate limiting (can be added)

### WebSocket Security
- Session-based authentication
- Connection validation
- Message sanitization
- Automatic cleanup on disconnect

## 📊 Monitoring & Logging

### Log Levels
- **INFO**: Normal operations
- **WARNING**: Potential issues
- **ERROR**: Actual problems
- **DEBUG**: Detailed debugging info

### Key Metrics to Monitor
- Active WebSocket connections
- Message throughput
- Database connection pool
- Redis memory usage
- Email delivery success rate

### Health Checks
```bash
# Check backend health
curl http://localhost:8000/api/admin/health

# Check Redis
redis-cli ping

# Check database
psql -h localhost -U yupcha_user -d yupcha_db -c "SELECT 1;"
```

## 🚀 Production Deployment

### Environment Setup
1. **Database**: PostgreSQL with connection pooling
2. **Cache**: Redis cluster for high availability
3. **Storage**: S3-compatible object storage
4. **Email**: Production SMTP service (SendGrid, AWS SES)
5. **Load Balancer**: Nginx or AWS ALB
6. **Monitoring**: Prometheus + Grafana

### Docker Deployment
```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8000

CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### Kubernetes Deployment
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: yupcha-api
spec:
  replicas: 3
  selector:
    matchLabels:
      app: yupcha-api
  template:
    metadata:
      labels:
        app: yupcha-api
    spec:
      containers:
      - name: api
        image: yupcha/api:latest
        ports:
        - containerPort: 8000
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: yupcha-secrets
              key: database-url
```

## 🔧 Troubleshooting

### Common Issues

#### WebSocket Connection Failed
```bash
# Check if backend is running
curl http://localhost:8000/api/admin/health

# Check Redis connection
redis-cli ping

# Check browser console for errors
```

#### Email Not Sending
```bash
# Check email configuration
grep MAIL_ .env

# Check logs for email errors
tail -f logs/app.log | grep email

# Test email service
curl -X POST http://localhost:8000/api/v1/notifications/test
```

#### Database Connection Issues
```bash
# Check database status
systemctl status postgresql

# Test connection
psql -h localhost -U yupcha_user -d yupcha_db

# Check connection pool
# Look for "pool" in application logs
```

### Performance Optimization
- Use database indexes on frequently queried columns
- Implement Redis caching for read-heavy operations
- Use connection pooling for database
- Compress WebSocket messages for large payloads
- Implement pagination for large result sets

---

**🎯 This comprehensive API documentation covers everything you need to understand, use, and deploy the Yupcha Customer Bot AI system!**

**📞 For support or questions, contact the development team or check the GitHub repository.**